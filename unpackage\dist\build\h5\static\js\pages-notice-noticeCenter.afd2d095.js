(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-notice-noticeCenter"],{1823:function(n,t,e){var i=e("24fb");t=i(!1),t.push([n.i,'@charset "UTF-8";\r\n/* uni.scss */\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 字体变量 */\r\n/* 颜色变量 */\r\n/* 行为相关颜色 */\r\n/* 文字基本颜色 */\r\n/* 背景颜色 */\r\n/* 边框颜色 */\r\n/* 尺寸变量 */\r\n/* 文字尺寸 */\r\n/* 图片尺寸 */\r\n/* Border Radius */\r\n/* 水平间距 */\r\n/* 垂直间距 */\r\n/* 透明度 */\r\n/* 文章场景相关 */.notice-center[data-v-53272266]{background-color:#fff;border-radius:%?10?%}.notice-center .each-line[data-v-53272266]{display:flex;align-items:center;justify-content:space-between;border-bottom:1px solid #f7f7f7;padding:%?24?%}.notice-center .each-line .label[data-v-53272266]{display:flex;align-items:center}.notice-center .each-line .label .icon[data-v-53272266]{width:24px;height:24px;margin-right:10px}',""]),n.exports=t},"1dff":function(n,t,e){"use strict";e.r(t);var i=e("ecff"),a=e("de57");for(var r in a)["default"].indexOf(r)<0&&function(n){e.d(t,n,(function(){return a[n]}))}(r);e("6f12"),e("2fe3");var c=e("f0c5"),o=Object(c["a"])(a["default"],i["b"],i["c"],!1,null,"53272266",null,!1,i["a"],void 0);t["default"]=o.exports},"22ec":function(n,t,e){var i=e("24fb");t=i(!1),t.push([n.i,"uni-page-body[data-v-53272266]{background-color:#f7f7f7}body.?%PAGE?%[data-v-53272266]{background-color:#f7f7f7}",""]),n.exports=t},"2fe3":function(n,t,e){"use strict";var i=e("3b1b"),a=e.n(i);a.a},"3b1b":function(n,t,e){var i=e("1823");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[n.i,i,""]]),i.locals&&(n.exports=i.locals);var a=e("4f06").default;a("ea867434",i,!0,{sourceMap:!1,shadowMode:!1})},"6f12":function(n,t,e){"use strict";var i=e("dd16"),a=e.n(i);a.a},aadb:function(n,t,e){"use strict";e("7a82"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i={name:"noticeCenter",data:function(){return{}},methods:{toNoticesList:function(n){uni.navigateTo({url:"/pages/notice/noticesList?type="+n})}}};t.default=i},dd16:function(n,t,e){var i=e("22ec");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[n.i,i,""]]),i.locals&&(n.exports=i.locals);var a=e("4f06").default;a("2300291d",i,!0,{sourceMap:!1,shadowMode:!1})},de57:function(n,t,e){"use strict";e.r(t);var i=e("aadb"),a=e.n(i);for(var r in i)["default"].indexOf(r)<0&&function(n){e.d(t,n,(function(){return i[n]}))}(r);t["default"]=a.a},ecff:function(n,t,e){"use strict";e.d(t,"b",(function(){return i})),e.d(t,"c",(function(){return a})),e.d(t,"a",(function(){}));var i=function(){var n=this,t=n.$createElement,e=n._self._c||t;return e("v-uni-view",{staticStyle:{padding:"24rpx"}},[e("v-uni-view",{staticClass:"notice-center"},[e("v-uni-view",{staticClass:"each-line",on:{click:function(t){arguments[0]=t=n.$handleEvent(t),n.toNoticesList("官方公告")}}},[e("v-uni-view",{staticClass:"label"},[e("v-uni-text",[n._v("官方公告")])],1),e("uni-icons",{attrs:{type:"right",size:"16"}})],1),e("v-uni-view",{staticClass:"each-line",on:{click:function(t){arguments[0]=t=n.$handleEvent(t),n.toNoticesList("系统消息")}}},[e("v-uni-view",{staticClass:"label"},[e("v-uni-text",[n._v("系统消息")])],1),e("uni-icons",{attrs:{type:"right",size:"16"}})],1)],1)],1)},a=[]}}]);