<template>
	<view class="page">
		
		<view class="form-container">
			<!-- 订单信息展示 -->
			<view class="order-info">
				<view class="order-title">订单信息</view>
				<view class="order-item">
					<text class="label">订单编号：</text>
					<text class="value">{{orderInfo.orderNo}}</text>
				</view>
				<view class="order-item">
					<text class="label">商品名称：</text>
					<text class="value">{{orderInfo.goodsName}}</text>
				</view>
				<view class="order-item">
					<text class="label">订单金额：</text>
					<text class="value">¥{{orderInfo.totalAmt}}</text>
				</view>
			</view>

			<!-- 发票信息表单 -->
			<view class="invoice-form">
				<view class="form-title">发票信息</view>
				
				<!-- 发票信息 -->
				<view class="form-section">
					<view class="section-title">发票信息</view>

					<view class="form-item">
						<view class="item-label required">发票类型</view>
						<u-radio-group v-model="invoiceForm.infoKind" placement="row">
							<u-radio
								:customStyle="{marginRight: '32rpx'}"
								label="普通发票"
								name="12"
								activeColor="#BBA186"
							></u-radio>
							<u-radio
								label="增值税专用发票"
								name="11"
								activeColor="#BBA186"
							></u-radio>
						</u-radio-group>
					</view>

					<view class="form-item">
						<view class="item-label required">开票对象</view>
						<u-radio-group v-model="invoiceForm.headerType" placement="row">
							<u-radio
								:customStyle="{marginRight: '32rpx'}"
								label="个人"
								name="1"
								activeColor="#BBA186"
							></u-radio>
							<u-radio
								label="企业"
								name="0"
								activeColor="#BBA186"
							></u-radio>
						</u-radio-group>
					</view>

					<view class="form-item">
						<view class="item-label required">购方名称</view>
						<u-input
							v-model="invoiceForm.clientName"
							placeholder="请填写购方名称"
							:clearable="true"
						></u-input>
					</view>

					<view class="form-item" v-if="invoiceForm.headerType === '0'">
						<view class="item-label required">购方税号</view>
						<u-input
							v-model="invoiceForm.clientTaxNo"
							placeholder="请填写购方税号"
							:clearable="true"
						></u-input>
					</view>
				</view>

			</view>
		</view>
		
		<!-- 底部按钮 -->
		<view class="footer-buttons">
			<button class="btn reset-btn" @click="resetForm">重置</button>
			<button class="btn submit-btn" @click="submitInvoice" :disabled="submitting">
				{{submitting ? '提交中...' : '提交申请'}}
			</button>
		</view>
	</view>
</template>

<script>
	export default {
		name: 'invoice',
		data() {
			return {
				orderInfo: {
					orderNo: '',
					goodsName: '',
					totalAmt: ''
				},
				invoiceForm: {
					orderNo: '',        // 订单号
					infoKind: '12',     // 发票类型：11-增值税专用发票，12-普通发票
					headerType: '1',    // 开票对象：0-企业，1-个人
					clientName: '',     // 购方名称
					clientTaxNo: ''     // 购方税号
				},
				submitting: false
			}
		},
		onLoad(options) {
			// 获取传递的订单信息
			if (options.orderNo) {
				this.orderInfo.orderNo = options.orderNo
				this.invoiceForm.orderNo = options.orderNo // 设置表单中的订单号
			}
			if (options.totalAmt) {
				this.orderInfo.totalAmt = options.totalAmt
			}
			if (options.goodsName) {
				this.orderInfo.goodsName = decodeURIComponent(options.goodsName)
			}
		},
		methods: {
			// 验证表单
			validateForm() {
				if (!this.invoiceForm.clientName) {
					uni.showToast({
						title: '请填写购方名称',
						icon: 'none'
					})
					return false
				}

				// 如果是企业开票，需要填写税号
				if (this.invoiceForm.headerType === '0' && !this.invoiceForm.clientTaxNo) {
					uni.showToast({
						title: '企业开票需要填写购方税号',
						icon: 'none'
					})
					return false
				}

				return true
			},
			
			// 提交发票申请
			submitInvoice() {
				if (!this.validateForm()) {
					return
				}

				this.submitting = true

				// 调用开具发票接口
				const util = require('../../utils/util')
				const api = require('../../config/api')

				const requestData = {
					orderNo: this.invoiceForm.orderNo,
					infoKind: this.invoiceForm.infoKind,
					headerType: this.invoiceForm.headerType,
					clientName: this.invoiceForm.clientName,
					clientTaxNo: this.invoiceForm.clientTaxNo
				}

				util.request('/app/goodsOrder/makeOut', requestData, 'POST').then((res) => {
					this.submitting = false
					if (res.code === 0) {
						uni.showToast({
							title: '发票申请已提交',
							icon: 'success'
						})

						setTimeout(() => {
							uni.navigateBack()
						}, 1500)
					} else {
						uni.showToast({
							title: res.message || '提交失败，请重试',
							icon: 'none'
						})
					}
				}).catch((error) => {
					this.submitting = false
					uni.showToast({
						title: '网络错误，请重试',
						icon: 'none'
					})
				})
			},
			
			// 重置表单
			resetForm() {
				this.invoiceForm = {
					orderNo: this.orderInfo.orderNo, // 保持订单号不变
					infoKind: '12',     // 重置为普通发票
					headerType: '1',    // 重置为个人
					clientName: '',     // 清空购方名称
					clientTaxNo: ''     // 清空购方税号
				}
			}
		}
	}
</script>

<style lang="scss">
	page {
		background-color: #F7F7F7;
	}
	
	.page {
		min-height: 100vh;
	}
	
	.form-container {
		padding: 24rpx;
	}
	
	.order-info {
		background-color: #FFFFFF;
		border-radius: 12rpx;
		padding: 24rpx;
		margin-bottom: 24rpx;
		
		.order-title {
			font-size: 32rpx;
			font-weight: 600;
			color: #171B25;
			margin-bottom: 20rpx;
		}
		
		.order-item {
			display: flex;
			margin-bottom: 12rpx;
			
			.label {
				color: #61687C;
				font-size: 28rpx;
				width: 160rpx;
			}
			
			.value {
				color: #171B25;
				font-size: 28rpx;
				flex: 1;
			}
		}
	}
	
	.invoice-form {
		background-color: #FFFFFF;
		border-radius: 12rpx;
		padding: 24rpx;
		
		.form-title {
			font-size: 32rpx;
			font-weight: 600;
			color: #171B25;
			margin-bottom: 24rpx;
		}
		
		.form-section {
			margin-bottom: 32rpx;
			
			.section-title {
				font-size: 28rpx;
				color: #171B25;
				margin-bottom: 20rpx;
				padding-bottom: 12rpx;
				border-bottom: 1px solid #F2F4F7;
			}
		}
		
		.form-item {
			margin-bottom: 24rpx;
			display: flex;
			align-items: center;

			.item-label {
				font-size: 28rpx;
				color: #171B25;
				width: 160rpx;
				flex-shrink: 0;

				&.required::after {
					content: '*';
					color: #FF0046;
					margin-left: 4rpx;
				}
			}
		}
	}
	
	.footer-buttons {
		background-color: #FFFFFF;
		padding: 24rpx;
		display: flex;
		gap: 24rpx;
		margin-top: 24rpx;
		
		.btn {
			flex: 1;
			height: 84rpx;
			border-radius: 42rpx;
			font-size: 28rpx;
			border: none;
			line-height: 84rpx;
			
			&.reset-btn {
				background-color: #F2F4F7;
				color: #61687C;
			}
			
			&.submit-btn {
				background-color: #BBA186;
				color: #FFFFFF;
				
				&:disabled {
					background-color: #D0D0D0;
					color: #999999;
				}
			}
		}
	}
</style>
