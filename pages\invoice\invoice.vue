<template>
	<view class="page">
		
		<view class="form-container">
			<!-- 订单信息展示 -->
			<view class="order-info">
				<view class="order-title">订单信息</view>
				<view class="order-item">
					<text class="label">订单编号：</text>
					<text class="value">{{orderInfo.orderNo}}</text>
				</view>
				<view class="order-item">
					<text class="label">商品名称：</text>
					<text class="value">{{orderInfo.goodsName}}</text>
				</view>
				<view class="order-item">
					<text class="label">订单金额：</text>
					<text class="value">¥{{orderInfo.totalAmt}}</text>
				</view>
			</view>

			<!-- 发票信息表单 -->
			<view class="invoice-form">
				<view class="form-title">发票信息</view>
				
				<!-- 必填项 -->
				<view class="form-section">
					<view class="section-title">必填信息</view>
					
					<view class="form-item">
						<view class="item-label required">购方名称</view>
						<u-input 
							v-model="invoiceForm.gfnsrmc" 
							placeholder="请填写购方名称"
							:clearable="true"
						></u-input>
					</view>
					
					<view class="form-item">
						<view class="item-label required">购方税号</view>
						<u-input 
							v-model="invoiceForm.gfnsrsbh" 
							placeholder="请填写购方税号"
							:clearable="true"
						></u-input>
					</view>
					
					<view class="form-item">
						<view class="item-label required">收票电话</view>
						<u-input 
							v-model="invoiceForm.phone" 
							placeholder="请填写收票电话"
							:clearable="true"
						></u-input>
					</view>
					
					<view class="form-item">
						<view class="item-label required">收票邮箱</view>
						<u-input 
							v-model="invoiceForm.email" 
							placeholder="请填写收票邮箱"
							:clearable="true"
						></u-input>
					</view>
					
					<view class="form-item">
						<view class="item-label">是否自然人</view>
						<u-switch 
							v-model="invoiceForm.isNaturalPerson" 
							active-color="#BBA186"
							inactive-color="#dcdfe6"
						></u-switch>
					</view>
				</view>
				
				<!-- 非必填项 -->
				<view class="form-section">
					<view class="section-title">选填信息</view>
					
					<view class="form-item">
						<view class="item-label">购方地址</view>
						<u-input 
							v-model="invoiceForm.address" 
							placeholder="请填写购方地址"
							:clearable="true"
						></u-input>
					</view>
					
					<view class="form-item">
						<view class="item-label">购方电话</view>
						<u-input 
							v-model="invoiceForm.tel" 
							placeholder="请填写购方电话"
							:clearable="true"
						></u-input>
					</view>
					
					<view class="form-item">
						<view class="item-label">银行名称</view>
						<u-input 
							v-model="invoiceForm.bankName" 
							placeholder="请填写开户行"
							:clearable="true"
						></u-input>
					</view>
					
					<view class="form-item">
						<view class="item-label">银行账号</view>
						<u-input 
							v-model="invoiceForm.bankNumber" 
							placeholder="请填写银行账号"
							:clearable="true"
						></u-input>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 底部按钮 -->
		<view class="footer-buttons">
			<button class="btn reset-btn" @click="resetForm">重置</button>
			<button class="btn submit-btn" @click="submitInvoice" :disabled="submitting">
				{{submitting ? '提交中...' : '提交申请'}}
			</button>
		</view>
	</view>
</template>

<script>
	export default {
		name: 'invoice',
		data() {
			return {
				orderInfo: {
					orderNo: '',
					goodsName: '',
					totalAmt: ''
				},
				invoiceForm: {
					gfnsrmc: '',        // 购方名称
					gfnsrsbh: '',       // 购方税号
					phone: '',          // 收票电话
					email: '',          // 收票邮箱
					isNaturalPerson: false, // 是否自然人
					address: '',        // 购方地址
					tel: '',           // 购方电话
					bankName: '',      // 银行名称
					bankNumber: ''     // 银行账号
				},
				submitting: false
			}
		},
		onLoad(options) {
			// 获取传递的订单信息
			if (options.orderNo) {
				this.orderInfo.orderNo = options.orderNo
			}
			if (options.totalAmt) {
				this.orderInfo.totalAmt = options.totalAmt
			}
			if (options.goodsName) {
				this.orderInfo.goodsName = decodeURIComponent(options.goodsName)
			}
		},
		methods: {
			// 验证表单
			validateForm() {
				if (!this.invoiceForm.gfnsrmc) {
					uni.showToast({
						title: '请填写购方名称',
						icon: 'none'
					})
					return false
				}
				
				if (!this.invoiceForm.gfnsrsbh) {
					uni.showToast({
						title: '请填写购方税号',
						icon: 'none'
					})
					return false
				}
				
				if (!this.invoiceForm.phone) {
					uni.showToast({
						title: '请填写收票电话',
						icon: 'none'
					})
					return false
				}
				
				// 验证手机号格式
				const phoneReg = /^1[3-9]\d{9}$/
				if (!phoneReg.test(this.invoiceForm.phone)) {
					uni.showToast({
						title: '请填写正确的手机号',
						icon: 'none'
					})
					return false
				}
				
				// 验证邮箱格式（如果填写了邮箱）
				if (this.invoiceForm.email) {
					const emailReg = /^([a-zA-Z0-9_-])+@([a-zA-Z0-9_-])+(\.[a-zA-Z0-9_-])+/
					if (!emailReg.test(this.invoiceForm.email)) {
						uni.showToast({
							title: '请填写正确的邮箱格式',
							icon: 'none'
						})
						return false
					}
				}
				
				return true
			},
			
			// 提交发票申请
			submitInvoice() {
				if (!this.validateForm()) {
					return
				}
				
				this.submitting = true
				
				// TODO: 这里后续接入真实的API接口
				// 模拟提交过程
				setTimeout(() => {
					this.submitting = false
					uni.showToast({
						title: '发票申请已提交',
						icon: 'success'
					})
					
					setTimeout(() => {
						uni.navigateBack()
					}, 1500)
				}, 2000)
			},
			
			// 重置表单
			resetForm() {
				this.invoiceForm = {
					gfnsrmc: '',
					gfnsrsbh: '',
					phone: '',
					email: '',
					isNaturalPerson: false,
					address: '',
					tel: '',
					bankName: '',
					bankNumber: ''
				}
			}
		}
	}
</script>

<style lang="scss">
	page {
		background-color: #F7F7F7;
	}
	
	.page {
		min-height: 100vh;
		padding-bottom: 120rpx;
	}
	
	.form-container {
		padding: 24rpx;
	}
	
	.order-info {
		background-color: #FFFFFF;
		border-radius: 12rpx;
		padding: 24rpx;
		margin-bottom: 24rpx;
		
		.order-title {
			font-size: 32rpx;
			font-weight: 600;
			color: #171B25;
			margin-bottom: 20rpx;
		}
		
		.order-item {
			display: flex;
			margin-bottom: 12rpx;
			
			.label {
				color: #61687C;
				font-size: 28rpx;
				width: 160rpx;
			}
			
			.value {
				color: #171B25;
				font-size: 28rpx;
				flex: 1;
			}
		}
	}
	
	.invoice-form {
		background-color: #FFFFFF;
		border-radius: 12rpx;
		padding: 24rpx;
		
		.form-title {
			font-size: 32rpx;
			font-weight: 600;
			color: #171B25;
			margin-bottom: 24rpx;
		}
		
		.form-section {
			margin-bottom: 32rpx;
			
			.section-title {
				font-size: 28rpx;
				color: #171B25;
				margin-bottom: 20rpx;
				padding-bottom: 12rpx;
				border-bottom: 1px solid #F2F4F7;
			}
		}
		
		.form-item {
			margin-bottom: 24rpx;
			
			.item-label {
				font-size: 28rpx;
				color: #171B25;
				margin-bottom: 12rpx;
				
				&.required::after {
					content: '*';
					color: #FF0046;
					margin-left: 4rpx;
				}
			}
		}
	}
	
	.footer-buttons {
		position: fixed;
		bottom: 0;
		left: 0;
		right: 0;
		background-color: #FFFFFF;
		padding: 24rpx;
		display: flex;
		gap: 24rpx;
		box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.1);
		
		.btn {
			flex: 1;
			height: 84rpx;
			border-radius: 42rpx;
			font-size: 28rpx;
			border: none;
			l
			
			&.reset-btn {
				background-color: #F2F4F7;
				color: #61687C;
			}
			
			&.submit-btn {
				background-color: #BBA186;
				color: #FFFFFF;
				
				&:disabled {
					background-color: #D0D0D0;
					color: #999999;
				}
			}
		}
	}
</style>
