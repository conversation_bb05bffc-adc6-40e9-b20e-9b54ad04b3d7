{"version": 3, "sources": ["uni-app:///main.js", "webpack:///E:/Home/ma-Yi/gold/pages/ForgotPassword/ForgotPassword.vue?1636", "webpack:///E:/Home/ma-Yi/gold/pages/ForgotPassword/ForgotPassword.vue?50d1", "webpack:///E:/Home/ma-Yi/gold/pages/ForgotPassword/ForgotPassword.vue?1f7f", "webpack:///E:/Home/ma-Yi/gold/pages/ForgotPassword/ForgotPassword.vue?4fbf", "uni-app:///pages/ForgotPassword/ForgotPassword.vue", "webpack:///E:/Home/ma-Yi/gold/pages/ForgotPassword/ForgotPassword.vue?d049", "webpack:///E:/Home/ma-Yi/gold/pages/ForgotPassword/ForgotPassword.vue?f4fd"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "checked", "srcs", "countdown", "disabled", "phone", "imgCode", "phonecode", "passwd", "DeviceID", "onLoad", "onShow", "methods", "resetPassword", "showErrorToast", "uni", "title", "icon", "util", "api", "res", "setTimeout", "url", "refreshsrc", "randomString", "generateRandomString", "result", "getPhonCode", "timer", "clearInterval"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,uBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAuI;AACvI;AACkE;AACL;AACsC;;;AAGnG;AACqM;AACrM,gBAAgB,8MAAU;AAC1B,EAAE,oFAAM;AACR,EAAE,qGAAM;AACR,EAAE,8GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,yGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAwwB,CAAgB,6xBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC2C5xB;AACA;AAAA,eACA;EACAC;IACA;MACAC;MACAC;MACA;MACAC;MACA;MACAC;MACA;MACA;MACAC;MACA;MACAC;MACA;MACAC;MACA;MACAC;MACA;MACA;MACAC;IAEA;EACA;EACA;AACA;AACA;EACAC;IACA;EACA;EACAC;IACA;EACA;EAEAC;IAEAC;MAAA;MAAA;QAAA,IACAC;QAAA;UAAA;YAAA;cAAA;gBAAAA;kBACAC;oBACAC;oBACAC;kBACA;gBACA;gBAAA,IACA;kBAAA;kBAAA;gBAAA;gBACAH;gBAAA;cAAA;gBAAA,IAGA;kBAAA;kBAAA;gBAAA;gBACAA;gBAAA;cAAA;gBAAA,IAGA;kBAAA;kBAAA;gBAAA;gBACAA;gBAAA;cAAA;gBAAA,IAGA;kBAAA;kBAAA;gBAAA;gBACAA;gBAAA;cAAA;gBAAA,IAGA;kBAAA;kBAAA;gBAAA;gBACAA;gBAAA;cAAA;gBAAA;gBAAA,OAIAI,aACAC;kBACA;kBACA;kBACA;kBACA;gBACA,GACA,OACA;cAAA;gBARAC;gBASA;gBACA;kBACAL;oBACAC;oBACAC;kBACA;gBACA;kBACAF;oBACAC;oBACAC;kBACA;kBACAI;oBACAN;sBACAO;oBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAC;gBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;IACAC;MACA;MACA;MACA;QACA;QACAC;MACA;MACA;IACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,IACA;kBAAA;kBAAA;gBAAA;gBACAZ;kBACAC;kBACAC;gBACA;gBAAA;cAAA;gBAAA,IAGA;kBAAA;kBAAA;gBAAA;gBACAF;kBACAC;kBACAC;gBACA;gBAAA;cAAA;gBAAA;gBAAA,OAGAC,aACAC;kBACA;kBACA;kBACA;kBACA;gBACA,GACA,OACA;cAAA;gBARAC;gBASA;gBACA;kBACAL;oBACAC;oBACAC;kBACA;gBACA;kBACAF;oBACAC;oBACAC;kBACA;kBAEAd;kBAEA;kBAEAyB;oBACAzB;oBAGA;oBACA;oBACA;sBACA0B;sBACA;sBACA,uBACA;oBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AClNA;AAAA;AAAA;AAAA;AAA28C,CAAgB,w6CAAG,EAAC,C;;;;;;;;;;;ACA/9C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/ForgotPassword/ForgotPassword.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/ForgotPassword/ForgotPassword.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./ForgotPassword.vue?vue&type=template&id=f8f415f4&scoped=true&\"\nvar renderjs\nimport script from \"./ForgotPassword.vue?vue&type=script&lang=js&\"\nexport * from \"./ForgotPassword.vue?vue&type=script&lang=js&\"\nimport style0 from \"./ForgotPassword.vue?vue&type=style&index=0&id=f8f415f4&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\HBuilderX.3.3.13.********.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"f8f415f4\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/ForgotPassword/ForgotPassword.vue\"\nexport default component.exports", "export * from \"-!D:\\\\HBuilderX.3.3.13.********.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\HBuilderX.3.3.13.********.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\HBuilderX.3.3.13.********.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\HBuilderX.3.3.13.********.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\HBuilderX.3.3.13.********.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX.3.3.13.********.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./ForgotPassword.vue?vue&type=template&id=f8f415f4&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\HBuilderX.3.3.13.********.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX.3.3.13.********.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX.3.3.13.********.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX.3.3.13.********.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX.3.3.13.********.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./ForgotPassword.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\HBuilderX.3.3.13.********.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX.3.3.13.********.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX.3.3.13.********.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX.3.3.13.********.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX.3.3.13.********.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./ForgotPassword.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"page\">\r\n\t\t<view class=\"page-cencont\">\r\n\t\t\t<view class=\"item\">\r\n\t\t\t\t<view class=\"account\" style=\"position: relative\">\r\n\t\t\t\t\t<input type=\"text\" data-type=\"imgCode\" placeholder-class=\"inp-palcehoder\" v-model=\"imgCode\"\r\n\t\t\t\t\t\tplaceholder=\"请输入图片验证码\" />\r\n\r\n\t\t\t\t\t<image @click=\"refreshsrc\" class=\"codeimg\" :src=\"srcs\" mode=\"\" />\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"item\">\r\n\t\t\t\t<view class=\"account\" style=\"position: relative\">\r\n\t\t\t\t\t<input type=\"text\" data-type=\"imgCode\" placeholder-class=\"inp-palcehoder\" v-model=\"phone\"\r\n\t\t\t\t\t\tplaceholder=\"请输入注册时的手机号\" />\r\n\r\n\t\t\t\t\t<button v-if=\"!disabled\" size=\"mini\" class=\"phonecode\" @click=\"getPhonCode\">获取验证码</button>\r\n\t\t\t\t\t<button v-if=\"disabled\" class=\"phonecode2\">{{ countdown }}秒后重新获取</button>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"item\">\r\n\t\t\t\t<view class=\"account\" style=\"position: relative\">\r\n\t\t\t\t\t<input type=\"text\" data-type=\"imgCode\" placeholder-class=\"inp-palcehoder\" v-model=\"phonecode\"\r\n\t\t\t\t\t\tplaceholder=\"请输入验证码\" />\r\n\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"item\">\r\n\t\t\t\t<view class=\"account\" style=\"position: relative\">\r\n\t\t\t\t\t<input type=\"text\" data-type=\"imgCode\" placeholder-class=\"inp-palcehoder\" v-model=\"passwd\"\r\n\t\t\t\t\t\tplaceholder=\"请输入密码\" />\r\n\r\n\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"confimr\" @click=\"resetPassword\">\r\n\t\t\t\t提交\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\tconst api = require('../../config/api');\r\n\tconst util = require('../../utils/util');\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tchecked: false,\r\n\t\t\t\tsrcs: '',\r\n\t\t\t\t//图片验证码src\r\n\t\t\t\tcountdown: 60,\r\n\t\t\t\t// 初始倒计时时间，单位：秒\r\n\t\t\t\tdisabled: false,\r\n\t\t\t\t// 按钮是否禁用\r\n\t\t\t\t// 表单数据\r\n\t\t\t\tphone: '',\r\n\t\t\t\t//手机号\r\n\t\t\t\timgCode: '',\r\n\t\t\t\t//图片验证码\r\n\t\t\t\tphonecode: '',\r\n\t\t\t\t//手机验证码\r\n\t\t\t\tpasswd: '',\r\n\t\t\t\t//密码\r\n\t\t\t\t//设备ID\r\n\t\t\t\tDeviceID: '',\r\n\r\n\t\t\t};\r\n\t\t},\r\n\t\t/**\r\n\t\t * 生命周期函数--监听页面加载\r\n\t\t */\r\n\t\tonLoad(options) {\r\n\t\t\tthis.refreshsrc();\r\n\t\t},\r\n\t\tonShow() {\r\n\t\t\tthis.refreshsrc();\r\n\t\t},\r\n\r\n\t\tmethods: {\r\n\r\n\t\t\tasync resetPassword() {\r\n\t\t\t\tfunction showErrorToast(message) {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: message,\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t\tif (!this.phone.trim()) {\r\n\t\t\t\t\tshowErrorToast('请输入手机号');\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tif (!/^[0-9]{11}$/.test(this.phone.trim())) {\r\n\t\t\t\t\tshowErrorToast('手机号格式不对');\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tif (!this.imgCode.trim()) {\r\n\t\t\t\t\tshowErrorToast('请输入图形码');\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tif (!this.phonecode.trim()) {\r\n\t\t\t\t\tshowErrorToast('请输入验证码');\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tif (!this.passwd.trim()) {\r\n\t\t\t\t\tshowErrorToast('请输入密码');\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\r\n\t\t\t\tconst res = await util.request(\r\n\t\t\t\t\tapi.resetPasswordUrl, {\r\n\t\t\t\t\t\t\"code\": this.phonecode,\r\n\t\t\t\t\t\t\"passwd\": this.passwd,\r\n\t\t\t\t\t\t\"phone\": this.phone,\r\n\t\t\t\t\t\t\"tenantId\": 3\r\n\t\t\t\t\t},\r\n\t\t\t\t\t'POST'\r\n\t\t\t\t);\r\n\t\t\t\t// console.log(res)\r\n\t\t\t\tif (res.code != 0) {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: res.msg,\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t})\r\n\t\t\t\t} else{\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '重置密码成功',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t})\r\n\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\tuni.reLaunch({\r\n\t\t\t\t\t\t\turl: '/pages/login/login'\r\n\t\t\t\t\t\t}, 1000);\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t},\r\n\r\n\t\t\t// 获取图片验证码\r\n\t\t\tasync refreshsrc() {\r\n\t\t\t\tconst randomString = this.generateRandomString(6);\r\n\t\t\t\tthis.DeviceID = randomString\r\n\t\t\t\tthis.srcs = api.ImgCodeUrl + \"?DeviceID=\" + this.DeviceID;\r\n\t\t\t},\r\n\r\n\t\t\t// 生成字符串\r\n\t\t\tgenerateRandomString(length) {\r\n\t\t\t\tconst characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';\r\n\t\t\t\tlet result = '';\r\n\t\t\t\tfor (let i = 0; i < length; i++) {\r\n\t\t\t\t\tconst randomIndex = Math.floor(Math.random() * characters.length);\r\n\t\t\t\t\tresult += characters.charAt(randomIndex);\r\n\t\t\t\t}\r\n\t\t\t\treturn result;\r\n\t\t\t},\r\n\t\t\tasync getPhonCode() {\r\n\t\t\t\tif (!this.imgCode.trim()) {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '请输入图形码',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t});\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tif (!this.phone.trim()) {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '手机号不能为空',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t});\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tconst res = await util.request(\r\n\t\t\t\t\tapi.PhoneCodeUrl, {\r\n\t\t\t\t\t\t\"deviceID\": this.DeviceID,\r\n\t\t\t\t\t\t\"imgCode\": this.imgCode,\r\n\t\t\t\t\t\t\"phone\": this.phone,\r\n\t\t\t\t\t\t\"tenantId\": 3\r\n\t\t\t\t\t},\r\n\t\t\t\t\t'POST'\r\n\t\t\t\t);\r\n\t\t\t\t// console.log(res);\r\n\t\t\t\tif (res.code !== 0) {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: res.msg,\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t});\r\n\t\t\t\t} else {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '发送成功',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t});\r\n\r\n\t\t\t\t\tlet countdown = this.countdown;\r\n\r\n\t\t\t\t\tthis.disabled = true\r\n\r\n\t\t\t\t\tlet timer = setInterval(() => {\r\n\t\t\t\t\t\tcountdown--;\r\n\r\n\r\n\t\t\t\t\t\tthis.countdown = countdown\r\n\t\t\t\t\t\t// 倒计时结束\r\n\t\t\t\t\t\tif (countdown <= 0) {\r\n\t\t\t\t\t\t\tclearInterval(timer);\r\n\t\t\t\t\t\t\t// 恢复按钮可点击状态\r\n\t\t\t\t\t\t\tthis.countdown = 60,\r\n\t\t\t\t\t\t\t\tthis.disabled = false\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}, 1000);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n\t.page {\r\n\t\tpadding: 1px;\r\n\t}\r\n\r\n\t.page-cencont {\r\n\t\tmargin-top: 50px;\r\n\t\tmargin-left: 25px;\r\n\t\tmargin-right: 25px;\r\n\t}\r\n\r\n\t.item {\r\n\t\theight: 45px;\r\n\t\tmargin-top: 15px;\r\n\r\n\t}\r\n\r\n\t.account {\r\n\t\tdisplay: flex;\r\n\t\tborder-bottom: 1px solid #F4F4F4;\r\n\t\tpadding: 0px 15px;\r\n\t\tbox-sizing: border-box;\r\n\t\tfont-size: 14px;\r\n\t\talign-items: center;\r\n\t}\r\n\r\n\t.account input {\r\n\t\t// padding-left: 20rpx;\r\n\t\t// width: 80%;\r\n\t\tflex: 1;\r\n\t}\r\n\r\n\t/* 图片验证码 */\r\n\t.codeimg {\r\n\t\twidth: 75px;\r\n\t\theight: 40px;\r\n\t}\r\n\r\n\t.inp-palcehoder {\r\n\t\tfont-size: 26rpx;\r\n\t}\r\n\r\n\t/* 短信验证码 */\r\n\t.phonecode {\r\n\t\theight: 35px;\r\n\t\tborder: 1px solid #6699F2;\r\n\t\tpadding: 0px 10px;\r\n\t\ttext-align: center;\r\n\t\tline-height: 35px;\r\n\t\tcolor: #6699F2;\r\n\t\tfont-size: 15px;\r\n\t}\r\n\r\n\t.phonecode2 {\r\n\t\theight: 35px;\r\n\t\tborder: 1px solid #6699F2;\r\n\t\tpadding: 0px 10px;\r\n\t\ttext-align: center;\r\n\t\tline-height: 35px;\r\n\t\tcolor: #767676;\r\n\t\tfont-size: 15px;\r\n\t}\r\n\r\n\t.confimr {\r\n\t\theight: 45px;\r\n\t\tfont-size: 20px;\r\n\t\tmargin-top: 35px;\r\n\t\tbackground-color: #6A9FFB;\r\n\t\ttext-align: center;\r\n\t\tcolor: #fff;\r\n\t\tline-height: 45px;\r\n\t\tborder-radius: 30px;\r\n\t}\r\n</style>", "import mod from \"-!D:\\\\HBuilderX.3.3.13.********.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX.3.3.13.********.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX.3.3.13.********.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX.3.3.13.********.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX.3.3.13.********.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX.3.3.13.********.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX.3.3.13.********.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX.3.3.13.********.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX.3.3.13.********.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./ForgotPassword.vue?vue&type=style&index=0&id=f8f415f4&lang=scss&scoped=true&\"; export default mod; export * from \"-!D:\\\\HBuilderX.3.3.13.********.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX.3.3.13.********.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX.3.3.13.********.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX.3.3.13.********.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX.3.3.13.********.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX.3.3.13.********.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX.3.3.13.********.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX.3.3.13.********.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX.3.3.13.********.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./ForgotPassword.vue?vue&type=style&index=0&id=f8f415f4&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1752722993375\n      var cssReload = require(\"D:/HBuilderX.3.3.13.********.full/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"../../\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}