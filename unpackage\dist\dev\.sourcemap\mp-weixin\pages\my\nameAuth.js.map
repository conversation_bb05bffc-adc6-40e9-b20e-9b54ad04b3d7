{"version": 3, "sources": ["uni-app:///main.js", "webpack:///E:/pos/黄金/gold_client/pages/my/nameAuth.vue?407f", "webpack:///E:/pos/黄金/gold_client/pages/my/nameAuth.vue?5bfb", "webpack:///E:/pos/黄金/gold_client/pages/my/nameAuth.vue?a2dc", "webpack:///E:/pos/黄金/gold_client/pages/my/nameAuth.vue?08a8", "uni-app:///pages/my/nameAuth.vue", "webpack:///E:/pos/黄金/gold_client/pages/my/nameAuth.vue?73a2", "webpack:///E:/pos/黄金/gold_client/pages/my/nameAuth.vue?1262"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "name", "data", "authInfo", "realName", "idCard", "phone", "onLoad", "methods", "getUserInfo", "util", "res", "console", "uni", "title", "icon", "url", "submitEvent"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,iBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAiI;AACjI;AAC4D;AACL;AACsC;;;AAG7F;AACgL;AAChL,gBAAgB,yLAAU;AAC1B,EAAE,8EAAM;AACR,EAAE,+FAAM;AACR,EAAE,wGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,mGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAypB,CAAgB,8qBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACmB7qB;AACA;AAAA,eACA;EACAC;EACAC;IACA;MACAC;QACAC;QACAC;QACAC;MACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,OACAC;cAAA;gBAAAC;gBACAC;gBACA;kBACAC;oBACAC;oBACAC;kBACA;gBACA;kBACA;oBACA;oBACA;oBACA;kBACA;oBACAF;sBACAG;oBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAC;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IAAA;EAEA;AACA;AAAA,2B;;;;;;;;;;;;;AC3EA;AAAA;AAAA;AAAA;AAAwwC,CAAgB,quCAAG,EAAC,C;;;;;;;;;;;ACA5xC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/my/nameAuth.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/my/nameAuth.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./nameAuth.vue?vue&type=template&id=b3d0ae26&scoped=true&\"\nvar renderjs\nimport script from \"./nameAuth.vue?vue&type=script&lang=js&\"\nexport * from \"./nameAuth.vue?vue&type=script&lang=js&\"\nimport style0 from \"./nameAuth.vue?vue&type=style&index=0&id=b3d0ae26&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"b3d0ae26\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/my/nameAuth.vue\"\nexport default component.exports", "export * from \"-!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./nameAuth.vue?vue&type=template&id=b3d0ae26&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./nameAuth.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./nameAuth.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"real-auth-page\">\r\n\t\t<view class=\"each-line\">\r\n\t\t\t<text class=\"label\">姓名</text>\r\n\t\t\t<input class=\"no-input\" :disabled=\"true\" v-model=\"authInfo.realName\" type=\"text\" placeholder=\"请输入姓名\" />\r\n\t\t</view>\r\n\t\t<view class=\"each-line\">\r\n\t\t\t<text class=\"label\">身份证号</text>\r\n\t\t\t<input class=\"no-input\" :disabled=\"true\" v-model=\"authInfo.idCard\" type=\"text\" placeholder=\"请输入身份证号\" />\r\n\t\t</view>\r\n\t\t<view class=\"each-line\">\r\n\t\t\t<text class=\"label\">手机号</text>\r\n\t\t\t<input class=\"no-input\" :disabled=\"true\" v-model=\"authInfo.phone\" type=\"text\" placeholder=\"请输入手机号\" />\r\n\t\t</view>\r\n\t\t<!-- <button class=\"submit-btn\" type=\"default\" @tap=\"submitEvent\">提交</button> -->\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\tconst api = require('../../config/api')\r\n\tconst util = require('../../utils/util')\r\n\texport default {\r\n\t\tname: 'realAuth',\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tauthInfo: {\r\n\t\t\t\t\trealName: '',\r\n\t\t\t\t\tidCard: '',\r\n\t\t\t\t\tphone: ''\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\t\tonLoad: function(options) {\r\n\t\t\tthis.getUserInfo()\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tasync getUserInfo() {\r\n\t\t\t\tconst res = await util.request(api.getUserInfoUrl, {}, 'POST')\r\n\t\t\t\tconsole.log(res)\r\n\t\t\t\tif (res.code !== 0) {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: res.msg,\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t})\r\n\t\t\t\t} else {\r\n\t\t\t\t\tif (res.data.realName) {\r\n\t\t\t\t\t\tthis.authInfo.realName = util.processName(res.data.realName.realName) \r\n\t\t\t\t\t\tthis.authInfo.idCard = util.phoneSubstringfct(res.data.realName.idCard) \r\n\t\t\t\t\t\tthis.authInfo.phone = util.phoneSubstring(res.data.realName.phone) \r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\t\t\turl: '/pages/my/myBankCard?from=nameAuth'\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tsubmitEvent() {\r\n\t\t\t\t// util.request(api.realNameUrl, this.authInfo, 'POST').then(res => {\r\n\t\t\t\t// \tconsole.log(res)\r\n\t\t\t\t// \tif (res.code !== 0) {\r\n\t\t\t\t// \t\tdebugger\r\n\t\t\t\t// \t\tuni.showToast({\r\n\t\t\t\t// \t\t\ttitle: res.msg,\r\n\t\t\t\t// \t\t\ticon: 'none'\r\n\t\t\t\t// \t\t})\r\n\t\t\t\t// \t} else {\r\n\t\t\t\t// \t\tdebugger\r\n\t\t\t\t// \t\tif (res.data.realName) {\r\n\t\t\t\t// \t\t\tthis.authInfo.realName = res.data.realName\r\n\t\t\t\t// \t\t\tthis.authInfo.idCard = res.data.realName\r\n\t\t\t\t// \t\t}\r\n\t\t\t\t// \t}\r\n\t\t\t\t// })\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n\t.real-auth-page {\r\n\t\t.each-line {\r\n\t\t\tdisplay: flex;\r\n\t\t\tjustify-content: normal;\r\n\t\t\tborder-bottom: 1px solid #e5e5e5;\r\n\t\t\tpadding: 20rpx 40rpx;\r\n\r\n\t\t\t.label {\r\n\t\t\t\twidth: 160rpx;\r\n\t\t\t}\r\n\r\n\t\t\t.no-input {\r\n\t\t\t\tpadding-left: 30rpx;\r\n\t\t\t\tflex-grow: 1;\r\n\t\t\t\tfont-size: 28rpx;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.idcard-image {\r\n\t\t\tdisplay: flex;\r\n\t\t\tjustify-content: space-around;\r\n\t\t\tmargin-top: 120rpx;\r\n\r\n\t\t\t.imgUpload {\r\n\t\t\t\twidth: 360rpx;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\tjustify-content: center;\r\n\t\t\t\ttext-align: center;\r\n\r\n\t\t\t\timage {\r\n\t\t\t\t\tborder: 1rpx solid #b6b4b4;\r\n\t\t\t\t\twidth: 340rpx;\r\n\t\t\t\t\theight: 228rpx;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.submit-btn {\r\n\t\t\twidth: 90%;\r\n\t\t\theight: 90rpx;\r\n\t\t\tline-height: 90rpx;\r\n\t\t\tcolor: #fff;\r\n\t\t\tfont-size: 32rpx;\r\n\t\t\tmargin-top: 40rpx;\r\n\t\t\tborder: 1px solid #007aff;\r\n\t\t\tbackground-color: #007aff;\r\n\t\t\tborder-radius: 20rpx;\r\n\t\t}\r\n\t}\r\n</style>", "import mod from \"-!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./nameAuth.vue?vue&type=style&index=0&id=b3d0ae26&lang=scss&scoped=true&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./nameAuth.vue?vue&type=style&index=0&id=b3d0ae26&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754273099686\n      var cssReload = require(\"D:/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}