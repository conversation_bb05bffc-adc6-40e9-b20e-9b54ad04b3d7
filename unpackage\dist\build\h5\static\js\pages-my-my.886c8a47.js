(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-my-my"],{"0de8":function(t,e,n){"use strict";var i=n("7af1"),a=n.n(i);a.a},"2b14":function(t,e,n){var i=n("24fb");e=i(!1),e.push([t.i,'@charset "UTF-8";\r\n/* uni.scss */\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 字体变量 */\r\n/* 颜色变量 */\r\n/* 行为相关颜色 */\r\n/* 文字基本颜色 */\r\n/* 背景颜色 */\r\n/* 边框颜色 */\r\n/* 尺寸变量 */\r\n/* 文字尺寸 */\r\n/* 图片尺寸 */\r\n/* Border Radius */\r\n/* 水平间距 */\r\n/* 垂直间距 */\r\n/* 透明度 */\r\n/* 文章场景相关 */.container[data-v-23887a24]{padding:%?24?%}.mh-person[data-v-23887a24]{display:flex;align-items:center}.mh-person .mh-p-img[data-v-23887a24]{width:%?100?%;height:%?100?%}.mh-person .mh-p-img.mh-p-img-bg[data-v-23887a24]{display:flex;align-items:center;justify-content:center;background-color:#fff}.mh-person .mh-p-img.mh-p-img-bg .iconfont[data-v-23887a24]{font-size:2.25rem;color:#999}.mh-person .mh-p-center[data-v-23887a24]{margin-left:%?20?%}.mh-person .mh-p-center .mh-pc-text1[data-v-23887a24]{font-size:%?36?%;font-weight:600;color:#171b25}.mh-person .mh-p-center .mh-pc-text2[data-v-23887a24]{margin-top:%?10?%;font-size:%?24?%;color:#61687c}.my_order[data-v-23887a24]{margin-top:%?24?%;background-color:#fff;border-radius:%?10?%;padding:%?30?% 0}.my_order .my_order_title[data-v-23887a24]{padding:0 %?32?%;display:flex;justify-content:space-between;align-items:center}.my_order .my_order_state[data-v-23887a24]{display:flex;margin-top:%?20?%}.my_order .my_order_state .my_order_list[data-v-23887a24]{padding-top:%?10?%;width:25%}.my-nav-content[data-v-23887a24]{margin-top:%?24?%;background-color:#fff;border-radius:%?14?%}.my-nav-content .my-nav-item[data-v-23887a24]{display:flex;align-items:center;padding-left:%?24?%}.my-nav-content .my-nav-item .my-ni-left[data-v-23887a24]{flex:1;margin-left:%?20?%;padding:%?30?% %?30?% %?30?% 0;display:flex;align-items:center;justify-content:space-between;border-bottom:%?1?% solid #f7f7f7}.my-nav-content .my-nav-item .icon[data-v-23887a24]{width:%?32?%;height:%?32?%}',""]),t.exports=e},"5e26":function(t,e,n){"use strict";n.d(e,"b",(function(){return i})),n.d(e,"c",(function(){return a})),n.d(e,"a",(function(){}));var i=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("v-uni-view",{staticClass:"container"},[n("v-uni-view",{staticClass:"mh-person"},[t.loginState?n("v-uni-view",{staticStyle:{display:"flex","border-radius":"50%",overflow:"hidden"}},[n("v-uni-image",{staticClass:"mh-p-img",attrs:{src:t.userData.imgUrl}})],1):n("v-uni-view",{staticClass:"mh-p-img mh-p-img-bg"},[n("v-uni-text",{staticClass:"iconfont icon-wode"})],1),t.loginState?n("v-uni-view",{staticClass:"mh-p-center"},[n("v-uni-view",{staticClass:"mh-pc-text1"},[t._v(t._s(t.userData.userName))]),n("v-uni-view",{staticClass:"mh-pc-text2"},[t._v(t._s(t.userData.phone))])],1):n("v-uni-view",{staticClass:"mh-p-center",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.login.apply(void 0,arguments)}}},[t._v("登录")])],1),n("v-uni-view",{staticClass:"my_order"},[n("v-uni-view",{staticClass:"my_order_title"},[n("v-uni-view",{staticStyle:{"font-size":"28rpx","font-weight":"600"}},[t._v("我的订单")]),n("v-uni-view",{staticStyle:{display:"flex","align-items":"center"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.goOrder(5)}}},[n("v-uni-view",{staticStyle:{color:"#9FA3B0","font-size":"24rpx"}},[t._v("全部订单")]),n("v-uni-view",{staticClass:"right-icon",staticStyle:{"margin-left":"6rpx"}})],1)],1),n("v-uni-view",{staticClass:"my_order_state"},[n("v-uni-view",{staticClass:"my_order_list",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.goOrder(0)}}},[n("v-uni-view",{staticStyle:{display:"flex","justify-content":"center"}},[n("v-uni-image",{staticStyle:{width:"48rpx",height:"48rpx"},attrs:{src:"/static/img/my/my_wallet-2.png",alt:""}})],1),n("v-uni-view",{staticStyle:{"font-size":"24rpx","text-align":"center","margin-top":"12rpx"}},[t._v("待付款")])],1),n("v-uni-view",{staticClass:"my_order_list",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.goOrder(1)}}},[n("v-uni-view",{staticStyle:{display:"flex","justify-content":"center"}},[n("v-uni-image",{staticStyle:{width:"48rpx",height:"48rpx"},attrs:{src:"/static/img/my/my_transaction-minus.png",alt:""}})],1),n("v-uni-view",{staticStyle:{"font-size":"24rpx","text-align":"center","margin-top":"12rpx"}},[t._v("待核销")])],1),n("v-uni-view",{staticClass:"my_order_list",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.goOrder(3)}}},[n("v-uni-view",{staticStyle:{display:"flex","justify-content":"center"}},[n("v-uni-image",{staticStyle:{width:"48rpx",height:"48rpx"},attrs:{src:"/static/img/my/my_receipt-item.png",alt:""}})],1),n("v-uni-view",{staticStyle:{"font-size":"24rpx","text-align":"center","margin-top":"12rpx"}},[t._v("已完成")])],1),n("v-uni-view",{staticClass:"my_order_list",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.goOrder(5)}}},[n("v-uni-view",{staticStyle:{display:"flex","justify-content":"center"}},[n("v-uni-image",{staticStyle:{width:"48rpx",height:"48rpx"},attrs:{src:"/static/img/my/my_receipt-search.png",alt:""}})],1),n("v-uni-view",{staticStyle:{"font-size":"24rpx","text-align":"center","margin-top":"12rpx"}},[t._v("全部订单")])],1)],1)],1),n("v-uni-view",{staticClass:"my-nav-content"},t._l(t.subMenu,(function(e,i){return n("v-uni-view",{key:i,staticClass:"my-nav-item",on:{click:function(n){arguments[0]=n=t.$handleEvent(n),t.toUrl(e)}}},[n("v-uni-view",{staticStyle:{display:"flex"}},[n("v-uni-image",{staticClass:"icon",attrs:{src:e.icon,alt:""}})],1),n("v-uni-view",{staticClass:"my-ni-left"},[n("v-uni-view",[t._v(t._s(e.name))]),n("v-uni-view",{staticClass:"right-icon"})],1)],1)})),1)],1)},a=[]},"74dd":function(t,e,n){"use strict";var i=n("de38"),a=n.n(i);a.a},"7af1":function(t,e,n){var i=n("9550");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var a=n("4f06").default;a("0e18955d",i,!0,{sourceMap:!1,shadowMode:!1})},9550:function(t,e,n){var i=n("24fb");e=i(!1),e.push([t.i,"uni-page-body[data-v-23887a24]{background-color:#f7f7f7}body.?%PAGE?%[data-v-23887a24]{background-color:#f7f7f7}",""]),t.exports=e},a489:function(t,e,n){"use strict";n("7a82");var i=n("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var a,r=i(n("ade3")),s=i(n("c7eb")),o=i(n("1da1")),c=n("30ea"),u=n("9e56"),l=(getApp(),{name:"my",data:function(){return{isTest:null,pageLoad:!0,loginState:!1,userData:{},show:!1,uid:"1",token:"123321123",phone:"",subMenu:[{name:"个人中心",icon:"/static/img/my/my_use.png",url:"/pages/my/personCenter"},{name:"消息中心",icon:"/static/img/my/my_message.png",url:"/pages/notice/noticesList?type=官方公告"},{name:"提货点",icon:"/static/img/shop/shop_store.png",url:"/pages/promotion/store?type=1"},{name:"售后服务",icon:"/static/img/my/my_star.png",url:""},{name:"设置",icon:"/static/img/my/my_setting.png",url:"/pages/Set/Set"}]}},onLoad:function(t){},onShow:function(){this.isTest=uni.getStorageSync("isTest"),this.getUserInfo(),this.getUserInfokefu()},methods:(a={goOrder:function(t){uni.setStorage({key:"roder_status",data:t,success:function(){setTimeout((function(){uni.switchTab({url:"/pages/order/myOrder"})}),600)}})},tmyOrder:function(){uni.navigateTo({url:"/pages/order/myOrder"})},logOut:function(){uni.removeStorageSync("token"),uni.reLaunch({url:"/pages/register/register"})},tomyjifen:function(){uni.navigateTo({url:"/pages/indexChild/Redeem/Redeem"})}},(0,r.default)(a,"tomyjifen",(function(){uni.navigateTo({url:"/pages/indexChild/Redeem/Redeem"})})),(0,r.default)(a,"getUserInfo",(function(){var t=this;return(0,o.default)((0,s.default)().mark((function e(){var n;return(0,s.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,u.request(c.getUserInfoUrl,{},"POST");case 2:n=e.sent,0!==n.code?(t.loginState=!1,uni.showToast({title:n.msg,icon:"none"})):(t.loginState=!0,t.userData=n.data.user);case 4:case"end":return e.stop()}}),e)})))()})),(0,r.default)(a,"login",(function(){})),(0,r.default)(a,"toUrl",(function(t){"售后服务"==t.name&&this.phone?uni.makePhoneCall({phoneNumber:this.phone}):uni.navigateTo({url:t.url})})),(0,r.default)(a,"getUserInfokefu",(function(){var t=this;return(0,o.default)((0,s.default)().mark((function e(){var n,i;return(0,s.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return n=t,e.next=3,u.request(c.getUserkefuUrl,{},"POST");case 3:i=e.sent,console.log(i),0!==i.code?uni.showToast({title:i.msg,icon:"none"}):n.phone=i.data.configValue;case 6:case"end":return e.stop()}}),e)})))()})),a)});e.default=l},a55b:function(t,e,n){"use strict";n.r(e);var i=n("5e26"),a=n("bcdb");for(var r in a)["default"].indexOf(r)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(r);n("0de8"),n("74dd");var s=n("f0c5"),o=Object(s["a"])(a["default"],i["b"],i["c"],!1,null,"23887a24",null,!1,i["a"],void 0);e["default"]=o.exports},bcdb:function(t,e,n){"use strict";n.r(e);var i=n("a489"),a=n.n(i);for(var r in i)["default"].indexOf(r)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(r);e["default"]=a.a},de38:function(t,e,n){var i=n("2b14");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var a=n("4f06").default;a("01f44448",i,!0,{sourceMap:!1,shadowMode:!1})}}]);