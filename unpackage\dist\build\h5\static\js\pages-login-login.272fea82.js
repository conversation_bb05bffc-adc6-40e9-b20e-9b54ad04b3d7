(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-login-login"],{1116:function(t,n,e){"use strict";e.d(n,"b",(function(){return i})),e.d(n,"c",(function(){return a})),e.d(n,"a",(function(){}));var i=function(){var t=this,n=t.$createElement,e=t._self._c||n;return e("v-uni-view",{staticClass:"loginpage"},[e("v-uni-view",{staticStyle:{height:"50px",width:"100%"}}),e("v-uni-view",{staticClass:"login-img"},[e("v-uni-image",{staticStyle:{width:"150px",height:"150px","border-radius":"10px"},attrs:{src:"/static/KLLD.png",mode:""}})],1),e("v-uni-view",{staticClass:"login-form"},[e("v-uni-view",{staticClass:"login-account"},[e("v-uni-image",{staticStyle:{width:"15px",height:"15px"},attrs:{src:"/static/img/login/ic_phone.png",mode:"widthFix"}}),e("v-uni-input",{attrs:{placeholder:"请输入手机号",type:"number"},model:{value:t.loginForm.phone,callback:function(n){t.$set(t.loginForm,"phone",n)},expression:"loginForm.phone"}})],1),e("v-uni-view",{staticClass:"login-account"},[e("v-uni-image",{staticStyle:{width:"15px",height:"15px"},attrs:{src:"/static/img/login/ic_password.png",mode:"widthFix"}}),"password"==t.passwordtype?e("v-uni-input",{attrs:{type:"password",placeholder:"请输入密码"},model:{value:t.loginForm.passwd,callback:function(n){t.$set(t.loginForm,"passwd",n)},expression:"loginForm.passwd"}}):e("v-uni-input",{attrs:{type:"text",placeholder:"请输入密码"},model:{value:t.loginForm.passwd,callback:function(n){t.$set(t.loginForm,"passwd",n)},expression:"loginForm.passwd"}}),"password"==t.passwordtype?e("v-uni-image",{staticClass:"showOre",staticStyle:{width:"15px",height:"10px"},attrs:{src:"/static/img/login/ic_password_normal.png",mode:"widthFix"},on:{click:function(n){arguments[0]=n=t.$handleEvent(n),t.sowPasst.apply(void 0,arguments)}}}):e("v-uni-image",{staticClass:"showOre",staticStyle:{width:"15px",height:"10px"},attrs:{src:"/static/img/login/ic_password_press.png",mode:"widthFix"},on:{click:function(n){arguments[0]=n=t.$handleEvent(n),t.hideenPasst.apply(void 0,arguments)}}})],1),e("v-uni-view",{staticClass:"footer"},[e("v-uni-text",{on:{click:function(n){arguments[0]=n=t.$handleEvent(n),t.gozhuce.apply(void 0,arguments)}}},[t._v("立即注册")]),e("v-uni-text",{on:{click:function(n){arguments[0]=n=t.$handleEvent(n),t.goForgotPassword.apply(void 0,arguments)}}},[t._v("忘记密码")])],1)],1),e("v-uni-view",{staticClass:"authorized-btn",on:{click:function(n){arguments[0]=n=t.$handleEvent(n),t.Login.apply(void 0,arguments)}}},[t._v("登录")])],1)},a=[]},1932:function(t,n,e){"use strict";e.r(n);var i=e("fddc"),a=e.n(i);for(var o in i)["default"].indexOf(o)<0&&function(t){e.d(n,t,(function(){return i[t]}))}(o);n["default"]=a.a},"1fde":function(t,n,e){var i=e("685b");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var a=e("4f06").default;a("7f4a0175",i,!0,{sourceMap:!1,shadowMode:!1})},"685b":function(t,n,e){var i=e("24fb");n=i(!1),n.push([t.i,'@charset "UTF-8";\r\n/* uni.scss */\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 字体变量 */\r\n/* 颜色变量 */\r\n/* 行为相关颜色 */\r\n/* 文字基本颜色 */\r\n/* 背景颜色 */\r\n/* 边框颜色 */\r\n/* 尺寸变量 */\r\n/* 文字尺寸 */\r\n/* 图片尺寸 */\r\n/* Border Radius */\r\n/* 水平间距 */\r\n/* 垂直间距 */\r\n/* 透明度 */\r\n/* 文章场景相关 */.loginpage[data-v-22ec2398]{padding:2px}.login-img[data-v-22ec2398]{display:flex;align-items:center;justify-content:center;margin-top:15px}\r\n/* 表单部分 */.login-form[data-v-22ec2398]{margin-left:25px;margin-right:25px;margin-top:25px}.login-account[data-v-22ec2398]{position:relative;display:flex;align-items:center;font-size:13px;align-items:center;background-color:#fff;height:45px;border-bottom:1px solid #e5e5e5}.login-account uni-input[data-v-22ec2398]{flex:1;margin-left:15px;padding:5px;padding-right:15px}.inp-palcehoder[data-v-22ec2398]{font-size:13px}.input-item[data-v-22ec2398]{position:relative;margin-left:20px;width:40px}.footer[data-v-22ec2398]{font-size:14px;margin-top:20px;display:flex;justify-content:space-between}.authorized-btn[data-v-22ec2398]{margin-top:15px;margin-left:15px;margin-right:15px;height:44px;line-height:44px;color:#fff;text-align:center;background-color:#6a9ffb;border-radius:20px}.footer[data-v-22ec2398]{color:#76a7fb;font-size:15px}',""]),t.exports=n},dd32:function(t,n,e){"use strict";var i=e("1fde"),a=e.n(i);a.a},f1a0:function(t,n,e){"use strict";e.r(n);var i=e("1116"),a=e("1932");for(var o in a)["default"].indexOf(o)<0&&function(t){e.d(n,t,(function(){return a[t]}))}(o);e("dd32");var s=e("f0c5"),r=Object(s["a"])(a["default"],i["b"],i["c"],!1,null,"22ec2398",null,!1,i["a"],void 0);n["default"]=r.exports},fddc:function(t,n,e){"use strict";e("7a82");var i=e("4ea4").default;Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0,e("498a");var a,o=i(e("c7eb")),s=i(e("1da1")),r=i(e("ade3")),c=e("30ea"),d=e("9e56"),u={data:function(){var t;return{loginForm:(t={passwd:"",phone:""},(0,r.default)(t,"passwd","17753422520"),(0,r.default)(t,"phone","15358381598"),(0,r.default)(t,"tenantId","3"),t),passwordtype:"password"}},onLoad:function(){},methods:(a={gozhuce:function(){uni.navigateTo({url:"/pages/register/register"})},hideenPasst:function(){this.passwordtype="password"},sowPasst:function(){this.passwordtype="number"},goForgotPassword:function(){uni.navigateTo({url:"/pages/ForgotPassword/ForgotPassword"})},Login:function(){uni.showLoading({title:"正在登录"}),this.handleLogin()}},(0,r.default)(a,"Login",(function(){uni.showLoading({title:"正在登录"}),this.handleLogin()})),(0,r.default)(a,"handleLogin",(function(){var t=this;return(0,s.default)((0,o.default)().mark((function n(){var e;return(0,o.default)().wrap((function(n){while(1)switch(n.prev=n.next){case 0:if(t.loginForm.phone.trim()){n.next=3;break}return uni.showToast({title:"手机号不能为空",icon:"none"}),n.abrupt("return");case 3:if(t.loginForm.passwd.trim()){n.next=6;break}return uni.showToast({title:"密码不能空",icon:"none"}),n.abrupt("return");case 6:return n.next=8,d.request(c.LoginUrl,{passwd:t.loginForm.passwd,phone:t.loginForm.phone,tenantId:t.loginForm.tenantId},"POST");case 8:if(e=n.sent,0===e.code){n.next=15;break}return uni.hideLoading(),setTimeout((function(){uni.showToast({title:e.msg,icon:"none"})}),30),n.abrupt("return");case 15:uni.setStorageSync("token",e.data.token),"17753422520"==e.data.user.phone?(uni.setStorageSync("isTest",!0),uni.setTabBarItem({index:2,text:"消息"}),uni.setTabBarItem({index:1,text:"文案"})):uni.setStorageSync("isTest",!1),setTimeout((function(){uni.showToast({title:"登陆成功",icon:"none"}),uni.switchTab({url:"/pages/index/index"})}),30);case 18:case"end":return n.stop()}}),n)})))()})),a)};n.default=u}}]);