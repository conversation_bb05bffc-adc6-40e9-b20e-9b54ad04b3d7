{"version": 3, "sources": ["webpack:///E:/Home/ma-Yi/gold/node_modules/uview-ui/components/u-number-box/u-number-box.vue?bd3b", "webpack:///E:/Home/ma-Yi/gold/node_modules/uview-ui/components/u-number-box/u-number-box.vue?a79d", "webpack:///E:/Home/ma-Yi/gold/node_modules/uview-ui/components/u-number-box/u-number-box.vue?8096", "webpack:///E:/Home/ma-Yi/gold/node_modules/uview-ui/components/u-number-box/u-number-box.vue?cfa5", "uni-app:///node_modules/uview-ui/components/u-number-box/u-number-box.vue", "webpack:///E:/Home/ma-Yi/gold/node_modules/uview-ui/components/u-number-box/u-number-box.vue?dab8", "webpack:///E:/Home/ma-Yi/gold/node_modules/uview-ui/components/u-number-box/u-number-box.vue?3c0c"], "names": ["name", "props", "value", "type", "default", "bgColor", "min", "max", "step", "disabled", "size", "color", "inputWidth", "inputHeight", "index", "disabledInput", "cursorSpacing", "longPress", "pressTime", "positiveInteger", "watch", "inputVal", "data", "timer", "changeFromInner", "innerChangeTimer", "created", "computed", "getCursorSpacing", "methods", "btnTouchStart", "clearInterval", "clearTimer", "minus", "plus", "calcPlus", "baseNum1", "baseNum2", "baseNum", "calcMinus", "computeVal", "uni", "onBlur", "val", "onFocus", "handleChange", "clearTimeout"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAqI;AACrI;AACgE;AACL;AACsC;;;AAGjG;AACqM;AACrM,gBAAgB,8MAAU;AAC1B,EAAE,kFAAM;AACR,EAAE,mGAAM;AACR,EAAE,4GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,uGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,+NAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjCA;AAAA;AAAA;AAAA;AAAswB,CAAgB,2xBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC+B1xB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAzBA,eA0BA;EACAA;EACAC;IACA;IACAC;MACAC;MACAC;IACA;IACA;IACAC;MACAF;MACAC;IACA;IACA;IACAE;MACAH;MACAC;IACA;IACA;IACAG;MACAJ;MACAC;IACA;IACA;IACAI;MACAL;MACAC;IACA;IACA;IACAK;MACAN;MACAC;IACA;IACA;IACAM;MACAP;MACAC;IACA;IACA;IACAO;MACAR;MACAC;IACA;IACA;IACAQ;MACAT;MACAC;IACA;IACA;IACAS;MACAV;MACAC;IACA;IACA;IACAU;MACAX;MACAC;IACA;IACA;IACA;IACAW;MACAZ;MACAC;IACA;IACA;IACAY;MACAb;MACAC;IACA;IACA;IACAa;MACAd;MACAC;IACA;IACA;IACAc;MACAf;MACAC;IACA;IACA;IACAe;MACAhB;MACAC;IACA;EACA;EACAgB;IACAlB;MACA;MACA;QACA;QACA;QACA;QACA;QACA;UACA;QACA;MACA;IACA;IACAmB;MAAA;MACA;MACA;MACA;MACA;MACA;MACA,6DACAnB;MACA;MACA;QACA;QACA;UACAA;UACA;UACA;YACA;UACA;QACA;MACA;MACA;MACA;IACA;EACA;EACAoB;IACA;MACAD;MAAA;MACAE;MAAA;MACAC;MAAA;MACAC;IACA;EACA;EACAC;IACA;EACA;EACAC;IACAC;MACA;MACA;IACA;EACA;EACAC;IACA;IACAC;MAAA;MACA;MACA;MACA;MACA;MACAC;MACA;MACA;QACA;QACA;MACA;IACA;IACAC;MAAA;MACA;QACAD;QACA;MACA;IACA;IACAE;MACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;MACA;QACAC;MACA;QACAA;MACA;MACA;QACAC;MACA;QACAA;MACA;MACAC;MACA;MACA;IACA;IACA;IACAC;MACA;MACA;QACAH;MACA;QACAA;MACA;MACA;QACAC;MACA;QACAA;MACA;MACAC;MACA;MACA;IACA;IACAE;MACAC;MACA;MACA;MACA;MACA;QACAvC;MACA;QACAA;MACA;MACA;MACA;QACA;MACA;MACA;MACA;IACA;IACA;IACAwC;MAAA;MACA;MACA;MACA;MACA;MACA;MACAC;MACA;QACAA;MACA;QACAA;MACA;MACA;QACA;MACA;MACA;IACA;IACA;IACAC;MACA;IACA;IACAC;MAAA;MACA;MACA;MACA;QACAC;QACA;MACA;MACA;MACA;MACA;MACA;MACA;QACA;MACA;MACA;MACA;QACA;QACA5C;QACAY;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC5TA;AAAA;AAAA;AAAA;AAAy8C,CAAgB,s6CAAG,EAAC,C;;;;;;;;;;;ACA79C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "node-modules/uview-ui/components/u-number-box/u-number-box.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./u-number-box.vue?vue&type=template&id=18418972&scoped=true&\"\nvar renderjs\nimport script from \"./u-number-box.vue?vue&type=script&lang=js&\"\nexport * from \"./u-number-box.vue?vue&type=script&lang=js&\"\nimport style0 from \"./u-number-box.vue?vue&type=style&index=0&id=18418972&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"18418972\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"node_modules/uview-ui/components/u-number-box/u-number-box.vue\"\nexport default component.exports", "export * from \"-!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-number-box.vue?vue&type=template&id=18418972&scoped=true&\"", "var components\ntry {\n  components = {\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-icon/u-icon\" */ \"uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-number-box.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-number-box.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"u-numberbox\">\r\n\t\t<view class=\"u-icon-minus\" @touchstart.stop.prevent=\"btnTouchStart('minus')\" @touchend.stop.prevent=\"clearTimer\" :class=\"{ 'u-icon-disabled': disabled || inputVal <= min }\"\r\n\t\t    :style=\"{\r\n\t\t\t\tbackground: bgColor,\r\n\t\t\t\theight: inputHeight + 'rpx',\r\n\t\t\t\tcolor: color\r\n\t\t\t}\">\r\n\t\t\t<u-icon name=\"minus\" :size=\"size\"></u-icon>\r\n\t\t</view>\r\n\t\t<input :disabled=\"disabledInput || disabled\" :cursor-spacing=\"getCursorSpacing\" :class=\"{ 'u-input-disabled': disabled }\"\r\n\t\t    v-model=\"inputVal\" class=\"u-number-input\" @blur=\"onBlur\" @focus=\"onFocus\"\r\n\t\t    type=\"digit\" :style=\"{\r\n\t\t\t\tcolor: color,\r\n\t\t\t\tfontSize: size + 'rpx',\r\n\t\t\t\tbackground: bgColor,\r\n\t\t\t\theight: inputHeight + 'rpx',\r\n\t\t\t\twidth: inputWidth + 'rpx'\r\n\t\t\t}\" />\r\n\t\t<view class=\"u-icon-plus\" @touchstart.stop.prevent=\"btnTouchStart('plus')\" @touchend.stop.prevent=\"clearTimer\" :class=\"{ 'u-icon-disabled': disabled || inputVal >= max }\"\r\n\t\t    :style=\"{\r\n\t\t\t\tbackground: bgColor,\r\n\t\t\t\theight: inputHeight + 'rpx',\r\n\t\t\t\tcolor: color\r\n\t\t\t}\">\r\n\t\t\t<u-icon name=\"plus\" :size=\"size\"></u-icon>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\t/**\r\n\t * numberBox 步进器\r\n\t * @description 该组件一般用于商城购物选择物品数量的场景。注意：该输入框只能输入大于或等于0的整数，不支持小数输入\r\n\t * @tutorial https://www.uviewui.com/components/numberBox.html\r\n\t * @property {Number} value 输入框初始值（默认1）\r\n\t * @property {String} bg-color 输入框和按钮的背景颜色（默认#F2F3F5）\r\n\t * @property {Number} min 用户可输入的最小值（默认0）\r\n\t * @property {Number} max 用户可输入的最大值（默认99999）\r\n\t * @property {Number} step 步长，每次加或减的值（默认1）\r\n\t * @property {Boolean} disabled 是否禁用操作，禁用后无法加减或手动修改输入框的值（默认false）\r\n\t * @property {Boolean} disabled-input 是否禁止输入框手动输入值（默认false）\r\n\t * @property {Boolean} positive-integer 是否只能输入正整数（默认true）\r\n\t * @property {String | Number} size 输入框文字和按钮字体大小，单位rpx（默认26）\r\n\t * @property {String} color 输入框文字和加减按钮图标的颜色（默认#323233）\r\n\t * @property {String | Number} input-width 输入框宽度，单位rpx（默认80）\r\n\t * @property {String | Number} input-height 输入框和按钮的高度，单位rpx（默认50）\r\n\t * @property {String | Number} index 事件回调时用以区分当前发生变化的是哪个输入框\r\n\t * @property {Boolean} long-press 是否开启长按连续递增或递减(默认true)\r\n\t * @property {String | Number} press-time 开启长按触发后，每触发一次需要多久，单位ms(默认250)\r\n\t * @property {String | Number} cursor-spacing 指定光标于键盘的距离，避免键盘遮挡输入框，单位rpx（默认200）\r\n\t * @event {Function} change 输入框内容发生变化时触发，对象形式\r\n\t * @event {Function} blur 输入框失去焦点时触发，对象形式\r\n\t * @event {Function} minus 点击减少按钮时触发(按钮可点击情况下)，对象形式\r\n\t * @event {Function} plus 点击增加按钮时触发(按钮可点击情况下)，对象形式\r\n\t * @example <u-number-box :min=\"1\" :max=\"100\"></u-number-box>\r\n\t */\r\n\texport default {\r\n\t\tname: \"u-number-box\",\r\n\t\tprops: {\r\n\t\t\t// 预显示的数字\r\n\t\t\tvalue: {\r\n\t\t\t\ttype: Number,\r\n\t\t\t\tdefault: 1\r\n\t\t\t},\r\n\t\t\t// 背景颜色\r\n\t\t\tbgColor: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: '#F2F3F5'\r\n\t\t\t},\r\n\t\t\t// 最小值\r\n\t\t\tmin: {\r\n\t\t\t\ttype: Number,\r\n\t\t\t\tdefault: 0\r\n\t\t\t},\r\n\t\t\t// 最大值\r\n\t\t\tmax: {\r\n\t\t\t\ttype: Number,\r\n\t\t\t\tdefault: 99999\r\n\t\t\t},\r\n\t\t\t// 步进值，每次加或减的值\r\n\t\t\tstep: {\r\n\t\t\t\ttype: Number,\r\n\t\t\t\tdefault: 1\r\n\t\t\t},\r\n\t\t\t// 是否禁用加减操作\r\n\t\t\tdisabled: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: false\r\n\t\t\t},\r\n\t\t\t// input的字体大小，单位rpx\r\n\t\t\tsize: {\r\n\t\t\t\ttype: [Number, String],\r\n\t\t\t\tdefault: 26\r\n\t\t\t},\r\n\t\t\t// 加减图标的颜色\r\n\t\t\tcolor: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: '#323233'\r\n\t\t\t},\r\n\t\t\t// input宽度，单位rpx\r\n\t\t\tinputWidth: {\r\n\t\t\t\ttype: [Number, String],\r\n\t\t\t\tdefault: 80\r\n\t\t\t},\r\n\t\t\t// input高度，单位rpx\r\n\t\t\tinputHeight: {\r\n\t\t\t\ttype: [Number, String],\r\n\t\t\t\tdefault: 50\r\n\t\t\t},\r\n\t\t\t// index索引，用于列表中使用，让用户知道是哪个numberbox发生了变化，一般使用for循环出来的index值即可\r\n\t\t\tindex: {\r\n\t\t\t\ttype: [Number, String],\r\n\t\t\t\tdefault: ''\r\n\t\t\t},\r\n\t\t\t// 是否禁用输入框，与disabled作用于输入框时，为OR的关系，即想要禁用输入框，又可以加减的话\r\n\t\t\t// 设置disabled为false，disabledInput为true即可\r\n\t\t\tdisabledInput: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: false\r\n\t\t\t},\r\n\t\t\t// 输入框于键盘之间的距离\r\n\t\t\tcursorSpacing: {\r\n\t\t\t\ttype: [Number, String],\r\n\t\t\t\tdefault: 100\r\n\t\t\t},\r\n\t\t\t// 是否开启长按连续递增或递减\r\n\t\t\tlongPress: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: true\r\n\t\t\t},\r\n\t\t\t// 开启长按触发后，每触发一次需要多久\r\n\t\t\tpressTime: {\r\n\t\t\t\ttype: [Number, String],\r\n\t\t\t\tdefault: 250\r\n\t\t\t},\r\n\t\t\t// 是否只能输入大于或等于0的整数(正整数)\r\n\t\t\tpositiveInteger: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: true\r\n\t\t\t}\r\n\t\t},\r\n\t\twatch: {\r\n\t\t\tvalue(v1, v2) {\r\n\t\t\t\t// 只有value的改变是来自外部的时候，才去同步inputVal的值，否则会造成循环错误\r\n\t\t\t\tif(!this.changeFromInner) {\r\n\t\t\t\t\tthis.inputVal = v1;\r\n\t\t\t\t\t// 因为inputVal变化后，会触发this.handleChange()，在其中changeFromInner会再次被设置为true，\r\n\t\t\t\t\t// 造成外面修改值，也导致被认为是内部修改的混乱，这里进行this.$nextTick延时，保证在运行周期的最后处\r\n\t\t\t\t\t// 将changeFromInner设置为false\r\n\t\t\t\t\tthis.$nextTick(function(){\r\n\t\t\t\t\t\tthis.changeFromInner = false;\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tinputVal(v1, v2) {\r\n\t\t\t\t// 为了让用户能够删除所有输入值，重新输入内容，删除所有值后，内容为空字符串\r\n\t\t\t\tif (v1 == '') return;\r\n\t\t\t\tlet value = 0;\r\n\t\t\t\t// 首先判断是否数值，并且在min和max之间，如果不是，使用原来值\r\n\t\t\t\tlet tmp = this.$u.test.number(v1);\r\n\t\t\t\tif (tmp && v1 >= this.min && v1 <= this.max) value = v1;\r\n\t\t\t\telse value = v2;\r\n\t\t\t\t// 判断是否只能输入大于等于0的整数\r\n\t\t\t\tif(this.positiveInteger) {\r\n\t\t\t\t\t// 小于0，或者带有小数点，\r\n\t\t\t\t\tif(v1 < 0 || String(v1).indexOf('.') !== -1) {\r\n\t\t\t\t\t\tvalue = v2;\r\n\t\t\t\t\t\t// 双向绑定input的值，必须要使用$nextTick修改显示的值\r\n\t\t\t\t\t\tthis.$nextTick(() => {\r\n\t\t\t\t\t\t\tthis.inputVal = v2;\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\t// 发出change事件\r\n\t\t\t\tthis.handleChange(value, 'change');\r\n\t\t\t}\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tinputVal: 1, // 输入框中的值，不能直接使用props中的value，因为应该改变props的状态\r\n\t\t\t\ttimer: null, // 用作长按的定时器\r\n\t\t\t\tchangeFromInner: false, // 值发生变化，是来自内部还是外部\r\n\t\t\t\tinnerChangeTimer: null, // 内部定时器\r\n\t\t\t};\r\n\t\t},\r\n\t\tcreated() {\r\n\t\t\tthis.inputVal = Number(this.value);\r\n\t\t},\r\n\t\tcomputed: {\r\n\t\t\tgetCursorSpacing() {\r\n\t\t\t\t// 先将值转为px单位，再转为数值\r\n\t\t\t\treturn Number(uni.upx2px(this.cursorSpacing));\r\n\t\t\t}\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\t// 点击退格键\r\n\t\t\tbtnTouchStart(callback) {\r\n\t\t\t\t// 先执行一遍方法，否则会造成松开手时，就执行了clearTimer，导致无法实现功能\r\n\t\t\t\tthis[callback]();\r\n\t\t\t\t// 如果没开启长按功能，直接返回\r\n\t\t\t\tif (!this.longPress) return;\r\n\t\t\t\tclearInterval(this.timer); //再次清空定时器，防止重复注册定时器\r\n\t\t\t\tthis.timer = null;\r\n\t\t\t\tthis.timer = setInterval(() => {\r\n\t\t\t\t\t// 执行加或减函数\r\n\t\t\t\t\tthis[callback]();\r\n\t\t\t\t}, this.pressTime);\r\n\t\t\t},\r\n\t\t\tclearTimer() {\r\n\t\t\t\tthis.$nextTick(() => {\r\n\t\t\t\t\tclearInterval(this.timer);\r\n\t\t\t\t\tthis.timer = null;\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tminus() {\r\n\t\t\t\tthis.computeVal('minus');\r\n\t\t\t},\r\n\t\t\tplus() {\r\n\t\t\t\tthis.computeVal('plus');\r\n\t\t\t},\r\n\t\t\t// 为了保证小数相加减出现精度溢出的问题\r\n\t\t\tcalcPlus(num1, num2) {\r\n\t\t\t\tlet baseNum, baseNum1, baseNum2;\r\n\t\t\t\ttry {\r\n\t\t\t\t\tbaseNum1 = num1.toString().split('.')[1].length;\r\n\t\t\t\t} catch (e) {\r\n\t\t\t\t\tbaseNum1 = 0;\r\n\t\t\t\t}\r\n\t\t\t\ttry {\r\n\t\t\t\t\tbaseNum2 = num2.toString().split('.')[1].length;\r\n\t\t\t\t} catch (e) {\r\n\t\t\t\t\tbaseNum2 = 0;\r\n\t\t\t\t}\r\n\t\t\t\tbaseNum = Math.pow(10, Math.max(baseNum1, baseNum2));\r\n\t\t\t\tlet precision = baseNum1 >= baseNum2 ? baseNum1 : baseNum2; //精度\r\n\t\t\t\treturn ((num1 * baseNum + num2 * baseNum) / baseNum).toFixed(precision);\r\n\t\t\t},\r\n\t\t\t// 为了保证小数相加减出现精度溢出的问题\r\n\t\t\tcalcMinus(num1, num2) {\r\n\t\t\t\tlet baseNum, baseNum1, baseNum2;\r\n\t\t\t\ttry {\r\n\t\t\t\t\tbaseNum1 = num1.toString().split('.')[1].length;\r\n\t\t\t\t} catch (e) {\r\n\t\t\t\t\tbaseNum1 = 0;\r\n\t\t\t\t}\r\n\t\t\t\ttry {\r\n\t\t\t\t\tbaseNum2 = num2.toString().split('.')[1].length;\r\n\t\t\t\t} catch (e) {\r\n\t\t\t\t\tbaseNum2 = 0;\r\n\t\t\t\t}\r\n\t\t\t\tbaseNum = Math.pow(10, Math.max(baseNum1, baseNum2));\r\n\t\t\t\tlet precision = baseNum1 >= baseNum2 ? baseNum1 : baseNum2;\r\n\t\t\t\treturn ((num1 * baseNum - num2 * baseNum) / baseNum).toFixed(precision);\r\n\t\t\t},\r\n\t\t\tcomputeVal(type) {\r\n\t\t\t\tuni.hideKeyboard();\r\n\t\t\t\tif (this.disabled) return;\r\n\t\t\t\tlet value = 0;\r\n\t\t\t\t// 减\r\n\t\t\t\tif (type === 'minus') {\r\n\t\t\t\t\tvalue = this.calcMinus(this.inputVal, this.step);\r\n\t\t\t\t} else if (type === 'plus') {\r\n\t\t\t\t\tvalue = this.calcPlus(this.inputVal, this.step);\r\n\t\t\t\t}\r\n\t\t\t\t// 判断是否小于最小值和大于最大值\r\n\t\t\t\tif (value < this.min || value > this.max) {\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tthis.inputVal = value;\r\n\t\t\t\tthis.handleChange(value, type);\r\n\t\t\t},\r\n\t\t\t// 处理用户手动输入的情况\r\n\t\t\tonBlur(event) {\r\n\t\t\t\tlet val = 0;\r\n\t\t\t\tlet value = event.detail.value;\r\n\t\t\t\t// 如果为非0-9数字组成，或者其第一位数值为0，直接让其等于min值\r\n\t\t\t\t// 这里不直接判断是否正整数，是因为用户传递的props min值可能为0\r\n\t\t\t\tif (!/(^\\d+$)/.test(value) || value[0] == 0) val = this.min;\r\n\t\t\t\tval = +value;\r\n\t\t\t\tif (val > this.max) {\r\n\t\t\t\t\tval = this.max;\r\n\t\t\t\t} else if (val < this.min) {\r\n\t\t\t\t\tval = this.min;\r\n\t\t\t\t}\r\n\t\t\t\tthis.$nextTick(() => {\r\n\t\t\t\t\tthis.inputVal = val;\r\n\t\t\t\t})\r\n\t\t\t\tthis.handleChange(val, 'blur');\r\n\t\t\t},\r\n\t\t\t// 输入框获得焦点事件\r\n\t\t\tonFocus() {\r\n\t\t\t\tthis.$emit('focus');\r\n\t\t\t},\r\n\t\t\thandleChange(value, type) {\r\n\t\t\t\tif (this.disabled) return;\r\n\t\t\t\t// 清除定时器，避免造成混乱\r\n\t\t\t\tif(this.innerChangeTimer) {\r\n\t\t\t\t\tclearTimeout(this.innerChangeTimer);\r\n\t\t\t\t\tthis.innerChangeTimer = null;\r\n\t\t\t\t}\r\n\t\t\t\t// 发出input事件，修改通过v-model绑定的值，达到双向绑定的效果\r\n\t\t\t\tthis.changeFromInner = true;\r\n\t\t\t\t// 一定时间内，清除changeFromInner标记，否则内部值改变后\r\n\t\t\t\t// 外部通过程序修改value值，将会无效\r\n\t\t\t\tthis.innerChangeTimer = setTimeout(() => {\r\n\t\t\t\t\tthis.changeFromInner = false;\r\n\t\t\t\t}, 150);\r\n\t\t\t\tthis.$emit('input', Number(value));\r\n\t\t\t\tthis.$emit(type, {\r\n\t\t\t\t\t// 转为Number类型\r\n\t\t\t\t\tvalue: Number(value),\r\n\t\t\t\t\tindex: this.index\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t}\r\n\t};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n\t@import \"../../libs/css/style.components.scss\";\r\n\r\n\t.u-numberbox {\r\n\t\tdisplay: inline-flex;\r\n\t\talign-items: center;\r\n\t}\r\n\r\n\t.u-number-input {\r\n\t\tposition: relative;\r\n\t\ttext-align: center;\r\n\t\tpadding: 0;\r\n\t\tmargin: 0 6rpx;\r\n\t\t@include vue-flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t}\r\n\r\n\t.u-icon-plus,\r\n\t.u-icon-minus {\r\n\t\twidth: 60rpx;\r\n\t\t@include vue-flex;\r\n\t\tjustify-content: center;\r\n\t\talign-items: center;\r\n\t}\r\n\r\n\t.u-icon-plus {\r\n\t\tborder-radius: 0 8rpx 8rpx 0;\r\n\t}\r\n\r\n\t.u-icon-minus {\r\n\t\tborder-radius: 8rpx 0 0 8rpx;\r\n\t}\r\n\r\n\t.u-icon-disabled {\r\n\t\tcolor: #c8c9cc !important;\r\n\t\tbackground: #f7f8fa !important;\r\n\t}\r\n\r\n\t.u-input-disabled {\r\n\t\tcolor: #c8c9cc !important;\r\n\t\tbackground-color: #f2f3f5 !important;\r\n\t}\r\n</style>\r\n", "import mod from \"-!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-number-box.vue?vue&type=style&index=0&id=18418972&lang=scss&scoped=true&\"; export default mod; export * from \"-!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-number-box.vue?vue&type=style&index=0&id=18418972&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1752590364598\n      var cssReload = require(\"D:/HBuilderX.3.3.13.20220314.full/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"../../\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}