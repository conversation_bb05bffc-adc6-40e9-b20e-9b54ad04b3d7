(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-indexChild-GoodsDetails-GoodsDetails"],{"065a":function(t,e,i){var r=i("24fb");e=r(!1),e.push([t.i,'@charset "UTF-8";\r\n/* uni.scss */\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 字体变量 */\r\n/* 颜色变量 */\r\n/* 行为相关颜色 */\r\n/* 文字基本颜色 */\r\n/* 背景颜色 */\r\n/* 边框颜色 */\r\n/* 尺寸变量 */\r\n/* 文字尺寸 */\r\n/* 图片尺寸 */\r\n/* Border Radius */\r\n/* 水平间距 */\r\n/* 垂直间距 */\r\n/* 透明度 */\r\n/* 文章场景相关 */.u-swiper-wrap[data-v-133bb712]{position:relative;overflow:hidden;-webkit-transform:translateY(0);transform:translateY(0)}.u-swiper-image[data-v-133bb712]{width:100%;will-change:transform;height:100%;display:block;pointer-events:none}.u-swiper-indicator[data-v-133bb712]{padding:0 %?24?%;position:absolute;display:flex;flex-direction:row;width:100%;z-index:1}.u-indicator-item-rect[data-v-133bb712]{width:%?26?%;height:%?8?%;margin:0 %?6?%;transition:all .5s;background-color:rgba(0,0,0,.3)}.u-indicator-item-rect-active[data-v-133bb712]{background-color:hsla(0,0%,100%,.8)}.u-indicator-item-dot[data-v-133bb712]{width:%?14?%;height:%?14?%;margin:0 %?6?%;border-radius:%?20?%;transition:all .5s;background-color:rgba(0,0,0,.3)}.u-indicator-item-dot-active[data-v-133bb712]{background-color:hsla(0,0%,100%,.8)}.u-indicator-item-round[data-v-133bb712]{width:%?14?%;height:%?14?%;margin:0 %?6?%;border-radius:%?20?%;transition:all .5s;background-color:rgba(0,0,0,.3)}.u-indicator-item-round-active[data-v-133bb712]{width:%?34?%;background-color:hsla(0,0%,100%,.8)}.u-indicator-item-number[data-v-133bb712]{padding:%?6?% %?16?%;line-height:1;background-color:rgba(0,0,0,.3);border-radius:%?100?%;font-size:%?26?%;color:hsla(0,0%,100%,.8)}.u-list-scale[data-v-133bb712]{-webkit-transform-origin:center center;transform-origin:center center}.u-list-image-wrap[data-v-133bb712]{width:100%;height:100%;flex:1;transition:all .5s;overflow:hidden;box-sizing:initial;position:relative}.u-swiper-title[data-v-133bb712]{position:absolute;background-color:rgba(0,0,0,.3);bottom:0;left:0;width:100%;font-size:%?28?%;padding:%?12?% %?24?%;color:hsla(0,0%,100%,.9)}.u-swiper-item[data-v-133bb712]{display:flex;flex-direction:row;overflow:hidden;align-items:center}',""]),t.exports=e},"0bde":function(t,e,i){"use strict";var r=i("21e4"),n=i.n(r);n.a},"13d0":function(t,e,i){"use strict";i("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("a9e3"),i("c975");var r={name:"u-swiper",props:{list:{type:Array,default:function(){return[]}},title:{type:Boolean,default:!1},indicator:{type:Object,default:function(){return{}}},borderRadius:{type:[Number,String],default:8},interval:{type:[String,Number],default:3e3},mode:{type:String,default:"round"},height:{type:[Number,String],default:250},indicatorPos:{type:String,default:"bottomCenter"},effect3d:{type:Boolean,default:!1},effect3dPreviousMargin:{type:[Number,String],default:50},autoplay:{type:Boolean,default:!0},duration:{type:[Number,String],default:500},circular:{type:Boolean,default:!0},imgMode:{type:String,default:"aspectFill"},name:{type:String,default:"image"},bgColor:{type:String,default:"#f3f4f6"},current:{type:[Number,String],default:0},titleStyle:{type:Object,default:function(){return{}}}},watch:{list:function(t,e){t.length!==e.length&&(this.uCurrent=0)},current:function(t){this.uCurrent=t}},data:function(){return{uCurrent:this.current}},computed:{justifyContent:function(){return"topLeft"==this.indicatorPos||"bottomLeft"==this.indicatorPos?"flex-start":"topCenter"==this.indicatorPos||"bottomCenter"==this.indicatorPos?"center":"topRight"==this.indicatorPos||"bottomRight"==this.indicatorPos?"flex-end":void 0},titlePaddingBottom:function(){var t=0;return"none"==this.mode?"12rpx":(t=["bottomLeft","bottomCenter","bottomRight"].indexOf(this.indicatorPos)>=0&&"number"==this.mode?"60rpx":["bottomLeft","bottomCenter","bottomRight"].indexOf(this.indicatorPos)>=0&&"number"!=this.mode?"40rpx":"12rpx",t)},elCurrent:function(){return Number(this.current)}},methods:{listClick:function(t){this.$emit("click",t)},change:function(t){var e=t.detail.current;this.uCurrent=e,this.$emit("change",e)},animationfinish:function(t){}}};e.default=r},"1c3a":function(t,e,i){"use strict";i.d(e,"b",(function(){return r})),i.d(e,"c",(function(){return n})),i.d(e,"a",(function(){}));var r=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",{staticClass:"u-divider",style:{height:"auto"==t.height?"auto":t.height+"rpx",backgroundColor:t.bgColor,marginBottom:t.marginBottom+"rpx",marginTop:t.marginTop+"rpx"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.click.apply(void 0,arguments)}}},[i("v-uni-view",{staticClass:"u-divider-line",class:[t.type?"u-divider-line--bordercolor--"+t.type:""],style:[t.lineStyle]}),t.useSlot?i("v-uni-view",{staticClass:"u-divider-text",style:{color:t.color,fontSize:t.fontSize+"rpx"}},[t._t("default")],2):t._e(),i("v-uni-view",{staticClass:"u-divider-line",class:[t.type?"u-divider-line--bordercolor--"+t.type:""],style:[t.lineStyle]})],1)},n=[]},"21e4":function(t,e,i){var r=i("065a");r.__esModule&&(r=r.default),"string"===typeof r&&(r=[[t.i,r,""]]),r.locals&&(t.exports=r.locals);var n=i("4f06").default;n("0e1c2d88",r,!0,{sourceMap:!1,shadowMode:!1})},"33c0":function(t,e,i){"use strict";i.r(e);var r=i("586e"),n=i.n(r);for(var o in r)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return r[t]}))}(o);e["default"]=n.a},"38ad":function(t,e,i){"use strict";i.r(e);var r=i("efc5"),n=i("4d11");for(var o in n)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(o);i("0bde");var a=i("f0c5"),d=Object(a["a"])(n["default"],r["b"],r["c"],!1,null,"133bb712",null,!1,r["a"],void 0);e["default"]=d.exports},"38cf":function(t,e,i){var r=i("23e7"),n=i("1148");r({target:"String",proto:!0},{repeat:n})},"4d11":function(t,e,i){"use strict";i.r(e);var r=i("13d0"),n=i.n(r);for(var o in r)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return r[t]}))}(o);e["default"]=n.a},"586e":function(t,e,i){"use strict";i("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("a9e3"),i("c975");var r={name:"u-divider",props:{halfWidth:{type:[Number,String],default:150},borderColor:{type:String,default:"#dcdfe6"},type:{type:String,default:"primary"},color:{type:String,default:"#909399"},fontSize:{type:[Number,String],default:26},bgColor:{type:String,default:"#ffffff"},height:{type:[Number,String],default:"auto"},marginTop:{type:[String,Number],default:0},marginBottom:{type:[String,Number],default:0},useSlot:{type:Boolean,default:!0}},computed:{lineStyle:function(){var t={};return-1!=String(this.halfWidth).indexOf("%")?t.width=this.halfWidth:t.width=this.halfWidth+"rpx",this.borderColor&&(t.borderColor=this.borderColor),t}},methods:{click:function(){this.$emit("click")}}};e.default=r},"663a":function(t,e,i){var r=i("24fb");e=r(!1),e.push([t.i,'@charset "UTF-8";\r\n/* uni.scss */\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 字体变量 */\r\n/* 颜色变量 */\r\n/* 行为相关颜色 */\r\n/* 文字基本颜色 */\r\n/* 背景颜色 */\r\n/* 边框颜色 */\r\n/* 尺寸变量 */\r\n/* 文字尺寸 */\r\n/* 图片尺寸 */\r\n/* Border Radius */\r\n/* 水平间距 */\r\n/* 垂直间距 */\r\n/* 透明度 */\r\n/* 文章场景相关 */.u-divider[data-v-f2b386da]{width:100%;position:relative;text-align:center;display:flex;flex-direction:row;justify-content:center;align-items:center;overflow:hidden;flex-direction:row}.u-divider-line[data-v-f2b386da]{border-bottom:1px solid #e4e7ed;-webkit-transform:scaleY(.5);transform:scaleY(.5);-webkit-transform-origin:center;transform-origin:center}.u-divider-line--bordercolor--primary[data-v-f2b386da]{border-color:#2979ff}.u-divider-line--bordercolor--success[data-v-f2b386da]{border-color:#19be6b}.u-divider-line--bordercolor--error[data-v-f2b386da]{border-color:#2979ff}.u-divider-line--bordercolor--info[data-v-f2b386da]{border-color:#909399}.u-divider-line--bordercolor--warning[data-v-f2b386da]{border-color:#f90}.u-divider-text[data-v-f2b386da]{white-space:nowrap;padding:0 %?16?%;display:inline-flex}',""]),t.exports=e},7173:function(t,e,i){"use strict";i("7a82");var r=i("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("ac1f"),i("5319");var n=r(i("c7eb")),o=r(i("1da1")),a=r(i("c8ed")),d=i("30ea"),s=i("9e56"),u={data:function(){return{number:a.default,goodsId:"",Goodsitem:{},show:!1,goodsNum:1}},onLoad:function(t){this.goodsId=t.goodsId,this.getGoodsdetail()},onShow:function(){},methods:{goPay:function(){uni.navigateTo({url:"/pages/indexChild/payment/payment?goodsId="+this.Goodsitem.id+"&goodsNum="+this.goodsNum})},showPopup:function(){this.show=!0},getGoodsdetail:function(){var t=this;return(0,o.default)((0,n.default)().mark((function e(){var i;return(0,n.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return uni.showLoading({title:"加载中"}),e.next=3,s.request(d.goodsUrl+"/"+t.goodsId,{},"POST");case 3:i=e.sent,console.log(i),0!==i.code?uni.showToast({title:i.msg,icon:"none"}):(uni.hideLoading(),i.data[0].goodsImg=i.data[0].goodsImg.split(","),i.data[0].goodsDetailInfo=i.data[0].goodsDetailInfo.replace("max-",""),i.data[0].goodsDescribe=i.data[0].goodsDescribe?i.data[0].goodsDescribe.split(","):[],i.data[0].baozhang=["品质保障","售后无忧"],t.Goodsitem=i.data[0]);case 6:case"end":return e.stop()}}),e)})))()}}};e.default=u},"7e37":function(t,e,i){"use strict";i.r(e);var r=i("f9b2"),n=i("d395");for(var o in n)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(o);i("9c1b");var a=i("f0c5"),d=Object(a["a"])(n["default"],r["b"],r["c"],!1,null,"05f7bbe3",null,!1,r["a"],void 0);e["default"]=d.exports},"8f12":function(t,e,i){"use strict";var r=i("e324"),n=i.n(r);n.a},9500:function(t,e,i){var r=i("24fb");e=r(!1),e.push([t.i,'@charset "UTF-8";\r\n/* uni.scss */\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 字体变量 */\r\n/* 颜色变量 */\r\n/* 行为相关颜色 */\r\n/* 文字基本颜色 */\r\n/* 背景颜色 */\r\n/* 边框颜色 */\r\n/* 尺寸变量 */\r\n/* 文字尺寸 */\r\n/* 图片尺寸 */\r\n/* Border Radius */\r\n/* 水平间距 */\r\n/* 垂直间距 */\r\n/* 透明度 */\r\n/* 文章场景相关 */.page[data-v-05f7bbe3]{background-color:#f5f5f5}.goods_content[data-v-05f7bbe3]{padding:%?24?% %?24?% 0 %?24?%}.goods_content .goods_padding[data-v-05f7bbe3]{padding:%?24?%;background-color:#fff;border-radius:%?10?%}.goods_name[data-v-05f7bbe3]{margin-top:%?18?%;font-size:%?32?%;font-weight:600}.goods_desc[data-v-05f7bbe3]{margin-top:%?20?%;display:flex;justify-content:space-between;color:#9fa3b0;font-size:%?24?%}.goods_price[data-v-05f7bbe3]{color:#ff0046;font-size:%?48?%}.goods_price > uni-text[data-v-05f7bbe3]:first-child{font-weight:600}.goods_params[data-v-05f7bbe3]{margin-top:%?20?%;padding:0 %?24?%;background-color:#fff;border-radius:%?10?%}.goods_params .goods_specifica[data-v-05f7bbe3]{display:flex;justify-content:space-between;align-items:center;border-bottom:1px solid #e5e6eb;padding:%?24?% 0}.tips_item[data-v-05f7bbe3]{margin-top:%?12?%;width:%?100?%;text-align:center;padding:%?6?% 0;border-radius:%?4?%;background-color:rgba(255,0,70,.05);color:#ff0046;font-size:%?22?%}.goods_detail_concent[data-v-05f7bbe3]{overflow-x:hidden;padding-bottom:%?170?%}.goods_detail_footer[data-v-05f7bbe3]{position:fixed;bottom:0;width:100%;height:%?170?%;display:flex;justify-content:center;background-color:#fff;padding-top:%?6?%}.goods_detail_footer uni-view[data-v-05f7bbe3]{height:%?84?%;width:90%;border-radius:9999px;background-color:#bba186;color:#fff;font-size:%?28?%;text-align:center;line-height:%?84?%}',""]),t.exports=e},9710:function(t,e,i){"use strict";i.r(e);var r=i("1c3a"),n=i("33c0");for(var o in n)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(o);i("8f12");var a=i("f0c5"),d=Object(a["a"])(n["default"],r["b"],r["c"],!1,null,"f2b386da",null,!1,r["a"],void 0);e["default"]=d.exports},"9c1b":function(t,e,i){"use strict";var r=i("ab64"),n=i.n(r);n.a},ab64:function(t,e,i){var r=i("9500");r.__esModule&&(r=r.default),"string"===typeof r&&(r=[[t.i,r,""]]),r.locals&&(t.exports=r.locals);var n=i("4f06").default;n("71b428b7",r,!0,{sourceMap:!1,shadowMode:!1})},c8ed:function(t,e,i){"use strict";i("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("d401"),i("d3b7"),i("25f0"),i("c975"),i("fb6a"),i("38cf"),i("99af");var r={parsePrice:function(t){if(t||0===t){var e=t.toString(),i=e.indexOf(".");return-1!==i&&e.length-i===3?e:-1!==i?e.length-i===2?e+"0":e.slice(0,i+3):e+".00"}return""},hideMiddleDigits:function(t){if(!t||"string"!==typeof t)return"";var e=t.slice(0,3)+"*".repeat(4),i=t.slice(7);return"".concat(e).concat(i)},oneparsePrice:function(t){if(null!=t&&""!==t){var e=t.toString(),i=e.indexOf(".");if(-1!==i){var r=e.length-i-1;return 1===r?e:r>1?e.slice(0,i+2):e+"0"}return e+".0"}return""}};e.default=r},d395:function(t,e,i){"use strict";i.r(e);var r=i("7173"),n=i.n(r);for(var o in r)["default"].indexOf(o)<0&&function(t){i.d(e,t,(function(){return r[t]}))}(o);e["default"]=n.a},e324:function(t,e,i){var r=i("663a");r.__esModule&&(r=r.default),"string"===typeof r&&(r=[[t.i,r,""]]),r.locals&&(t.exports=r.locals);var n=i("4f06").default;n("eec325a4",r,!0,{sourceMap:!1,shadowMode:!1})},efc5:function(t,e,i){"use strict";i.d(e,"b",(function(){return r})),i.d(e,"c",(function(){return n})),i.d(e,"a",(function(){}));var r=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",{staticClass:"u-swiper-wrap",style:{borderRadius:t.borderRadius+"rpx"}},[i("v-uni-swiper",{style:{height:t.height+"rpx",backgroundColor:t.bgColor},attrs:{current:t.elCurrent,interval:t.interval,circular:t.circular,duration:t.duration,autoplay:t.autoplay,"previous-margin":t.effect3d?t.effect3dPreviousMargin+"rpx":"0","next-margin":t.effect3d?t.effect3dPreviousMargin+"rpx":"0"},on:{change:function(e){arguments[0]=e=t.$handleEvent(e),t.change.apply(void 0,arguments)},animationfinish:function(e){arguments[0]=e=t.$handleEvent(e),t.animationfinish.apply(void 0,arguments)}}},t._l(t.list,(function(e,r){return i("v-uni-swiper-item",{key:r,staticClass:"u-swiper-item"},[i("v-uni-view",{staticClass:"u-list-image-wrap",class:[t.uCurrent!=r?"u-list-scale":""],style:{borderRadius:t.borderRadius+"rpx",transform:t.effect3d&&t.uCurrent!=r?"scaleY(0.9)":"scaleY(1)",margin:t.effect3d&&t.uCurrent!=r?"0 20rpx":0},on:{click:function(e){e.stopPropagation(),e.preventDefault(),arguments[0]=e=t.$handleEvent(e),t.listClick(r)}}},[i("v-uni-image",{staticClass:"u-swiper-image",attrs:{src:e[t.name]||e,mode:t.imgMode}}),t.title&&e.title?i("v-uni-view",{staticClass:"u-swiper-title u-line-1",style:[{"padding-bottom":t.titlePaddingBottom},t.titleStyle]},[t._v(t._s(e.title))]):t._e()],1)],1)})),1),i("v-uni-view",{staticClass:"u-swiper-indicator",style:{top:"topLeft"==t.indicatorPos||"topCenter"==t.indicatorPos||"topRight"==t.indicatorPos?"12rpx":"auto",bottom:"bottomLeft"==t.indicatorPos||"bottomCenter"==t.indicatorPos||"bottomRight"==t.indicatorPos?"12rpx":"auto",justifyContent:t.justifyContent,padding:"0 "+(t.effect3d?"74rpx":"24rpx")}},["rect"==t.mode?t._l(t.list,(function(e,r){return i("v-uni-view",{key:r,staticClass:"u-indicator-item-rect",class:{"u-indicator-item-rect-active":r==t.uCurrent}})})):t._e(),"dot"==t.mode?t._l(t.list,(function(e,r){return i("v-uni-view",{key:r,staticClass:"u-indicator-item-dot",class:{"u-indicator-item-dot-active":r==t.uCurrent}})})):t._e(),"round"==t.mode?t._l(t.list,(function(e,r){return i("v-uni-view",{key:r,staticClass:"u-indicator-item-round",class:{"u-indicator-item-round-active":r==t.uCurrent}})})):t._e(),"number"==t.mode?[i("v-uni-view",{staticClass:"u-indicator-item-number"},[t._v(t._s(t.uCurrent+1)+"/"+t._s(t.list.length))])]:t._e()],2)],1)},n=[]},f9b2:function(t,e,i){"use strict";i.d(e,"b",(function(){return n})),i.d(e,"c",(function(){return o})),i.d(e,"a",(function(){return r}));var r={uSwiper:i("38ad").default,uDivider:i("9710").default},n=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",{staticClass:"page"},[i("v-uni-view",{staticClass:"img"},[i("u-swiper",{attrs:{height:750,borderRadius:"0",imgMode:"widthFix",name:"imgUrl",list:t.Goodsitem.goodsImg,mode:"none"}})],1),i("v-uni-view",{staticClass:"goods_content"},[i("v-uni-view",{staticClass:"goods_padding"},[i("v-uni-view",{staticClass:"goods_price"},[i("v-uni-text",[i("v-uni-text",{staticStyle:{"font-size":"32rpx"}},[t._v("¥")]),t._v(t._s(t.Goodsitem.price))],1)],1),t._l(t.Goodsitem.goodsDescribe,(function(e,r){return i("v-uni-view",{key:r,staticClass:"tips_item"},[t._v(t._s(e))])})),i("v-uni-view",{staticClass:"goods_name"},[t._v(t._s(t.Goodsitem.goodsName))]),i("v-uni-view",{staticClass:"goods_desc"},[i("v-uni-view",[t._v("已成交 "+t._s(t.Goodsitem.sales)+" 件")]),i("v-uni-view",[t._v("剩余 "+t._s(t.Goodsitem.inventory)+" 件")])],1)],2),i("v-uni-view",{staticClass:"goods_params"},[i("v-uni-view",{staticClass:"goods_specifica"},[i("v-uni-view",{staticStyle:{display:"flex"}},[i("v-uni-view",{staticStyle:{color:"#61687C"}},[t._v("保障")]),i("v-uni-view",{staticStyle:{"margin-left":"24rpx",display:"flex",color:"#171B25"}},t._l(t.Goodsitem.baozhang,(function(e){return i("v-uni-view",{staticStyle:{display:"flex","align-items":"center","margin-right":"32rpx"}},[i("v-uni-view",{staticStyle:{display:"flex"}},[i("v-uni-image",{staticStyle:{width:"25rpx",height:"25rpx"},attrs:{src:"/static/img/shop/shop_tick-circle.png",alt:"",srcset:""}})],1),i("v-uni-view",{staticStyle:{"margin-left":"8rpx"}},[t._v(t._s(e))])],1)})),1)],1),i("v-uni-view",{staticClass:"right-icon"})],1),i("v-uni-view",{staticClass:"goods_specifica",staticStyle:{border:"none",color:"#171B25"}},[i("v-uni-view",{staticStyle:{display:"flex"}},[i("v-uni-view",{staticStyle:{color:"#61687C"}},[t._v("规格")]),i("v-uni-view",{staticStyle:{"margin-left":"24rpx",display:"flex"}},[t._v(t._s(t.Goodsitem.specification)+"g")])],1),i("v-uni-view",{staticClass:"right-icon"})],1)],1)],1),i("v-uni-view",{staticStyle:{padding:"30rpx 0"}},[i("u-divider",{attrs:{"half-width":"260","border-color":"#9FA3B0","bg-color":"#F5F5F5"}},[i("v-uni-image",{staticStyle:{width:"102rpx",height:"25rpx"},attrs:{mode:"widthFix",src:"/static/img/shop/shop_detail_title.png",alt:"",srcset:""}})],1)],1),i("v-uni-view",{staticClass:"goods_detail_concent"},[i("v-uni-rich-text",{attrs:{nodes:t.Goodsitem.goodsDetailInfo}})],1),i("v-uni-view",{staticClass:"goods_detail_footer"},[i("v-uni-view",{on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.goPay.apply(void 0,arguments)}}},[t._v("立即购买")])],1)],1)},o=[]}}]);