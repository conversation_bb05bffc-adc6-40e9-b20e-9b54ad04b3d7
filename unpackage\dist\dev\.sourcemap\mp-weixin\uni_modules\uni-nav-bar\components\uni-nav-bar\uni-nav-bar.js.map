{"version": 3, "sources": ["webpack:///E:/Home/ma-Yi/gold/uni_modules/uni-nav-bar/components/uni-nav-bar/uni-nav-bar.vue?1830", "webpack:///E:/Home/ma-Yi/gold/uni_modules/uni-nav-bar/components/uni-nav-bar/uni-nav-bar.vue?dd8c", "webpack:///E:/Home/ma-Yi/gold/uni_modules/uni-nav-bar/components/uni-nav-bar/uni-nav-bar.vue?11b0", "webpack:///E:/Home/ma-Yi/gold/uni_modules/uni-nav-bar/components/uni-nav-bar/uni-nav-bar.vue?617b", "uni-app:///uni_modules/uni-nav-bar/components/uni-nav-bar/uni-nav-bar.vue", "webpack:///E:/Home/ma-Yi/gold/uni_modules/uni-nav-bar/components/uni-nav-bar/uni-nav-bar.vue?d13c", "webpack:///E:/Home/ma-Yi/gold/uni_modules/uni-nav-bar/components/uni-nav-bar/uni-nav-bar.vue?772f"], "names": ["name", "components", "statusBar", "emits", "props", "dark", "type", "default", "title", "leftText", "rightText", "leftIcon", "rightIcon", "fixed", "color", "backgroundColor", "shadow", "border", "height", "leftWidth", "rightWidth", "stat", "computed", "themeBgColor", "themeColor", "navbarHeight", "leftIconWidth", "rightIconWidth", "mounted", "uni", "methods", "onClickLeft", "onClickRight", "onClickTitle"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAoI;AACpI;AAC+D;AACL;AACsC;;;AAGhG;AACqM;AACrM,gBAAgB,8MAAU;AAC1B,EAAE,iFAAM;AACR,EAAE,kGAAM;AACR,EAAE,2GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,sGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,sTAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACpDA;AAAA;AAAA;AAAA;AAAqwB,CAAgB,0xBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;ACmDzxB;EAAA;AAAA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AArBA,eAsBA;EACAA;EACAC;IACAC;EACA;EACAC;EACAC;IACAC;MACAC;MACAC;IACA;IACAC;MACAF;MACAC;IACA;IACAE;MACAH;MACAC;IACA;IACAG;MACAJ;MACAC;IACA;IACAI;MACAL;MACAC;IACA;IACAK;MACAN;MACAC;IACA;IACAM;MACAP;MACAC;IACA;IACAO;MACAR;MACAC;IACA;IACAQ;MACAT;MACAC;IACA;IACAL;MACAI;MACAC;IACA;IACAS;MACAV;MACAC;IACA;IACAU;MACAX;MACAC;IACA;IACAW;MACAZ;MACAC;IACA;IACAY;MACAb;MACAC;IACA;IACAa;MACAd;MACAC;IACA;IACAc;MACAf;MACAC;IACA;EACA;EACAe;IACAC;MACA;QACA;QACA;UACA;QACA;UACA;QACA;MACA;MACA;IACA;IACAC;MACA;QACA;QACA;UACA;QACA;UACA;QACA;MACA;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;EACA;EACAC;IACA;MACAC;IACA;EACA;EACAC;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACpMA;AAAA;AAAA;AAAA;AAAw8C,CAAgB,q6CAAG,EAAC,C;;;;;;;;;;;ACA59C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "uni_modules/uni-nav-bar/components/uni-nav-bar/uni-nav-bar.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./uni-nav-bar.vue?vue&type=template&id=6bda1a90&scoped=true&\"\nvar renderjs\nimport script from \"./uni-nav-bar.vue?vue&type=script&lang=js&\"\nexport * from \"./uni-nav-bar.vue?vue&type=script&lang=js&\"\nimport style0 from \"./uni-nav-bar.vue?vue&type=style&index=0&id=6bda1a90&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"6bda1a90\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"uni_modules/uni-nav-bar/components/uni-nav-bar/uni-nav-bar.vue\"\nexport default component.exports", "export * from \"-!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./uni-nav-bar.vue?vue&type=template&id=6bda1a90&scoped=true&\"", "var components\ntry {\n  components = {\n    uniIcons: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-icons/components/uni-icons/uni-icons\" */ \"@/uni_modules/uni-icons/components/uni-icons/uni-icons.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.leftIcon.length\n  var g1 = _vm.leftText.length\n  var g2 = g1 ? _vm.leftIcon.length : null\n  var g3 = _vm.title.length\n  var g4 = _vm.rightIcon.length\n  var g5 = _vm.rightText.length && !_vm.rightIcon.length\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        g1: g1,\n        g2: g2,\n        g3: g3,\n        g4: g4,\n        g5: g5,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./uni-nav-bar.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./uni-nav-bar.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"uni-navbar\" :class=\"{'uni-dark':dark, 'uni-nvue-fixed': fixed}\">\r\n\t\t<view class=\"uni-navbar__content\" :class=\"{ 'uni-navbar--fixed': fixed, 'uni-navbar--shadow': shadow, 'uni-navbar--border': border }\"\r\n\t\t\t:style=\"{ 'background-color': themeBgColor, 'border-bottom-color':themeColor }\" >\r\n\t\t\t<status-bar v-if=\"statusBar\" />\r\n\t\t\t<view :style=\"{ color: themeColor,backgroundColor: themeBgColor ,height:navbarHeight}\"\r\n\t\t\t\tclass=\"uni-navbar__header\">\r\n\t\t\t\t<view @tap=\"onClickLeft\" class=\"uni-navbar__header-btns uni-navbar__header-btns-left\"\r\n\t\t\t\t\t:style=\"{width:leftIconWidth}\">\r\n\t\t\t\t\t<slot name=\"left\">\r\n\t\t\t\t\t\t<view class=\"uni-navbar__content_view\" v-if=\"leftIcon.length > 0\">\r\n\t\t\t\t\t\t\t<uni-icons :color=\"themeColor\" :type=\"leftIcon\" size=\"20\" />\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view :class=\"{ 'uni-navbar-btn-icon-left': !leftIcon.length > 0 }\" class=\"uni-navbar-btn-text\"\r\n\t\t\t\t\t\t\tv-if=\"leftText.length\">\r\n\t\t\t\t\t\t\t<text :style=\"{ color: themeColor, fontSize: '12px' }\">{{ leftText }}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</slot>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"uni-navbar__header-container \" @tap=\"onClickTitle\">\r\n\t\t\t\t\t<slot>\r\n\t\t\t\t\t\t<view class=\"uni-navbar__header-container-inner\" v-if=\"title.length>0\">\r\n\t\t\t\t\t\t\t<text class=\"uni-nav-bar-text uni-ellipsis-1\"\r\n\t\t\t\t\t\t\t\t:style=\"{color: themeColor }\">{{ title }}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</slot>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view @click=\"onClickRight\" class=\"uni-navbar__header-btns uni-navbar__header-btns-right\"\r\n\t\t\t\t\t:style=\"{width:rightIconWidth}\">\r\n\t\t\t\t\t<slot name=\"right\">\r\n\t\t\t\t\t\t<view v-if=\"rightIcon.length\">\r\n\t\t\t\t\t\t\t<uni-icons :color=\"themeColor\" :type=\"rightIcon\" size=\"22\" />\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"uni-navbar-btn-text\" v-if=\"rightText.length && !rightIcon.length\">\r\n\t\t\t\t\t\t\t<text class=\"uni-nav-bar-right-text\" :style=\"{ color: themeColor}\">{{ rightText }}</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</slot>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<!-- #ifndef APP-NVUE -->\r\n\t\t<view class=\"uni-navbar__placeholder\" v-if=\"fixed\">\r\n\t\t\t<status-bar v-if=\"statusBar\" />\r\n\t\t\t<view class=\"uni-navbar__placeholder-view\" :style=\"{ height:navbarHeight}\" />\r\n\t\t</view>\r\n\t\t<!-- #endif -->\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport statusBar from \"./uni-status-bar.vue\";\r\n\tconst getVal = (val) => typeof val === 'number' ? val + 'px' : val;\r\n\r\n\t/**\r\n\t * \r\n\t * \r\n\t * NavBar 自定义导航栏\r\n\t * @description 导航栏组件，主要用于头部导航\r\n\t * @tutorial https://ext.dcloud.net.cn/plugin?id=52\r\n\t * @property {Boolean} dark 开启黑暗模式\r\n\t * @property {String} title 标题文字\r\n\t * @property {String} leftText 左侧按钮文本\r\n\t * @property {String} rightText 右侧按钮文本\r\n\t * @property {String} leftIcon 左侧按钮图标（图标类型参考 [Icon 图标](http://ext.dcloud.net.cn/plugin?id=28) type 属性）\r\n\t * @property {String} rightIcon 右侧按钮图标（图标类型参考 [Icon 图标](http://ext.dcloud.net.cn/plugin?id=28) type 属性）\r\n\t * @property {String} color 图标和文字颜色\r\n\t * @property {String} backgroundColor 导航栏背景颜色\r\n\t * @property {Boolean} fixed = [true|false] 是否固定顶部\r\n\t * @property {Boolean} statusBar = [true|false] 是否包含状态栏\r\n\t * @property {Boolean} shadow = [true|false] 导航栏下是否有阴影\r\n\t * @property {Boolean} stat 是否开启统计标题上报\r\n\t * @event {Function} clickLeft 左侧按钮点击时触发\r\n\t * @event {Function} clickRight 右侧按钮点击时触发\r\n\t * @event {Function} clickTitle 中间标题点击时触发\r\n\t */\r\n\texport default {\r\n\t\tname: \"UniNavBar\",\r\n\t\tcomponents: {\r\n\t\t\tstatusBar\r\n\t\t},\r\n\t\temits: ['clickLeft', 'clickRight', 'clickTitle'],\r\n\t\tprops: {\r\n\t\t\tdark: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: false\r\n\t\t\t},\r\n\t\t\ttitle: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: \"\"\r\n\t\t\t},\r\n\t\t\tleftText: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: \"\"\r\n\t\t\t},\r\n\t\t\trightText: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: \"\"\r\n\t\t\t},\r\n\t\t\tleftIcon: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: \"\"\r\n\t\t\t},\r\n\t\t\trightIcon: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: \"\"\r\n\t\t\t},\r\n\t\t\tfixed: {\r\n\t\t\t\ttype: [Boolean, String],\r\n\t\t\t\tdefault: false\r\n\t\t\t},\r\n\t\t\tcolor: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: \"\"\r\n\t\t\t},\r\n\t\t\tbackgroundColor: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: \"\"\r\n\t\t\t},\r\n\t\t\tstatusBar: {\r\n\t\t\t\ttype: [Boolean, String],\r\n\t\t\t\tdefault: false\r\n\t\t\t},\r\n\t\t\tshadow: {\r\n\t\t\t\ttype: [Boolean, String],\r\n\t\t\t\tdefault: false\r\n\t\t\t},\r\n\t\t\tborder: {\r\n\t\t\t\ttype: [Boolean, String],\r\n\t\t\t\tdefault: true\r\n\t\t\t},\r\n\t\t\theight: {\r\n\t\t\t\ttype: [Number, String],\r\n\t\t\t\tdefault: 44\r\n\t\t\t},\r\n\t\t\tleftWidth: {\r\n\t\t\t\ttype: [Number, String],\r\n\t\t\t\tdefault: 60\r\n\t\t\t},\r\n\t\t\trightWidth: {\r\n\t\t\t\ttype: [Number, String],\r\n\t\t\t\tdefault: 60\r\n\t\t\t},\r\n\t\t\tstat: {\r\n\t\t\t\ttype: [Boolean, String],\r\n\t\t\t\tdefault: ''\r\n\t\t\t}\r\n\t\t},\r\n\t\tcomputed: {\r\n\t\t\tthemeBgColor() {\r\n\t\t\t\tif (this.dark) {\r\n\t\t\t\t\t// 默认值\r\n\t\t\t\t\tif (this.backgroundColor) {\r\n\t\t\t\t\t\treturn this.backgroundColor\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\treturn this.dark ? '#333' : '#FFF'\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\treturn this.backgroundColor || '#FFF'\r\n\t\t\t},\r\n\t\t\tthemeColor() {\r\n\t\t\t\tif (this.dark) {\r\n\t\t\t\t\t// 默认值\r\n\t\t\t\t\tif (this.color) {\r\n\t\t\t\t\t\treturn this.color\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\treturn this.dark ? '#fff' : '#333'\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\treturn this.color || '#333'\r\n\t\t\t},\r\n\t\t\tnavbarHeight() {\r\n\t\t\t\treturn getVal(this.height)\r\n\t\t\t},\r\n\t\t\tleftIconWidth() {\r\n\t\t\t\treturn getVal(this.leftWidth)\r\n\t\t\t},\r\n\t\t\trightIconWidth() {\r\n\t\t\t\treturn getVal(this.rightWidth)\r\n\t\t\t}\r\n\t\t},\r\n\t\tmounted() {\r\n\t\t\tif (uni.report && this.stat && this.title !== '') {\r\n\t\t\t\tuni.report('title', this.title)\r\n\t\t\t}\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tonClickLeft() {\r\n\t\t\t\tthis.$emit(\"clickLeft\");\r\n\t\t\t},\r\n\t\t\tonClickRight() {\r\n\t\t\t\tthis.$emit(\"clickRight\");\r\n\t\t\t},\r\n\t\t\tonClickTitle() {\r\n\t\t\t\tthis.$emit(\"clickTitle\");\r\n\t\t\t}\r\n\t\t}\r\n\t};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n\t$nav-height: 44px;\r\n\r\n\t.uni-nvue-fixed {\r\n\t\t/* #ifdef APP-NVUE */\r\n\t\tposition: sticky;\r\n\t\t/* #endif */\r\n\t}\r\n\t.uni-navbar {\r\n\t\t// box-sizing: border-box;\r\n\t}\r\n\r\n\t.uni-nav-bar-text {\r\n\t\t/* #ifdef APP-PLUS */\r\n\t\tfont-size: 34rpx;\r\n\t\t/* #endif */\r\n\t\t/* #ifndef APP-PLUS */\r\n\t\tfont-size: 14px;\r\n\t\t/* #endif */\r\n\t}\r\n\r\n\t.uni-nav-bar-right-text {\r\n\t\tfont-size: 12px;\r\n\t}\r\n\r\n\t.uni-navbar__content {\r\n\t\tposition: relative;\r\n\t\t// background-color: #fff;\r\n\t\t// box-sizing: border-box;\r\n\t\tbackground-color: transparent;\r\n\t}\r\n\r\n\t.uni-navbar__content_view {\r\n\t\t// box-sizing: border-box;\r\n\t}\r\n\r\n\t.uni-navbar-btn-text {\r\n\t\t/* #ifndef APP-NVUE */\r\n\t\tdisplay: flex;\r\n\t\t/* #endif */\r\n\t\tflex-direction: column;\r\n\t\tjustify-content: flex-start;\r\n\t\talign-items: center;\r\n\t\tline-height: 12px;\r\n\t}\r\n\r\n\t.uni-navbar__header {\r\n\t\t/* #ifndef APP-NVUE */\r\n\t\tdisplay: flex;\r\n\t\t/* #endif */\r\n\t\tpadding: 0 10px;\r\n\t\tflex-direction: row;\r\n\t\theight: $nav-height;\r\n\t\tfont-size: 12px;\r\n\t}\r\n\r\n\t.uni-navbar__header-btns {\r\n\t\t/* #ifndef APP-NVUE */\r\n\t\toverflow: hidden;\r\n\t\tdisplay: flex;\r\n\t\t/* #endif */\r\n\t\tflex-wrap: nowrap;\r\n\t\tflex-direction: row;\r\n\t\twidth: 120rpx;\r\n\t\t// padding: 0 6px;\r\n\t\tjustify-content: center;\r\n\t\talign-items: center;\r\n\t\t/* #ifdef H5 */\r\n\t\tcursor: pointer;\r\n\t\t/* #endif */\r\n\t}\r\n\r\n\t.uni-navbar__header-btns-left {\r\n\t\t/* #ifndef APP-NVUE */\r\n\t\tdisplay: flex;\r\n\t\t/* #endif */\r\n\t\twidth: 120rpx;\r\n\t\tjustify-content: flex-start;\r\n\t\talign-items: center;\r\n\t}\r\n\r\n\t.uni-navbar__header-btns-right {\r\n\t\t/* #ifndef APP-NVUE */\r\n\t\tdisplay: flex;\r\n\t\t/* #endif */\r\n\t\tflex-direction: row;\r\n\t\t// width: 150rpx;\r\n\t\t// padding-right: 30rpx;\r\n\t\tjustify-content: flex-end;\r\n\t\talign-items: center;\r\n\t}\r\n\r\n\t.uni-navbar__header-container {\r\n\t\t/* #ifndef APP-NVUE */\r\n\t\tdisplay: flex;\r\n\t\t/* #endif */\r\n\t\tflex: 1;\r\n\t\tpadding: 0 10px;\r\n\t\toverflow: hidden;\r\n\t}\r\n\r\n\t.uni-navbar__header-container-inner {\r\n\t\t/* #ifndef APP-NVUE */\r\n\t\tdisplay: flex;\r\n\t\t/* #endif */\r\n\t\tflex: 1;\r\n\t\tflex-direction: row;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\tfont-size: 12px;\r\n\t\toverflow: hidden;\r\n\t\t// box-sizing: border-box;\r\n\t}\r\n\r\n\r\n\t.uni-navbar__placeholder-view {\r\n\t\theight: $nav-height;\r\n\t}\r\n\r\n\t.uni-navbar--fixed {\r\n\t\tposition: fixed;\r\n\t\tz-index: 99;\r\n\t\t/* #ifdef H5 */\r\n\t\tleft: var(--window-left);\r\n\t\tright: var(--window-right);\r\n\t\t/* #endif */\r\n\t\t/* #ifndef H5 */\r\n\t\tleft: 0;\r\n\t\tright: 0;\r\n\t\t/* #endif */\r\n\r\n\t}\r\n\r\n\t.uni-navbar--shadow {\r\n\t\tbox-shadow: 0 1px 6px #ccc;\r\n\t}\r\n\r\n\t.uni-navbar--border {\r\n\t\tborder-bottom-width: 1rpx;\r\n\t\tborder-bottom-style: solid;\r\n\t\tborder-bottom-color: #eee;\r\n\t}\r\n\r\n\t.uni-ellipsis-1 {\r\n\t\toverflow: hidden;\r\n\t\t/* #ifndef APP-NVUE */\r\n\t\twhite-space: nowrap;\r\n\t\ttext-overflow: ellipsis;\r\n\t\t/* #endif */\r\n\t\t/* #ifdef APP-NVUE */\r\n\t\tlines: 1;\r\n\t\ttext-overflow: ellipsis;\r\n\t\t/* #endif */\r\n\t}\r\n\r\n\t// 暗主题配置\r\n\t.uni-dark {}\r\n</style>\r\n", "import mod from \"-!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./uni-nav-bar.vue?vue&type=style&index=0&id=6bda1a90&lang=scss&scoped=true&\"; export default mod; export * from \"-!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./uni-nav-bar.vue?vue&type=style&index=0&id=6bda1a90&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1752590364061\n      var cssReload = require(\"D:/HBuilderX.3.3.13.20220314.full/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"../../\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}