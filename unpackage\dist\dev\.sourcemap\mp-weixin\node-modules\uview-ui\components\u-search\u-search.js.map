{"version": 3, "sources": ["webpack:///E:/pos/黄金/gold_client/node_modules/uview-ui/components/u-search/u-search.vue?c629", "webpack:///E:/pos/黄金/gold_client/node_modules/uview-ui/components/u-search/u-search.vue?0724", "webpack:///E:/pos/黄金/gold_client/node_modules/uview-ui/components/u-search/u-search.vue?8469", "webpack:///E:/pos/黄金/gold_client/node_modules/uview-ui/components/u-search/u-search.vue?2a7e", "uni-app:///node_modules/uview-ui/components/u-search/u-search.vue", "webpack:///E:/pos/黄金/gold_client/node_modules/uview-ui/components/u-search/u-search.vue?f010", "webpack:///E:/pos/黄金/gold_client/node_modules/uview-ui/components/u-search/u-search.vue?d871"], "names": ["name", "props", "shape", "type", "default", "bgColor", "placeholder", "clearabled", "focus", "showAction", "actionStyle", "actionText", "inputAlign", "disabled", "animation", "borderColor", "value", "height", "inputStyle", "maxlength", "searchIconColor", "color", "placeholderColor", "margin", "searchIcon", "data", "keyword", "showClear", "show", "focused", "watch", "immediate", "handler", "computed", "showActionBtn", "borderStyle", "methods", "inputChange", "clear", "search", "uni", "custom", "getFocus", "blur", "setTimeout", "clickHandler"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAiI;AACjI;AAC4D;AACL;AACsC;;;AAG7F;AACgL;AAChL,gBAAgB,yLAAU;AAC1B,EAAE,8EAAM;AACR,EAAE,+FAAM;AACR,EAAE,wGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,mGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,+NAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACnDA;AAAA;AAAA;AAAA;AAAypB,CAAgB,8qBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACiD7qB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AA/BA,gBAgCA;EACAA;EACAC;IACA;IACAC;MACAC;MACAC;IACA;IACA;IACAC;MACAF;MACAC;IACA;IACA;IACAE;MACAH;MACAC;IACA;IACA;IACAG;MACAJ;MACAC;IACA;IACA;IACAI;MACAL;MACAC;IACA;IACA;IACAK;MACAN;MACAC;IACA;IACA;IACAM;MACAP;MACAC;QACA;MACA;IACA;IACA;IACAO;MACAR;MACAC;IACA;IACA;IACAQ;MACAT;MACAC;IACA;IACA;IACAS;MACAV;MACAC;IACA;IACA;IACAU;MACAX;MACAC;IACA;IACA;IACAW;MACAZ;MACAC;IACA;IACA;IACAY;MACAb;MACAC;IACA;IACA;IACAa;MACAd;MACAC;IACA;IACA;IACAc;MACAf;MACAC;QACA;MACA;IACA;IACA;IACAe;MACAhB;MACAC;IACA;IACA;IACAgB;MACAjB;MACAC;IACA;IACA;IACAiB;MACAlB;MACAC;IACA;IACA;IACAkB;MACAnB;MACAC;IACA;IACA;IACAmB;MACApB;MACAC;IACA;IACA;IACAoB;MACArB;MACAC;IACA;EACA;EACAqB;IACA;MACAC;MACAC;MAAA;MACAC;MACA;MACAC;MACA;MACA;IACA;EACA;;EACAC;IACAJ;MACA;MACA;MACA;MACA;IACA;IACAV;MACAe;MACAC;QACA;MACA;IACA;EACA;EACAC;IACAC;MACA,yDACA;IACA;IACA;IACAC;MACA,wEACA;IACA;EACA;EACAC;IACA;IACAC;MACA;IACA;IACA;IACA;IACAC;MAAA;MACA;MACA;MACA;QACA;MACA;IACA;IACA;IACAC;MACA;MACA;QACA;QACAC;MACA;IACA;IACA;IACAC;MACA;MACA;QACA;QACAD;MACA;IACA;IACA;IACAE;MACA;MACA;MACA;MACA;IACA;IACA;IACAC;MAAA;MACA;MACA;MACAC;QACA;MACA;MACA;MACA;IACA;IACA;IACAC;MACA;IACA;EACA;AACA;AAAA,4B;;;;;;;;;;;;;AC1RA;AAAA;AAAA;AAAA;AAAwwC,CAAgB,quCAAG,EAAC,C;;;;;;;;;;;ACA5xC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "node-modules/uview-ui/components/u-search/u-search.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./u-search.vue?vue&type=template&id=1a326067&scoped=true&\"\nvar renderjs\nimport script from \"./u-search.vue?vue&type=script&lang=js&\"\nexport * from \"./u-search.vue?vue&type=script&lang=js&\"\nimport style0 from \"./u-search.vue?vue&type=style&index=0&id=1a326067&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"1a326067\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"node_modules/uview-ui/components/u-search/u-search.vue\"\nexport default component.exports", "export * from \"-!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-search.vue?vue&type=template&id=1a326067&scoped=true&\"", "var components\ntry {\n  components = {\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-icon/u-icon\" */ \"uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var s0 = _vm.__get_style([\n    {\n      textAlign: _vm.inputAlign,\n      color: _vm.color,\n      backgroundColor: _vm.bgColor,\n    },\n    _vm.inputStyle,\n  ])\n  var s1 = _vm.__get_style([_vm.actionStyle])\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        s0: s0,\n        s1: s1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-search.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-search.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"u-search\" @tap=\"clickHandler\" :style=\"{\r\n\t\tmargin: margin,\r\n\t}\">\r\n\t\t<view\r\n\t\t\tclass=\"u-content\"\r\n\t\t\t:style=\"{\r\n\t\t\t\tbackgroundColor: bgColor,\r\n\t\t\t\tborderRadius: shape == 'round' ? '100rpx' : '10rpx',\r\n\t\t\t\tborder: borderStyle,\r\n\t\t\t\theight: height + 'rpx'\r\n\t\t\t}\"\r\n\t\t>\r\n\t\t\t<view class=\"u-icon-wrap\">\r\n\t\t\t\t<u-icon class=\"u-clear-icon\" :size=\"30\" :name=\"searchIcon\" :color=\"searchIconColor ? searchIconColor : color\"></u-icon>\r\n\t\t\t</view>\r\n\t\t\t<input\r\n\t\t\t\tconfirm-type=\"search\"\r\n\t\t\t\t@blur=\"blur\"\r\n\t\t\t\t:value=\"value\"\r\n\t\t\t\t@confirm=\"search\"\r\n\t\t\t\t@input=\"inputChange\"\r\n\t\t\t\t:disabled=\"disabled\"\r\n\t\t\t\t@focus=\"getFocus\"\r\n\t\t\t\t:focus=\"focus\"\r\n\t\t\t\t:maxlength=\"maxlength\"\r\n\t\t\t\tplaceholder-class=\"u-placeholder-class\"\r\n\t\t\t\t:placeholder=\"placeholder\"\r\n\t\t\t\t:placeholder-style=\"`color: ${placeholderColor}`\"\r\n\t\t\t\tclass=\"u-input\"\r\n\t\t\t\ttype=\"text\"\r\n\t\t\t\t:style=\"[{\r\n\t\t\t\t\ttextAlign: inputAlign,\r\n\t\t\t\t\tcolor: color,\r\n\t\t\t\t\tbackgroundColor: bgColor,\r\n\t\t\t\t}, inputStyle]\"\r\n\t\t\t/>\r\n\t\t\t<view class=\"u-close-wrap\" v-if=\"keyword && clearabled && focused\" @tap=\"clear\">\r\n\t\t\t\t<u-icon class=\"u-clear-icon\" name=\"close-circle-fill\" size=\"34\" color=\"#c0c4cc\"></u-icon>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view :style=\"[actionStyle]\" class=\"u-action\" \r\n\t\t\t:class=\"[showActionBtn || show ? 'u-action-active' : '']\" \r\n\t\t\******************=\"custom\"\r\n\t\t>{{ actionText }}</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n/**\r\n * search 搜索框\r\n * @description 搜索组件，集成了常见搜索框所需功能，用户可以一键引入，开箱即用。\r\n * @tutorial https://www.uviewui.com/components/search.html\r\n * @property {String} shape 搜索框形状，round-圆形，square-方形（默认round）\r\n * @property {String} bg-color 搜索框背景颜色（默认#f2f2f2）\r\n * @property {String} border-color 边框颜色，配置了颜色，才会有边框\r\n * @property {String} placeholder 占位文字内容（默认“请输入关键字”）\r\n * @property {Boolean} clearabled 是否启用清除控件（默认true）\r\n * @property {Boolean} focus 是否自动获得焦点（默认false）\r\n * @property {Boolean} show-action 是否显示右侧控件（默认true）\r\n * @property {String} action-text 右侧控件文字（默认“搜索”）\r\n * @property {Object} action-style 右侧控件的样式，对象形式\r\n * @property {String} input-align 输入框内容水平对齐方式（默认left）\r\n * @property {Object} input-style 自定义输入框样式，对象形式\r\n * @property {Boolean} disabled 是否启用输入框（默认false）\r\n * @property {String} search-icon-color 搜索图标的颜色，默认同输入框字体颜色\r\n * @property {String} color 输入框字体颜色（默认#606266）\r\n * @property {String} placeholder-color placeholder的颜色（默认#909399）\r\n * @property {String} search-icon 输入框左边的图标，可以为uView图标名称或图片路径\r\n * @property {String} margin 组件与其他上下左右元素之间的距离，带单位的字符串形式，如\"30rpx\"\r\n * @property {Boolean} animation 是否开启动画，见上方说明（默认false）\r\n * @property {String} value 输入框初始值\r\n * @property {String | Number} maxlength 输入框最大能输入的长度，-1为不限制长度\r\n * @property {Boolean} input-style input输入框的样式，可以定义文字颜色，大小等，对象形式\r\n * @property {String | Number} height 输入框高度，单位rpx（默认64）\r\n * @event {Function} change 输入框内容发生变化时触发\r\n * @event {Function} search 用户确定搜索时触发，用户按回车键，或者手机键盘右下角的\"搜索\"键时触发\r\n * @event {Function} custom 用户点击右侧控件时触发\r\n * @event {Function} clear 用户点击清除按钮时触发\r\n * @example <u-search placeholder=\"日照香炉生紫烟\" v-model=\"keyword\"></u-search>\r\n */\r\nexport default {\r\n\tname: \"u-search\",\r\n\tprops: {\r\n\t\t// 搜索框形状，round-圆形，square-方形\r\n\t\tshape: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: 'round'\r\n\t\t},\r\n\t\t// 搜索框背景色，默认值#f2f2f2\r\n\t\tbgColor: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: '#f2f2f2'\r\n\t\t},\r\n\t\t// 占位提示文字\r\n\t\tplaceholder: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: '请输入关键字'\r\n\t\t},\r\n\t\t// 是否启用清除控件\r\n\t\tclearabled: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: true\r\n\t\t},\r\n\t\t// 是否自动聚焦\r\n\t\tfocus: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: false\r\n\t\t},\r\n\t\t// 是否在搜索框右侧显示取消按钮\r\n\t\tshowAction: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: true\r\n\t\t},\r\n\t\t// 右边控件的样式\r\n\t\tactionStyle: {\r\n\t\t\ttype: Object,\r\n\t\t\tdefault() {\r\n\t\t\t\treturn {};\r\n\t\t\t}\r\n\t\t},\r\n\t\t// 取消按钮文字\r\n\t\tactionText: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: '搜索'\r\n\t\t},\r\n\t\t// 输入框内容对齐方式，可选值为 left|center|right\r\n\t\tinputAlign: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: 'left'\r\n\t\t},\r\n\t\t// 是否启用输入框\r\n\t\tdisabled: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: false\r\n\t\t},\r\n\t\t// 开启showAction时，是否在input获取焦点时才显示\r\n\t\tanimation: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: false\r\n\t\t},\r\n\t\t// 边框颜色，只要配置了颜色，才会有边框\r\n\t\tborderColor: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: 'none'\r\n\t\t},\r\n\t\t// 输入框的初始化内容\r\n\t\tvalue: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: ''\r\n\t\t},\r\n\t\t// 搜索框高度，单位rpx\r\n\t\theight: {\r\n\t\t\ttype: [Number, String],\r\n\t\t\tdefault: 64\r\n\t\t},\r\n\t\t// input输入框的样式，可以定义文字颜色，大小等，对象形式\r\n\t\tinputStyle: {\r\n\t\t\ttype: Object,\r\n\t\t\tdefault() {\r\n\t\t\t\treturn {}\r\n\t\t\t}\r\n\t\t},\r\n\t\t// 输入框最大能输入的长度，-1为不限制长度(来自uniapp文档)\r\n\t\tmaxlength: {\r\n\t\t\ttype: [Number, String],\r\n\t\t\tdefault: '-1'\r\n\t\t},\r\n\t\t// 搜索图标的颜色，默认同输入框字体颜色\r\n\t\tsearchIconColor: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: ''\r\n\t\t},\r\n\t\t// 输入框字体颜色\r\n\t\tcolor: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: '#606266'\r\n\t\t},\r\n\t\t// placeholder的颜色\r\n\t\tplaceholderColor: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: '#909399'\r\n\t\t},\r\n\t\t// 组件与其他上下左右元素之间的距离，带单位的字符串形式，如\"30rpx\"、\"30rpx 20rpx\"等写法\r\n\t\tmargin: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: '0'\r\n\t\t},\r\n\t\t// 左边输入框的图标，可以为uView图标名称或图片路径\r\n\t\tsearchIcon: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: 'search'\r\n\t\t}\r\n\t},\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\tkeyword: '',\r\n\t\t\tshowClear: false, // 是否显示右边的清除图标\r\n\t\t\tshow: false,\r\n\t\t\t// 标记input当前状态是否处于聚焦中，如果是，才会显示右侧的清除控件\r\n\t\t\tfocused: this.focus\r\n\t\t\t// 绑定输入框的值\r\n\t\t\t// inputValue: this.value\r\n\t\t};\r\n\t},\r\n\twatch: {\r\n\t\tkeyword(nVal) {\r\n\t\t\t// 双向绑定值，让v-model绑定的值双向变化\r\n\t\t\tthis.$emit('input', nVal);\r\n\t\t\t// 触发change事件，事件效果和v-model双向绑定的效果一样，让用户多一个选择\r\n\t\t\tthis.$emit('change', nVal);\r\n\t\t},\r\n\t\tvalue: {\r\n\t\t\timmediate: true,\r\n\t\t\thandler(nVal) {\r\n\t\t\t\tthis.keyword = nVal;\r\n\t\t\t}\r\n\t\t}\r\n\t},\r\n\tcomputed: {\r\n\t\tshowActionBtn() {\r\n\t\t\tif (!this.animation && this.showAction) return true;\r\n\t\t\telse return false;\r\n\t\t},\r\n\t\t// 样式，根据用户传入的颜色值生成，如果不传入，默认为none\r\n\t\tborderStyle() {\r\n\t\t\tif (this.borderColor) return `1px solid ${this.borderColor}`;\r\n\t\t\telse return 'none';\r\n\t\t},\r\n\t},\r\n\tmethods: {\r\n\t\t// 目前HX2.6.9 v-model双向绑定无效，故监听input事件获取输入框内容的变化\r\n\t\tinputChange(e) {\r\n\t\t\tthis.keyword = e.detail.value;\r\n\t\t},\r\n\t\t// 清空输入\r\n\t\t// 也可以作为用户通过this.$refs形式调用清空输入框内容\r\n\t\tclear() {\r\n\t\t\tthis.keyword = '';\r\n\t\t\t// 延后发出事件，避免在父组件监听clear事件时，value为更新前的值(不为空)\r\n\t\t\tthis.$nextTick(() => {\r\n\t\t\t\tthis.$emit('clear');\r\n\t\t\t})\r\n\t\t},\r\n\t\t// 确定搜索\r\n\t\tsearch(e) {\r\n\t\t\tthis.$emit('search', e.detail.value);\r\n\t\t\ttry{\r\n\t\t\t\t// 收起键盘\r\n\t\t\t\tuni.hideKeyboard();\r\n\t\t\t}catch(e){}\r\n\t\t},\r\n\t\t// 点击右边自定义按钮的事件\r\n\t\tcustom() {\r\n\t\t\tthis.$emit('custom', this.keyword);\r\n\t\t\ttry{\r\n\t\t\t\t// 收起键盘\r\n\t\t\t\tuni.hideKeyboard();\r\n\t\t\t}catch(e){}\r\n\t\t},\r\n\t\t// 获取焦点\r\n\t\tgetFocus() {\r\n\t\t\tthis.focused = true;\r\n\t\t\t// 开启右侧搜索按钮展开的动画效果\r\n\t\t\tif (this.animation && this.showAction) this.show = true;\r\n\t\t\tthis.$emit('focus', this.keyword);\r\n\t\t},\r\n\t\t// 失去焦点\r\n\t\tblur() {\r\n\t\t\t// 最开始使用的是监听图标@touchstart事件，自从hx2.8.4后，此方法在微信小程序出错\r\n\t\t\t// 这里改为监听点击事件，手点击清除图标时，同时也发生了@blur事件，导致图标消失而无法点击，这里做一个延时\r\n\t\t\tsetTimeout(() => {\r\n\t\t\t\tthis.focused = false;\r\n\t\t\t}, 100)\r\n\t\t\tthis.show = false;\r\n\t\t\tthis.$emit('blur', this.keyword);\r\n\t\t},\r\n\t\t// 点击搜索框，只有disabled=true时才发出事件，因为禁止了输入，意味着是想跳转真正的搜索页\r\n\t\tclickHandler() {\r\n\t\t\tif(this.disabled) this.$emit('click');\r\n\t\t}\r\n\t}\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n@import \"../../libs/css/style.components.scss\";\r\n\r\n.u-search {\r\n\t@include vue-flex;\r\n\talign-items: center;\r\n\tflex: 1;\r\n}\r\n\r\n.u-content {\r\n\t@include vue-flex;\r\n\talign-items: center;\r\n\tpadding: 0 18rpx;\r\n\tflex: 1;\r\n}\r\n\r\n.u-clear-icon {\r\n\t@include vue-flex;\r\n\talign-items: center;\r\n}\r\n\r\n.u-input {\r\n\tflex: 1;\r\n\tfont-size: 28rpx;\r\n\tline-height: 1;\r\n\tmargin: 0 10rpx;\r\n\tcolor: $u-tips-color;\r\n}\r\n\r\n.u-close-wrap {\r\n\twidth: 40rpx;\r\n\theight: 100%;\r\n\t@include vue-flex;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n\tborder-radius: 50%;\r\n}\r\n\r\n.u-placeholder-class {\r\n\tcolor: $u-tips-color;\r\n}\r\n\r\n.u-action {\r\n\tfont-size: 28rpx;\r\n\tcolor: $u-main-color;\r\n\twidth: 0;\r\n\toverflow: hidden;\r\n\ttransition: all 0.3s;\r\n\twhite-space: nowrap;\r\n\ttext-align: center;\r\n}\r\n\r\n.u-action-active {\r\n\twidth: 80rpx;\r\n\tmargin-left: 10rpx;\r\n}\r\n</style>\r\n", "import mod from \"-!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-search.vue?vue&type=style&index=0&id=1a326067&lang=scss&scoped=true&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-search.vue?vue&type=style&index=0&id=1a326067&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754273099956\n      var cssReload = require(\"D:/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}