<template>
  <div class="build">
    <el-form ref="buildForm" :model="buildForm" :rules="buildRules" class="build-form">
      <div class="img">
        <img>
      </div>
      <div class="header">
        <h2 class="title">{{ xfmc }}</h2>
        <h3 class="title">{{ buildForm.companyName }}</h3>
        <h3 class="titleSecend">{{ buildForm.zdname }}</h3>
      </div>
      <div class="content">
        <el-form-item label="购方名称" prop="gfnsrmc" class="item">
          <el-input v-model="buildForm.gfnsrmc" placeholder="请填写购方名称" clearable style="width: 240px"/>
        </el-form-item>
        <el-form-item label="购方税号" prop="gfnsrsbh" class="item">
          <el-input v-model="buildForm.gfnsrsbh" type="text" clearable style="width: 240px" auto-complete="off" placeholder="请填写购方税号"/>
        </el-form-item>
        <el-form-item label="收票电话" prop="phone" class="item">
          <el-input v-model="buildForm.phone" type="text" clearable style="width: 240px" auto-complete="off" placeholder="请填写收票电话"/>
        </el-form-item>
        <el-form-item label="收票邮箱" prop="email" class="item" style="margin-left: 24px;">
          <el-input v-model="buildForm.email" type="text" clearable style="width: 240px" auto-complete="off" placeholder="请填写收票邮箱"/>
        </el-form-item>
        <el-form-item label="是否自然人" prop="phone" class="item" style="margin-left: 24px;">
          <el-switch v-model="buildForm.isNaturalPerson" class="switch"
            active-color="#1890ff"
            inactive-color="#dcdfe6"
            active-value="1"
            inactive-value="0"></el-switch>
        </el-form-item>
  
        <div class="line">
          <span class="text">以下为非必填内容</span>
        </div>
        <el-form-item label="购方地址" prop="address" class="item" style="margin-left: 24px;">
          <el-input v-model="buildForm.address" type="text" clearable style="width: 240px" auto-complete="off" placeholder="地址"/>
        </el-form-item>
        <el-form-item label="购方电话" prop="tel" class="item" style="margin-left: 24px;">
          <el-input v-model="buildForm.tel" type="text" clearable style="width: 240px" auto-complete="off" placeholder="电话"/>
        </el-form-item>
        <el-form-item label="银行名称" prop="bankName" class="item" style="margin-left: 24px;">
          <el-input
            v-model="buildForm.bankName" type="text" clearable style="width: 240px" auto-complete="off" placeholder="开户行"/>
        </el-form-item>
        <el-form-item label="银行账号" prop="bankNumber" class="item" style="margin-left: 24px;">
          <el-input
            v-model="buildForm.bankNumber" type="text" clearable style="width: 240px" auto-complete="off" placeholder="银行账号"/>
        </el-form-item>
      </div>

      <div class="footer">
        <div class="button" style="margin-top: 10px">
          <el-button
            :loading="loading"
            :disabled="buildButton"
            size="medium"
            type="primary"
            style="width:30%;"
            @click.native.prevent="handleBuildApply"
          >
            <span v-if="!loading">提 交</span>
            <span v-else>提 交 中...</span>
          </el-button>
          <el-button :loading="loading" size="medium" type="primary" style="width:30%;" @click.native.prevent="handleReset">
            <span>重 置</span>
          </el-button>
        </div>
        <div class="support">
          <span>本页面由宽谷数电平台提供技术支持</span>
          <br>
          <span>© 2014-2024 宽谷信息</span>
        </div>
      </div>
    </el-form>
  </div>
</template>

<script>
import { getCusByQysh } from "@/api/system/cus"
import { getInfoDeptByNum } from "@/api/system/dept";
import { applyBuild } from '@/api/xxfp/bill/qrCodeBuild';
import { Toast } from 'vant'
export default {
  name: "qrCodeByTel",
  data() {
    // 验证企业名称
    var checkCusName = (rule, value, cb) => {
      // 验证企业名称的正则表达式
      const regCusName = /^[\u0391-\uFFE5A-Za-z0-9]+$/;
      if (regCusName.test(value)) {
        // 合法的企业名称
        return cb();
      }
      cb(new Error("请输入合法的企业名称"));
    };

    // 验证税号
    var checkTaxId = (rule, value, cb) => {
      // 验证税号的正则表达式
      const regTaxId = /^[A-Z0-9]{15,20}$/;
      if (regTaxId.test(value)) {
        // 合法的税号
        return cb();
      }
      cb(new Error("请输入合法的税号"));
    };

    // 验证邮箱
    var checkEmail = (rule, value, cb) => {
      // 验证邮箱的正则表达式
      const regEmail = /^([a-zA-Z0-9_-])+@([a-zA-Z0-9_-])+(\.[a-zA-Z0-9_-])+/;
      if (regEmail.test(value)) {
        // 合法的邮箱
        return cb();
      }
      cb(new Error("请输入合法的邮箱"));
    };

    // 验证手机号
    var checkMobile = (rule, value, cb) => {
      // 验证手机号的正则表达式
      const regMobile = /^(0|86|17951)?(13[0-9]|15[*********]|17[678]|18[0-9]|14[57]|19[0-9])[0-9]{8}$/;
      if (regMobile.test(value)) {
        return cb();
      }
      cb(new Error("请输入合法的手机号"));
    };
    return {
      buildForm: {
        gfnsrmc: "测试",
        gfnsrsbh: "91370100MA3ERQBT0U",
        email: "",
        phone: "***********",
        address: "",
        tel: "",
        bankName: "",
        bankNumber: "",
        isNaturalPerson: "1",
        fdh: "",
        zdname: undefined,
        deptId: undefined
      },
      buildRules: {
        gfnsrmc: [
          { required: true, trigger: "blur", message: "请输入企业名称" },
          { validator: checkCusName, trigger: "blur", message: "请输入正确的企业名称" }
        ],
        gfnsrsbh: [
          { required: true, trigger: "blur", message: "请输入企业税号" },
          { validator: checkTaxId, trigger: "blur", message: "请输入正确的企业税号" }
        ],
        phone: [
          { required: true, trigger: "blur", message: "请输入收票电话" },
          { validator: checkMobile, trigger: "blur", message: "请输入正确的收票电话" }
        ]
      },
      loading: false,
      order:{// 订单
        number: undefined,// 订单号
      },
      consumeTime: undefined,// 消费时间
      consumeAmount: undefined,// 消费金额
      billInfoId: undefined,// 单据表id
      buildButton: false,// 开具按钮
      billStatus: undefined,// 票据状态
      isRed: undefined,// 是否冲红
      xfmc: undefined,// 销方名称
    };
  },
  watch: {
    $route: {
      handler: function(route) {
        this.redirect = route.query && route.query.redirect;
      },
      immediate: true
    }
  },
  created() {
    let xfsh = this.$route.query.xfsh;
    let fdh = this.$route.query.fdh;
    this.buildForm.salerTaxNum = xfsh;
    this.buildForm.fdh = fdh;
    getCusByQysh(xfsh).then(res => {
      if (res.code == "200") {
        this.xfmc = res.data.cusName;
      }
    })
    getInfoDeptByNum(fdh).then(res => {
      if (res.code == "200") {
        this.buildForm.zdname = res.data.deptName;
        this.buildForm.deptId = res.data.deptId;
      }
    }).catch(err => {
      console.log(err);
    });
  },
  methods: {
    handleBuildApply() {
      this.$refs["buildForm"].validate(valid => {
        if (valid) {
          this.buildButton = true;
          var email = this.buildForm.email;
          if (email) {
            const regEmail = /^([a-zA-Z0-9_-])+@([a-zA-Z0-9_-])+(\.[a-zA-Z0-9_-])+/;
            if (!regEmail.test(email)) {
              // 合法的邮箱
              Toast('邮箱不合法,请检查')
              return false;
            }
          }
  
          this.buildForm.number = this.order.number;
          this.buildForm.billInfoId = this.billInfoId;
          Toast.loading({
            mask: true,
            message: '加载中...'
          });
          applyBuild(this.buildForm).then(res => {
            console.log(res);
            if (res.code == "200") {
              if (res.data) {
                var data = res.data;
                if (data.code == "500") {
                  this.$router.push({ path: "/qrCodeBuildEnd?msg=" + data.msg }).catch(()=>{});
                }
              } else {
                this.$router.push({ path: "/qrCodeBuildEnd?msg=开票申请已提交,请等待前台完成开票" }).catch(()=>{});
              }
            } else {
              this.$router.push({ path: "/qrCodeBuildEnd?msg=" + data.msg }).catch(()=>{});
            }
            Toast.clear();
          }).catch(res => {
            Toast.clear();
            this.buildButton = true;
          })
        }
      });
    },
    handleReset() {
      this.resetForm("buildForm");
    }
  }
};
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.build {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  background-color: white;
  background-size: cover;
}
.build-form {
  height: 100vh;
  // background: #ffffff;
  background-size: cover; /* 确保图片覆盖整个元素 */
  background-position: center; /* 将图片居中显示 */
  background-image: url("../assets/images/shortHeader.png");
  width: 100%;
  padding: 0 15px;
  .img {
    text-align: center;
    height: 14vh;
    img {
      height: 14vh;
    }
  }
  .el-input {
    height: 38px;
    input {
      height: 38px;
    }
  }
  .el-form-item {
    margin-bottom: 16px;
    ::v-deep .el-form-item__label::before {
      color: #FFE7CE;
    }
    ::v-deep .el-form-item__error {
      color: #FFE7CE;
    }
  }
  .input-icon {
    height: 39px;
    width: 14px;
    margin-left: 2px;
  }
}
.header {
  height: 5vh;
  margin: 10px 0 20px 0;
  .title {
    margin: 0px auto 5px auto;
    text-align: center;
    color: #FFE7CE;
    font-size: 24px;
    font-weight: 700;
  }
  .titleSecend {
     margin: 10px auto 5px auto;
     text-align: center;
     color: #FFE7CE;
  }
}
.content {
  margin-top: 5vh;
  height: 65vh;
  overflow: auto;
  .item {
    margin-left: 15px;
    ::v-deep .el-form-item__label {
      color: #FFE7CE;
      font-size: 16px;
    }
  }
  .switch ::v-deep .el-switch__core {
    background-color: #FE9F3C !important;
    border-color: #FE9F3C !important;
  }
  .line {
    height:0;
    border-top:1px solid #FFE7CE;
    text-align:center;
    margin-bottom: 20px;
    margin: 20px auto 20px auto;
  }
  .text {
    position:relative;
    top:-14px;
    background-color:#E50000;
    color: #FFE7CE;
  }
  .button {
    width: 100%;
    text-align: center;
    .el-button--primary {
      background-color: #FFE7CE;
    }
  }
}
.footer {
  width: 100%;
  height: 15vh;
  text-align: center;
  color: #fff;
  position: absolute;
  bottom: 0;
  left: 0;
  transition: bottom 0.3s; /* 添加过渡效果 */
  .button {
    width: 100%;
    text-align: center;
    .el-button--primary {
      background-color: #FE9F3C;
      border-color: #FE9F3C;
      font-size: 16px;
    }
    .el-button--primary.is-disabled, .el-button--primary.is-disabled:hover {
      background-color: #EBB563;
      border-color: #EBB563;
    }
  }
  .support {
    width: 100%;
    position: absolute;
    text-align: center;
    bottom: 5px;
  }
}
</style>
