{"version": 3, "sources": ["webpack:///E:/Home/ma-Yi/gold/pages/myAgent/mescroll-swiper-item.vue?bdbc", "webpack:///E:/Home/ma-Yi/gold/pages/myAgent/mescroll-swiper-item.vue?2188", "webpack:///E:/Home/ma-Yi/gold/pages/myAgent/mescroll-swiper-item.vue?33ef", "webpack:///E:/Home/ma-Yi/gold/pages/myAgent/mescroll-swiper-item.vue?360b", "uni-app:///pages/myAgent/mescroll-swiper-item.vue", "webpack:///E:/Home/ma-Yi/gold/pages/myAgent/mescroll-swiper-item.vue?4c65", "webpack:///E:/Home/ma-Yi/gold/pages/myAgent/mescroll-swiper-item.vue?e8a1"], "names": ["mixins", "data", "number", "agentId", "testphon", "auditStatus", "downOption", "auto", "upOption", "textLoading", "textNoMore", "noMoreSize", "empty", "icon", "tip", "btnText", "records", "startDeta", "endDetaL", "keyword", "props", "i", "index", "type", "default", "tabs", "height", "onShow", "methods", "search", "startDateChange", "endDateChange", "menuClick", "todetails", "uni", "url", "downCallback", "upCallback", "api", "endTime", "limit", "page", "startTime", "userId", "console", "emptyClick", "title"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAA6I;AAC7I;AACwE;AACL;AACsC;;;AAGzG;AACqM;AACrM,gBAAgB,8MAAU;AAC1B,EAAE,0FAAM;AACR,EAAE,2GAAM;AACR,EAAE,oHAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,+GAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,iVAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjCA;AAAA;AAAA;AAAA;AAA8wB,CAAgB,myBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;AC+ElyB;AAGA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAHA;AACA;AAAA,gBAKA;EACAA;EAAA;EACAC;IACA;MACAC;MAAA;MACAC;MACAC;MACAC;MACAC;QACAC;MACA;;MACAC;QACAD;QAAA;QACAE;QAAA;QACAC;QAAA;QACA;QACA;QACA;QACA;QACAC;QAAA;QACAC;UACA;UACAC;UACAC;UAAA;UACAC;QACA;MACA;MACAC;MAAA;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACAC;IAAA;IACAC;MAAA;MACAC;MACAC;QACA;MACA;IACA;IACAC;MAAA;MACAF;MACAC;QACA;MACA;IACA;IACAE;EACA;EACAC;IACA;;IAEA;IACA;;IAEA;EAAA,CACA;EACAC;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACA;IAGA;IAEA;IACAC;MACAC;QACAC;MACA;IACA;IACA,YACAC;MACA;MACA;MACA;MACA;IACA;IACA,wDACAC;MAAA;MAEA;MACA;MACA;MACA;QACA;QACA;MACA;MACA;MACA;MACA;MACA;MACA,uBACAC;QACAC;QACAC;QACAC;QACAC;QACAnB;QACAoB;MAEA,GACA,OACA;QACAC;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;QACA;QACA;MACA;IACA;IACA;IACAC;MACAX;QACAY;MACA;IACA;EACA;AACA;AAAA,4B;;;;;;;;;;;;;AC3NA;AAAA;AAAA;AAAA;AAAi9C,CAAgB,86CAAG,EAAC,C;;;;;;;;;;;ACAr+C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/myAgent/mescroll-swiper-item.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./mescroll-swiper-item.vue?vue&type=template&id=0c8b0b6f&scoped=true&\"\nvar renderjs\nimport script from \"./mescroll-swiper-item.vue?vue&type=script&lang=js&\"\nexport * from \"./mescroll-swiper-item.vue?vue&type=script&lang=js&\"\nimport style0 from \"./mescroll-swiper-item.vue?vue&type=style&index=0&id=0c8b0b6f&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"0c8b0b6f\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/myAgent/mescroll-swiper-item.vue\"\nexport default component.exports", "export * from \"-!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./mescroll-swiper-item.vue?vue&type=template&id=0c8b0b6f&scoped=true&\"", "var components\ntry {\n  components = {\n    mescrollUni: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/mescroll-uni/components/mescroll-uni/mescroll-uni\" */ \"@/uni_modules/mescroll-uni/components/mescroll-uni/mescroll-uni.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./mescroll-swiper-item.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./mescroll-swiper-item.vue?vue&type=script&lang=js&\"", "<template>\r\n\r\n\t<!-- top的高度等于悬浮菜单tabs的高度 -->\r\n\t<mescroll-uni @init=\"mescrollInit\" :height=\"height\" :down=\"downOption\" @down=\"downCallback\" :up=\"upOption\"\r\n\t\t@up=\"upCallback\">\r\n\t\t<!-- <view class=\"Goos-List\"> -->\r\n\t\t\t<view class=\"deliver-query\" v-if=\"keyword=='1'\">\r\n\t\t\t\t<view class=\"deliver-time\" ontap=\"showPopup\">\r\n\r\n\t\t\t\t\t<image src=\"/static/img/index/query_time.png\" style=\"width:28rpx;height:28rpx;\"></image>\r\n\t\t\t\t\t<picker mode=\"date\" :value=\"startDeta\" :start=\"startTime\" :end=\"endTime\" @change=\"startDateChange\">\r\n\t\t\t\t\t\t<view class=\"picker\">\r\n\t\t\t\t\t\t\t{{startDeta}}\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</picker>\r\n\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"line\">至</view>\r\n\t\t\t\t<view class=\"deliver-time\" ontap=\"showPopup\">\r\n\r\n\t\t\t\t\t<image src=\"/static/img/index/query_time.png\" style=\"width:28rpx;height:28rpx;\"></image>\r\n\t\t\t\t\t<picker mode=\"date\" :value=\"endTime\" :start=\"startDeta\" :end=\"endTime\" @change=\"endDateChange\">\r\n\t\t\t\t\t\t<view class=\"picker\">\r\n\t\t\t\t\t\t\t{{endDetaL}}\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</picker>\r\n\t\t\t\t</view>\r\n\t\t\t\t<button class=\"queryBut\" @click=\"search\">查询</button>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"list-item\" v-for=\"(item,index) in records\" @click=\"todetails(item)\">\r\n\t\t\t\t<view class=\"list-deta\">{{item.days}}</view>\r\n\t\t\t\t<view class=\"list-center\">\r\n\t\t\t\t\t<view class=\"center-left\">\r\n\t\t\t\t\t\t<view class=\"center-title\">交易金额</view>\r\n\t\t\t\t\t\t<view class=\"center-title\">{{item.trxAmt}}元</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"center-left\">\r\n\t\t\t\t\t\t<view class=\"center-title\">收益</view>\r\n\t\t\t\t\t\t<view class=\"center-title\">{{item.syAmt}}元</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"list-bottom\">\r\n\t\t\t\t\t<view class=\"bottom-left\">\r\n\t\t\t\t\t\t<view class=\"bottom-item\">\r\n\t\t\t\t\t\t\t<view class=\"bottom-title\">新增会员</view>\r\n\t\t\t\t\t\t\t<view class=\"bottom-number\">{{item.addUser}}</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"bottom-item\">\r\n\t\t\t\t\t\t\t<view class=\"bottom-title\">激活数</view>\r\n\t\t\t\t\t\t\t<view class=\"bottom-number\">{{item.jihuoNum}}</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"bottom-item\">\r\n\t\t\t\t\t\t\t<view class=\"bottom-title\">达标奖励</view>\r\n\t\t\t\t\t\t\t<view class=\"bottom-number\">{{item.dabiaoAmt}}</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"border\"></view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"bottom-left\">\r\n\t\t\t\t\t\t<view class=\"bottom-item\">\r\n\t\t\t\t\t\t\t<view class=\"bottom-title\">分润奖励</view>\r\n\t\t\t\t\t\t\t<view class=\"bottom-number\">{{item.fenrunAmt}}</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"bottom-item\">\r\n\t\t\t\t\t\t\t<view class=\"bottom-title\">激活奖励</view>\r\n\t\t\t\t\t\t\t<view class=\"bottom-number\">{{item.activationAmt}}</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"bottom-item\">\r\n\t\t\t\t\t\t\t<view class=\"bottom-title\">推荐奖励</view>\r\n\t\t\t\t\t\t\t<view class=\"bottom-number\">{{item.recommendAmt}}</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\r\n\t\t<!-- </view> -->\r\n\t</mescroll-uni>\r\n</template>\r\n\r\n<script>\r\n\timport number from \"../../utils/number.js\";\r\n\tconst api = require('../../config/api');\r\n\tconst util = require('../../utils/util');\r\n\timport MescrollMixin from \"@/uni_modules/mescroll-uni/components/mescroll-uni/mescroll-mixins.js\";\r\n\timport MescrollMoreItemMixin from \"@/uni_modules/mescroll-uni/components/mescroll-uni/mixins/mescroll-more-item.js\";\r\n\r\n\r\n\texport default {\r\n\t\tmixins: [MescrollMixin, MescrollMoreItemMixin], // 注意此处还需使用MescrollMoreItemMixin (必须写在MescrollMixin后面)\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tnumber: number, //声明number属性并赋值为引入的number模块\r\n\t\t\t\tagentId: '',\r\n\t\t\t\ttestphon: '',\r\n\t\t\t\tauditStatus: '',\r\n\t\t\t\tdownOption: {\r\n\t\t\t\t\tauto: true // false不自动加载 (mixin已处理第一个tab触发downCallback)\r\n\t\t\t\t},\r\n\t\t\t\tupOption: {\r\n\t\t\t\t\tauto: false, // 不自动加载\r\n\t\t\t\t\ttextLoading: '加载中 ...', // 加载中的提示文本\r\n\t\t\t\t\ttextNoMore: '暂无更多', // 没有更多数据的提示文本\r\n\t\t\t\t\t// page: {\r\n\t\t\t\t\t// \tnum: 0, // 当前页码,默认0,回调之前会加1,即callback(page)会从1开始\r\n\t\t\t\t\t// \tsize: 10 // 每页数据的数量\r\n\t\t\t\t\t// },\r\n\t\t\t\t\tnoMoreSize: 4, //如果列表已无数据,可设置列表的总数量要大于半页才显示无更多数据;避免列表数据过少(比如只有一条数据),显示无更多数据会不好看; 默认5\r\n\t\t\t\t\tempty: {\r\n\t\t\t\t\t\t// use: false,\r\n\t\t\t\t\t\ticon: \"\",\r\n\t\t\t\t\t\ttip: '~ 暂无数据 ~', // 提示\r\n\t\t\t\t\t\tbtnText: ''\r\n\t\t\t\t\t}\r\n\t\t\t\t},\r\n\t\t\t\trecords: [], //列表数据\r\n\t\t\t\tstartDeta: '开始时间',\r\n\t\t\t\tendDetaL: '结束时间',\r\n\t\t\t\tkeyword: ''\r\n\t\t\t}\r\n\t\t},\r\n\t\tprops: {\r\n\t\t\ti: Number, // 每个tab页的专属下标 (除了支付宝小程序必须在这里定义, 其他平台都可不用写, 因为已在MescrollMoreItemMixin定义)\r\n\t\t\tindex: { // 当前tab的下标 (除了支付宝小程序必须在这里定义, 其他平台都可不用写, 因为已在MescrollMoreItemMixin定义)\r\n\t\t\t\ttype: Number,\r\n\t\t\t\tdefault () {\r\n\t\t\t\t\treturn 0\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\ttabs: { // 为了请求数据,演示用,可根据自己的项目判断是否要传\r\n\t\t\t\ttype: Array,\r\n\t\t\t\tdefault () {\r\n\t\t\t\t\treturn []\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\theight: [Number, String] // mescroll的高度\r\n\t\t},\r\n\t\tonShow() {\r\n\t\t\t// console.log(this.agentId);\r\n\r\n\t\t\t// this.testphon = uni.getStorageSync('phone')\r\n\t\t\t// this.downCallback();\r\n\t\t\t\r\n\t\t\t// console.log(this.testphon);\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tsearch(){\r\n\t\t\t\tthis.downCallback();\r\n\t\t\t},\r\n\t\t\tstartDateChange: function(e) {\r\n\t\t\t\tthis.startDeta = e.detail.value\r\n\t\t\t},\r\n\t\t\tendDateChange: function(e) {\r\n\t\t\t\tthis.endDetaL = e.detail.value\r\n\t\t\t},\r\n\t\t\tmenuClick(index) {\r\n\t\t\t\tthis.auditStatus = index\r\n\t\t\t\tthis.downCallback();\r\n\r\n\r\n\t\t\t},\r\n\r\n\t\t\t// 代理商详情\r\n\t\t\ttodetails(item) {\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: '/pages/indexChild/GoodsDetails/GoodsDetails?goodsId=' + item.id\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t/*下拉刷新的回调 */\r\n\t\t\tdownCallback() {\r\n\t\t\t\t// 这里加载你想下拉刷新的数据, 比如刷新轮播数据\r\n\t\t\t\t// loadSwiper();\r\n\t\t\t\t// 下拉刷新的回调,默认重置上拉加载列表为第一页 (自动执行 page.num=1, 再触发upCallback方法 )\r\n\t\t\t\tthis.mescroll.resetUpScroll()\r\n\t\t\t},\r\n\t\t\t/*上拉加载的回调: 其中page.num:当前页 从1开始, page.size:每页数据条数,默认10 */\r\n\t\t\tupCallback(page) {\r\n\t\t\t\r\n\t\t\t\t//联网加载数据\r\n\t\t\t\t// this.mescroll.endErr();\r\n\t\t\t\tthis.keyword = this.tabs[this.i].dictValue\r\n\t\t\t\tif(this.keyword != 1){\r\n\t\t\t\t\tthis.startDeta = '开始时间'\r\n\t\t\t\t\tthis.endDetaL =  '结束时间'\r\n\t\t\t\t}\r\n\t\t\t\tlet user = uni.getStorageSync('agentInfo')\r\n\t\t\t\tlet keyword = this.tabs[this.i].dictValue\r\n\t\t\t\tlet pageNum = page.num; // 页码, 默认从1开始\r\n\t\t\t\tlet pageSize = page.size; // 页长, 默认每页10条\r\n\t\t\t\tconst res = util.request(\r\n\t\t\t\t\tapi.skUrl, {\r\n\t\t\t\t\t\tendTime: this.endDetaL=='结束时间'?'':this.endDetaL,\r\n\t\t\t\t\t\tlimit: 10,\r\n\t\t\t\t\t\tpage: 1,\r\n\t\t\t\t\t\tstartTime: this.startDeta=='开始时间'?'':this.startDeta,\r\n\t\t\t\t\t\ttype: keyword,\r\n\t\t\t\t\t\tuserId: user.id,\r\n\r\n\t\t\t\t\t},\r\n\t\t\t\t\t'POST'\r\n\t\t\t\t).then(res => {\r\n\t\t\t\t\tconsole.log(res);\r\n\t\t\t\t\t//联网成功的回调,隐藏下拉刷新和上拉加载的状态;\r\n\t\t\t\t\tlet totalPage = res.data.pages;\r\n\t\t\t\t\tthis.mescroll.endByPage(res.data.records.length, totalPage); //必传参数(当前页的数据个数, 总页数)\r\n\t\t\t\t\t// this.mescroll.endSuccess(res.data.records.length);\r\n\t\t\t\t\t//设置列表数据\r\n\t\t\t\t\tif (page.num == 1) this.records = []; //如果是第一页需手动制空列表\r\n\t\t\t\t\tthis.records = this.records.concat(res.data.records); //追加新数据\r\n\t\t\t\t}).catch(() => {\r\n\t\t\t\t\t//联网失败, 结束加载\r\n\t\t\t\t\tthis.mescroll.endErr();\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t//点击空布局按钮的回调\r\n\t\t\temptyClick() {\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '点击了按钮,具体逻辑自行实现'\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n\t// .Goos-List {\r\n\t// \tdisplay: flex;\r\n\t// \tflex-wrap: wrap;\r\n\t// \tjustify-content: space-between;\r\n\t// \tpadding: 0 10px;\r\n\t// \tmargin-bottom: 10px;\r\n\t// }\r\n\r\n\t// .Goos-List-item {\r\n\t// \twidth: 48%;\r\n\t// \tbackground-color: #fff;\r\n\t// \tborder-radius: 6px;\r\n\t// \tmargin-top: 10px;\r\n\t// \tbox-shadow: 1px 1px 3px 2px rgba(0, 0, 0, 0.04);\r\n\r\n\t// \t.item-img {\r\n\t// \t\twidth: 100%;\r\n\t// \t\theight: 375rpx;\r\n\t// \t\timage{\r\n\t// \t\t\twidth: 100%;\r\n\t// \t\t\theight: 100%;\r\n\t// \t\t}\r\n\t// \t}\r\n\t// }\r\n\r\n\t.goodsname {\r\n\t\tfont-size: 28rpx;\r\n\t\tpadding: 15rpx 30rpx 0 30rpx;\r\n\t}\r\n\r\n\t.goodsprice {\r\n\t\tpadding: 0 30rpx 10rpx 30rpx;\r\n\t}\r\n\r\n\t.goodsprice>text:first-child {\r\n\t\tcolor: #E30F06;\r\n\t\tfont-size: 22rpx;\r\n\t}\r\n\r\n\t.goodsprice>text:last-child {\r\n\t\tcolor: #E30F06;\r\n\t\tfont-size: 28rpx;\r\n\t}\r\n\r\n\t.list-item {\r\n\t\tpadding: 0 30rpx;\r\n\t\tborder-bottom: 10rpx solid #eee;\r\n\t}\r\n\r\n\t.list-deta {\r\n\t\tfont-size: 30rpx;\r\n\t\tpadding: 25rpx 0;\r\n\t}\r\n\r\n\t.list-center {\r\n\t\t// background-color: #FF445B;\r\n\t\tbackground-color: #6A9FFB;\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: space-around;\r\n\t\talign-items: center;\r\n\t\tpadding: 20rpx 0;\r\n\t\tborder-radius: 10rpx;\r\n\t}\r\n\r\n\t.center-left {\r\n\t\ttext-align: center;\r\n\t}\r\n\r\n\t.center-title {\r\n\t\tfont-size: 30rpx;\r\n\t\tcolor: #ffffff;\r\n\t\tpadding: 5rpx 0;\r\n\t}\r\n\r\n\t.list-bottom {\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: space-between;\r\n\t\talign-items: center;\r\n\t\tpadding-top: 25rpx;\r\n\t}\r\n\r\n\t.bottom-left {\r\n\t\twidth: 46%;\r\n\t\tposition: relative;\r\n\t}\r\n\r\n\t.border {\r\n\t\tposition: absolute;\r\n\t\tright: -30rpx;\r\n\t\ttop: 10rpx;\r\n\t\twidth: 5rpx;\r\n\t\theight: 160rpx;\r\n\t\tbackground-color: #eee;\r\n\t}\r\n\r\n\t.bottom-item {\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: space-between;\r\n\t\talign-content: center;\r\n\t\tpadding-bottom: 15rpx;\r\n\t}\r\n\r\n\t.box {\r\n\t\r\n\t\tmargin-bottom: 30rpx;\r\n\t}\r\n\r\n\t.van-tabs__wrap {\r\n\t\tborder-bottom: 5px solid #eee;\r\n\t}\r\n\r\n\t.deliver-query {\r\n\t\tpadding-top: 20rpx;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: space-between;\r\n\t\toverflow: hidden;\r\n\t\twidth: 690rpx;\r\n\t\tmargin: 0rpx auto;\r\n\t}\r\n\r\n\t.deliver-time {\r\n\t\tcolor: #AEAEAE;\r\n\t\tfont-size: 26rpx;\r\n\t\twidth: 35%;\r\n\t\theight: 62rpx;\r\n\t\tline-height: 62rpx;\r\n\t\ttext-align: center;\r\n\t\tbackground: white;\r\n\t\t/* border: 1px solid #AEAEAE; */\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\tborder-radius: 10rpx;\r\n\t\tborder: 1rpx solid #f5f5ff;\r\n\t}\r\n\r\n\t.deliver-time>text {\r\n\t\tpadding-left: 10rpx;\r\n\t}\r\n\r\n\t.line {\r\n\t\tcolor: #D2D2D2;\r\n\t\tfont-size: 30rpx;\r\n\t\tpadding: 0 20rpx;\r\n\t}\r\n\r\n\t.picker {\r\n\t\tpadding-left: 15rpx;\r\n\t}\r\n\r\n\t.queryBut {\r\n\t\twidth: 100rpx;\r\n\t\theight: 62rpx;\r\n\t\tbackground-color: #6A9FFB;\r\n\t\tfont-size: 25rpx;\r\n\t\tpadding: 0rpx;\r\n\t\tcolor: #fff;\r\n\t\tline-height: 62rpx;\r\n\t\tmargin-left: 25rpx;\r\n\t}\r\n\r\n\t.queryBut::after {\r\n\t\tborder: 0;\r\n\r\n\t}\r\n\r\n\t.bottom {\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: center;\r\n\t\tpadding: 40rpx 0;\r\n\t}\r\n\r\n\t.van-divider {\r\n\t\tborder: none !important;\r\n\t}\r\n</style>", "import mod from \"-!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./mescroll-swiper-item.vue?vue&type=style&index=0&id=0c8b0b6f&lang=scss&scoped=true&\"; export default mod; export * from \"-!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./mescroll-swiper-item.vue?vue&type=style&index=0&id=0c8b0b6f&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1752590364557\n      var cssReload = require(\"D:/HBuilderX.3.3.13.20220314.full/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"../../\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}