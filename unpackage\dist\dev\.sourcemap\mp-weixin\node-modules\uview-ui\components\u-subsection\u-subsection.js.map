{"version": 3, "sources": ["webpack:///D:/work/IdeaProjects/test/pos2_hhlm/node_modules/uview-ui/components/u-subsection/u-subsection.vue?d4c0", "webpack:///D:/work/IdeaProjects/test/pos2_hhlm/node_modules/uview-ui/components/u-subsection/u-subsection.vue?eb15", "webpack:///D:/work/IdeaProjects/test/pos2_hhlm/node_modules/uview-ui/components/u-subsection/u-subsection.vue?d8e3", "webpack:///D:/work/IdeaProjects/test/pos2_hhlm/node_modules/uview-ui/components/u-subsection/u-subsection.vue?3229", "uni-app:///node_modules/uview-ui/components/u-subsection/u-subsection.vue", "webpack:///D:/work/IdeaProjects/test/pos2_hhlm/node_modules/uview-ui/components/u-subsection/u-subsection.vue?5050", "webpack:///D:/work/IdeaProjects/test/pos2_hhlm/node_modules/uview-ui/components/u-subsection/u-subsection.vue?8494"], "names": ["name", "props", "list", "type", "default", "current", "activeColor", "inactiveColor", "mode", "fontSize", "animation", "height", "bold", "bgColor", "buttonColor", "vibrateShort", "data", "listInfo", "itemBgStyle", "width", "left", "backgroundColor", "transition", "currentIndex", "buttonPadding", "borderRadius", "firstTimeVibrateShort", "watch", "immediate", "handler", "created", "val", "computed", "noBorderRight", "textStyle", "style", "itemStyle", "subsectionStyle", "itemBarStyle", "mounted", "setTimeout", "methods", "changeSectionStatus", "uni", "click", "getTabsInfo", "view", "res", "itemBgLeft"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAqI;AACrI;AACgE;AACL;AACsC;;;AAGjG;AACwL;AACxL,gBAAgB,sLAAU;AAC1B,EAAE,kFAAM;AACR,EAAE,mGAAM;AACR,EAAE,4GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,uGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AChCA;AAAA;AAAA;AAAA;AAAktB,CAAgB,mqBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACWtuB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAjBA,gBAkBA;EACAA;EACAC;IACA;IACAC;MACAC;MACAC;QACA;MACA;IACA;IACA;IACAC;MACAF;MACAC;IACA;IACA;IACAE;MACAH;MACAC;IACA;IACA;IACAG;MACAJ;MACAC;IACA;IACA;IACAI;MACAL;MACAC;IACA;IACA;IACAK;MACAN;MACAC;IACA;IACA;IACAM;MACAP;MACAC;IACA;IACA;IACAO;MACAR;MACAC;IACA;IACA;IACAQ;MACAT;MACAC;IACA;IACA;IACAS;MACAV;MACAC;IACA;IACA;IACAU;MACAX;MACAC;IACA;IACA;IACAW;MACAZ;MACAC;IACA;EACA;EACAY;IACA;MACAC;MACAC;QACAC;QACAC;QACAC;QACAV;QACAW;MACA;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;IACA;EACA;;EACAC;IACAtB;MACAuB;MACAC;QACA;QACA;MACA;IACA;EACA;EACAC;IACA;IACA;IACA;MACA;QACA;UACAX;UACAnB;QACA;QACA;MACA;QACA+B;QACA;MACA;IACA;EACA;EACAC;IACA;IACAC;MAAA;MACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;IACA;IACA;IACAC;MAAA;MACA;QACA;QACA;QACA;UACA;YACAC;UACA;YACAA;UACA;QACA;UACA;YACAA;UACA;YACAA;UACA;QACA;QACA;QACA;QACA;QACAA;QACA;MACA;IACA;IACA;IACAC;MAAA;MACA;QACA;QACA;UACA;UACAD;UACAA;UACAA;QACA;QACA;MACA;IACA;IACA;IACAE;MACA;MACAF;MACA;QACAA;QACAA;QACAA;MACA;MACA;IACA;IACA;IACAG;MACA;MACAH;MACAA;MACA;QACAA;QACAA;QACAA;QACAA;QACAA;MACA;MACA;IACA;EACA;EACAI;IAAA;IACAC;MACA;IACA;EACA;EACAC;IACA;IACAC;MAAA;MACA;QACA;QACA;UACA;QACA;QACA;UACA;QACA;QACA;UACA;QACA;MACA;MACA;MACAF;QACA;MACA;MACA;QACA;;QAEAG;MAEA;MACA;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;MACA;IACA;IACA;IACAC;MAAA;MACA;MACA;QACAC;MACA;MACAA;QACA;UACAN;YACA;YACA;UACA;QACA;QACA;QACAO;UACA;QACA;QACA;QACA;UACA;QACA;UACA;QACA;QACA;QACA;MACA;IACA;IACAC;MAAA;MACA;MACA;QACA;MACA;QACA;MACA;MACA;MACA;MACA;QACA;MACA;MACA;MACA;QACA;MACA;QACA;MACA;IACA;EACA;AACA;AAAA,4B;;;;;;;;;;;;;AC7SA;AAAA;AAAA;AAAA;AAAy2C,CAAgB,8sCAAG,EAAC,C;;;;;;;;;;;ACA73C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "node-modules/uview-ui/components/u-subsection/u-subsection.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./u-subsection.vue?vue&type=template&id=244377f2&scoped=true&\"\nvar renderjs\nimport script from \"./u-subsection.vue?vue&type=script&lang=js&\"\nexport * from \"./u-subsection.vue?vue&type=script&lang=js&\"\nimport style0 from \"./u-subsection.vue?vue&type=style&index=0&id=244377f2&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../software/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"244377f2\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"node_modules/uview-ui/components/u-subsection/u-subsection.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../software/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../software/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../software/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../software/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../software/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../software/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-subsection.vue?vue&type=template&id=244377f2&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var s0 = _vm.__get_style([_vm.subsectionStyle])\n  var l0 = _vm.__map(_vm.listInfo, function (item, index) {\n    var $orig = _vm.__get_orig(item)\n    var s1 = _vm.__get_style([_vm.itemStyle(index)])\n    var m0 = _vm.noBorderRight(index)\n    var s2 = _vm.__get_style([_vm.textStyle(index)])\n    return {\n      $orig: $orig,\n      s1: s1,\n      m0: m0,\n      s2: s2,\n    }\n  })\n  var s3 = _vm.__get_style([_vm.itemBarStyle])\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        s0: s0,\n        l0: l0,\n        s3: s3,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../software/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../software/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../software/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../software/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../software/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-subsection.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../software/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../software/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../software/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../software/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../software/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-subsection.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"u-subsection\" :style=\"[subsectionStyle]\">\r\n\t\t<view class=\"u-item u-line-1\" :style=\"[itemStyle(index)]\" @tap=\"click(index)\" :class=\"[noBorderRight(index), 'u-item-' + index]\"\r\n\t\t v-for=\"(item, index) in listInfo\" :key=\"index\">\r\n\t\t\t<view :style=\"[textStyle(index)]\" class=\"u-item-text u-line-1\">{{ item.name }}</view>\r\n\t\t</view>\r\n\t\t<view class=\"u-item-bg\" :style=\"[itemBarStyle]\"></view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\t/**\r\n\t * subsection 分段器\r\n\t * @description 该分段器一般用于用户从几个选项中选择某一个的场景\r\n\t * @tutorial https://www.uviewui.com/components/subsection.html\r\n\t * @property {Array} list 选项的数组，形式见上方\"基本使用\"\r\n\t * @property {String Number} current 初始化时默认选中的选项索引值（默认0）\r\n\t * @property {String} active-color 激活时的颜色，mode为subsection时固定为白色（默认#303133）\r\n\t * @property {String} inactive-color 未激活时字体的颜色，mode为subsection时无效（默认#606266）\r\n\t * @property {String} mode 模式选择，见官网\"模式选择\"说明（默认button）\r\n\t * @property {String Number} font-size 字体大小，单位rpx（默认28）\r\n\t * @property {String Number} height 组件高度，单位rpx（默认70）\r\n\t * @property {Boolean} animation 是否开启动画效果，见上方说明（默认true）\r\n\t * @property {Boolean} bold 激活选项的字体是否加粗（默认true）\r\n\t * @property {String} bg-color 组件背景颜色，mode为button时有效（默认#eeeeef）\r\n\t * @property {String} button-color 按钮背景颜色，mode为button时有效（默认#ffffff）\r\n\t * @event {Function} change 分段器选项发生改变时触发\r\n\t * @example <u-subsection active-color=\"#ff9900\"></u-subsection>\r\n\t */\r\n\texport default {\r\n\t\tname: \"u-subsection\",\r\n\t\tprops: {\r\n\t\t\t// tab的数据\r\n\t\t\tlist: {\r\n\t\t\t\ttype: Array,\r\n\t\t\t\tdefault () {\r\n\t\t\t\t\treturn [];\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t// 当前活动的tab的index\r\n\t\t\tcurrent: {\r\n\t\t\t\ttype: [Number, String],\r\n\t\t\t\tdefault: 0\r\n\t\t\t},\r\n\t\t\t// 激活的颜色\r\n\t\t\tactiveColor: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: '#303133'\r\n\t\t\t},\r\n\t\t\t// 未激活的颜色\r\n\t\t\tinactiveColor: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: '#606266'\r\n\t\t\t},\r\n\t\t\t// 模式选择，mode=button为按钮形式，mode=subsection时为分段模式\r\n\t\t\tmode: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: 'button'\r\n\t\t\t},\r\n\t\t\t// 字体大小，单位rpx\r\n\t\t\tfontSize: {\r\n\t\t\t\ttype: [Number, String],\r\n\t\t\t\tdefault: 28\r\n\t\t\t},\r\n\t\t\t// 是否开启动画效果\r\n\t\t\tanimation: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: true\r\n\t\t\t},\r\n\t\t\t// 组件的高度，单位rpx\r\n\t\t\theight: {\r\n\t\t\t\ttype: [Number, String],\r\n\t\t\t\tdefault: 70\r\n\t\t\t},\r\n\t\t\t// 激活tab的字体是否加粗\r\n\t\t\tbold: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: true\r\n\t\t\t},\r\n\t\t\t// mode=button时，组件背景颜色\r\n\t\t\tbgColor: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: '#eeeeef'\r\n\t\t\t},\r\n\t\t\t// mode = button时，滑块背景颜色\r\n\t\t\tbuttonColor: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: '#ffffff'\r\n\t\t\t},\r\n\t\t\t// 在切换分段器的时候，是否让设备震一下\r\n\t\t\tvibrateShort: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: false\r\n\t\t\t}\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tlistInfo: [],\r\n\t\t\t\titemBgStyle: {\r\n\t\t\t\t\twidth: 0,\r\n\t\t\t\t\tleft: 0,\r\n\t\t\t\t\tbackgroundColor: '#ffffff',\r\n\t\t\t\t\theight: '100%',\r\n\t\t\t\t\ttransition: ''\r\n\t\t\t\t},\r\n\t\t\t\tcurrentIndex: this.current,\r\n\t\t\t\tbuttonPadding: 3, // mode = button 时，组件的内边距\r\n\t\t\t\tborderRadius: 5, // 圆角值\r\n\t\t\t\tfirstTimeVibrateShort: true // 组件初始化时，会触发current变化，此时不应震动\r\n\t\t\t};\r\n\t\t},\r\n\t\twatch: {\r\n\t\t\tcurrent: {\r\n\t\t\t\timmediate: true,\r\n\t\t\t\thandler(nVal) {\r\n\t\t\t\t\tthis.currentIndex = nVal;\r\n\t\t\t\t\tthis.changeSectionStatus(nVal);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\t\tcreated() {\r\n\t\t\t// 将list的数据，传入listInfo数组，因为不能修改props传递的list值\r\n\t\t\t// 可以接受直接数组形式，或者数组元素为对象的形式，如：['简介', '评论'],或者[{name: '简介'}, {name: '评论'}]\r\n\t\t\tthis.listInfo = this.list.map((val, index) => {\r\n\t\t\t\tif (typeof val != 'object') {\r\n\t\t\t\t\tlet obj = {\r\n\t\t\t\t\t\twidth: 0,\r\n\t\t\t\t\t\tname: val\r\n\t\t\t\t\t};\r\n\t\t\t\t\treturn obj;\r\n\t\t\t\t} else {\r\n\t\t\t\t\tval.width = 0;\r\n\t\t\t\t\treturn val;\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t},\r\n\t\tcomputed: {\r\n\t\t\t// 设置mode=subsection时，滑块特有的样式\r\n\t\t\tnoBorderRight() {\r\n\t\t\t\treturn index => {\r\n\t\t\t\t\tif (this.mode != 'subsection') return;\r\n\t\t\t\t\tlet classs = '';\r\n\t\t\t\t\t// 不显示右边的边框\r\n\t\t\t\t\tif (index < this.list.length - 1) classs += ' u-none-border-right';\r\n\t\t\t\t\t// 显示整个组件的左右边圆角\r\n\t\t\t\t\tif (index == 0) classs += ' u-item-first';\r\n\t\t\t\t\tif (index == this.list.length - 1) classs += ' u-item-last';\r\n\t\t\t\t\treturn classs;\r\n\t\t\t\t};\r\n\t\t\t},\r\n\t\t\t// 文字的样式\r\n\t\t\ttextStyle() {\r\n\t\t\t\treturn index => {\r\n\t\t\t\t\tlet style = {};\r\n\t\t\t\t\t// 设置字体颜色\r\n\t\t\t\t\tif (this.mode == 'subsection') {\r\n\t\t\t\t\t\tif (index == this.currentIndex) {\r\n\t\t\t\t\t\t\tstyle.color = '#ffffff';\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tstyle.color = this.activeColor;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tif (index == this.currentIndex) {\r\n\t\t\t\t\t\t\tstyle.color = this.activeColor;\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tstyle.color = this.inactiveColor;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t\t// 字体加粗\r\n\t\t\t\t\tif (index == this.currentIndex && this.bold) style.fontWeight = 'bold';\r\n\t\t\t\t\t// 文字大小\r\n\t\t\t\t\tstyle.fontSize = this.fontSize + 'rpx';\r\n\t\t\t\t\treturn style;\r\n\t\t\t\t};\r\n\t\t\t},\r\n\t\t\t// 每个分段器item的样式\r\n\t\t\titemStyle() {\r\n\t\t\t\treturn index => {\r\n\t\t\t\t\tlet style = {};\r\n\t\t\t\t\tif (this.mode == 'subsection') {\r\n\t\t\t\t\t\t// 设置border的样式\r\n\t\t\t\t\t\tstyle.borderColor = this.activeColor;\r\n\t\t\t\t\t\tstyle.borderWidth = '1px';\r\n\t\t\t\t\t\tstyle.borderStyle = 'solid';\r\n\t\t\t\t\t}\r\n\t\t\t\t\treturn style;\r\n\t\t\t\t};\r\n\t\t\t},\r\n\t\t\t// mode=button时，外层view的样式\r\n\t\t\tsubsectionStyle() {\r\n\t\t\t\tlet style = {};\r\n\t\t\t\tstyle.height = uni.upx2px(this.height) + 'px';\r\n\t\t\t\tif (this.mode == 'button') {\r\n\t\t\t\t\tstyle.backgroundColor = this.bgColor;\r\n\t\t\t\t\tstyle.padding = `${this.buttonPadding}px`;\r\n\t\t\t\t\tstyle.borderRadius = `${this.borderRadius}px`;\r\n\t\t\t\t}\r\n\t\t\t\treturn style;\r\n\t\t\t},\r\n\t\t\t// 滑块的样式\r\n\t\t\titemBarStyle() {\r\n\t\t\t\tlet style = {};\r\n\t\t\t\tstyle.backgroundColor = this.activeColor;\r\n\t\t\t\tstyle.zIndex = 1;\r\n\t\t\t\tif (this.mode == 'button') {\r\n\t\t\t\t\tstyle.backgroundColor = this.buttonColor;\r\n\t\t\t\t\tstyle.borderRadius = `${this.borderRadius}px`;\r\n\t\t\t\t\tstyle.bottom = `${this.buttonPadding}px`;\r\n\t\t\t\t\tstyle.height = uni.upx2px(this.height) - this.buttonPadding * 2 + 'px';\r\n\t\t\t\t\tstyle.zIndex = 0;\r\n\t\t\t\t}\r\n\t\t\t\treturn Object.assign(this.itemBgStyle, style);\r\n\t\t\t}\r\n\t\t},\r\n\t\tmounted() {\r\n\t\t\tsetTimeout(() => {\r\n\t\t\t\tthis.getTabsInfo();\r\n\t\t\t}, 10);\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\t// 改变滑块的样式\r\n\t\t\tchangeSectionStatus(nVal) {\r\n\t\t\t\tif (this.mode == 'subsection') {\r\n\t\t\t\t\t// 根据滑块在最左边和最右边时，显示左边和右边的圆角\r\n\t\t\t\t\tif (nVal == this.list.length - 1) {\r\n\t\t\t\t\t\tthis.itemBgStyle.borderRadius = `0 ${this.buttonPadding}px ${this.buttonPadding}px 0`;\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif (nVal == 0) {\r\n\t\t\t\t\t\tthis.itemBgStyle.borderRadius = `${this.buttonPadding}px 0 0 ${this.buttonPadding}px`;\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif (nVal > 0 && nVal < this.list.length - 1) {\r\n\t\t\t\t\t\tthis.itemBgStyle.borderRadius = '0';\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\t// 更新滑块的位置\r\n\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\tthis.itemBgLeft();\r\n\t\t\t\t}, 10);\r\n\t\t\t\tif (this.vibrateShort && !this.firstTimeVibrateShort) {\r\n\t\t\t\t\t// 使手机产生短促震动，微信小程序有效，APP(HX 2.6.8)和H5无效\r\n\t\t\t\t\t// #ifndef H5\r\n\t\t\t\t\tuni.vibrateShort();\r\n\t\t\t\t\t// #endif\r\n\t\t\t\t}\r\n\t\t\t\t// 第一次过后，设置firstTimeVibrateShort为false，让其下一次可以震动(如果允许震动的话)\r\n\t\t\t\tthis.firstTimeVibrateShort = false;\r\n\t\t\t},\r\n\t\t\tclick(index) {\r\n\t\t\t\t// 不允许点击当前激活选项\r\n\t\t\t\tif (index == this.currentIndex) return;\r\n\t\t\t\tthis.currentIndex = index;\r\n\t\t\t\tthis.changeSectionStatus(index);\r\n\t\t\t\tthis.$emit('change', Number(index));\r\n\t\t\t},\r\n\t\t\t// 获取各个tab的节点信息\r\n\t\t\tgetTabsInfo() {\r\n\t\t\t\tlet view = uni.createSelectorQuery().in(this);\r\n\t\t\t\tfor (let i = 0; i < this.list.length; i++) {\r\n\t\t\t\t\tview.select('.u-item-' + i).boundingClientRect();\r\n\t\t\t\t}\r\n\t\t\t\tview.exec(res => {\r\n\t\t\t\t\tif (!res.length) {\r\n\t\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\t\tthis.getTabsInfo();\r\n\t\t\t\t\t\t\treturn;\r\n\t\t\t\t\t\t}, 10);\r\n\t\t\t\t\t}\r\n\t\t\t\t\t// 将分段器每个item的宽度，放入listInfo数组\r\n\t\t\t\t\tres.map((val, index) => {\r\n\t\t\t\t\t\tthis.listInfo[index].width = val.width;\r\n\t\t\t\t\t});\r\n\t\t\t\t\t// 初始化滑块的宽度\r\n\t\t\t\t\tif (this.mode == 'subsection') {\r\n\t\t\t\t\t\tthis.itemBgStyle.width = this.listInfo[0].width + 'px';\r\n\t\t\t\t\t} else if (this.mode == 'button') {\r\n\t\t\t\t\t\tthis.itemBgStyle.width = this.listInfo[0].width + 'px';\r\n\t\t\t\t\t}\r\n\t\t\t\t\t// 初始化滑块的位置\r\n\t\t\t\t\tthis.itemBgLeft();\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\titemBgLeft() {\r\n\t\t\t\t// 根据是否开启动画效果，\r\n\t\t\t\tif (this.animation) {\r\n\t\t\t\t\tthis.itemBgStyle.transition = 'all 0.35s';\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.itemBgStyle.transition = 'all 0s';\r\n\t\t\t\t}\r\n\t\t\t\tlet left = 0;\r\n\t\t\t\t// 计算当前活跃item到组件左边的距离\r\n\t\t\t\tthis.listInfo.map((val, index) => {\r\n\t\t\t\t\tif (index < this.currentIndex) left += val.width;\r\n\t\t\t\t});\r\n\t\t\t\t// 根据mode不同模式，计算滑块需要移动的距离\r\n\t\t\t\tif (this.mode == 'subsection') {\r\n\t\t\t\t\tthis.itemBgStyle.left = left + 'px';\r\n\t\t\t\t} else if (this.mode == 'button') {\r\n\t\t\t\t\tthis.itemBgStyle.left = left + this.buttonPadding + 'px';\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n\t@import \"../../libs/css/style.components.scss\";\r\n\t\r\n\t.u-subsection {\r\n\t\t@include vue-flex;\r\n\t\talign-items: center;\r\n\t\toverflow: hidden;\r\n\t\tposition: relative;\r\n\t}\r\n\r\n\t.u-item {\r\n\t\tflex: 1;\r\n\t\ttext-align: center;\r\n\t\tfont-size: 26rpx;\r\n\t\theight: 100%;\r\n\t\t@include vue-flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\tcolor: $u-main-color;\r\n\t\tpadding: 0 6rpx;\r\n\t}\r\n\r\n\t.u-item-bg {\r\n\t\tbackground-color: $u-type-primary;\r\n\t\tposition: absolute;\r\n\t\tz-index: -1;\r\n\t}\r\n\r\n\t.u-none-border-right {\r\n\t\tborder-right: none !important;\r\n\t}\r\n\r\n\t.u-item-first {\r\n\t\tborder-top-left-radius: 8rpx;\r\n\t\tborder-bottom-left-radius: 8rpx;\r\n\t}\r\n\r\n\t.u-item-last {\r\n\t\tborder-top-right-radius: 8rpx;\r\n\t\tborder-bottom-right-radius: 8rpx;\r\n\t}\r\n\r\n\t.u-item-text {\r\n\t\ttransition: all 0.35s;\r\n\t\tcolor: $u-main-color;\r\n\t\t@include vue-flex;\r\n\t\talign-items: center;\r\n\t\tposition: relative;\r\n\t\tz-index: 3;\r\n\t}\r\n</style>\r\n", "import mod from \"-!../../../../../../../../software/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../software/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../software/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../software/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../software/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../software/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../software/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../software/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../software/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-subsection.vue?vue&type=style&index=0&id=244377f2&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../../software/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../software/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../software/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../software/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../software/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../software/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../software/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../software/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../software/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-subsection.vue?vue&type=style&index=0&id=244377f2&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1752369745369\n      var cssReload = require(\"D:/software/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}