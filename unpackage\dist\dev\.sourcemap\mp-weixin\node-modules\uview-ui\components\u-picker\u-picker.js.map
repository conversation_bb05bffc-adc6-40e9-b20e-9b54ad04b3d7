{"version": 3, "sources": ["webpack:///E:/Home/ma-Yi/gold/node_modules/uview-ui/components/u-picker/u-picker.vue?047b", "webpack:///E:/Home/ma-Yi/gold/node_modules/uview-ui/components/u-picker/u-picker.vue?9957", "webpack:///E:/Home/ma-Yi/gold/node_modules/uview-ui/components/u-picker/u-picker.vue?34e7", "webpack:///E:/Home/ma-Yi/gold/node_modules/uview-ui/components/u-picker/u-picker.vue?4bf7", "uni-app:///node_modules/uview-ui/components/u-picker/u-picker.vue", "webpack:///E:/Home/ma-Yi/gold/node_modules/uview-ui/components/u-picker/u-picker.vue?ced5", "webpack:///E:/Home/ma-Yi/gold/node_modules/uview-ui/components/u-picker/u-picker.vue?637d"], "names": ["name", "props", "params", "type", "default", "year", "month", "day", "hour", "minute", "second", "province", "city", "area", "timestamp", "range", "defaultSelector", "rangeKey", "mode", "startYear", "endYear", "cancelColor", "confirmColor", "defaultTime", "defaultRegion", "showTimeTag", "areaCode", "safeAreaInsetBottom", "maskCloseAble", "value", "zIndex", "title", "cancelText", "confirmText", "data", "years", "months", "days", "hours", "minutes", "seconds", "reset", "startDate", "endDate", "valueArr", "provinces", "citys", "areas", "moving", "mounted", "computed", "props<PERSON><PERSON>e", "regionChange", "yearAndMonth", "uZIndex", "watch", "setTimeout", "methods", "pickstart", "pickend", "getItemValue", "formatNumber", "generateArray", "start", "end", "getIndex", "initTimeValue", "fdate", "time", "init", "set<PERSON>ears", "setMonths", "setDays", "index", "setHours", "setMinutes", "setSeconds", "setProvinces", "tmp", "useCode", "setCitys", "<PERSON><PERSON><PERSON><PERSON>", "close", "change", "column", "getResult", "result", "getTimestamp"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAiI;AACjI;AAC4D;AACL;AACsC;;;AAG7F;AACqM;AACrM,gBAAgB,8MAAU;AAC1B,EAAE,8EAAM;AACR,EAAE,+FAAM;AACR,EAAE,wGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,mGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,qOAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe;AACf;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC1JA;AAAA;AAAA;AAAA;AAAkwB,CAAgB,uxBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;ACkGtxB;AACA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAzBA,gBA0BA;EACAA;EACAC;IACA;IACAC;MACAC;MACAC;QACA;UACAC;UACAC;UACAC;UACAC;UACAC;UACAC;UACAC;UACAC;UACAC;UACAC;QACA;MACA;IACA;IACA;IACAC;MACAZ;MACAC;QACA;MACA;IACA;IACA;IACAY;MACAb;MACAC;QACA;MACA;IACA;IACA;IACAa;MACAd;MACAC;IACA;IACA;IACAc;MACAf;MACAC;IACA;IACA;IACAe;MACAhB;MACAC;IACA;IACA;IACAgB;MACAjB;MACAC;IACA;IACA;IACAiB;MACAlB;MACAC;IACA;IACA;IACAkB;MACAnB;MACAC;IACA;IACA;IACAmB;MACApB;MACAC;IACA;IACA;IACAoB;MACArB;MACAC;QACA;MACA;IACA;IACA;IACAqB;MACAtB;MACAC;IACA;IACA;IACAsB;MACAvB;MACAC;QACA;MACA;IACA;IACAuB;MACAxB;MACAC;IACA;IACA;IACAwB;MACAzB;MACAC;IACA;IACA;IACAyB;MACA1B;MACAC;IACA;IACA;IACA0B;MACA3B;MACAC;IACA;IACA;IACA2B;MACA5B;MACAC;IACA;IACA;IACA4B;MACA7B;MACAC;IACA;IACA;IACA6B;MACA9B;MACAC;IACA;EACA;EACA8B;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAnC;MACAC;MACAC;MACAC;MACAC;MACAC;MACA+B;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACApC;MACAC;MACAC;MACAmC;IACA;EACA;EACAC;IACA;EACA;EACAC;IACAC;MACA;MACA;IACA;IACAC;MACA;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACA;IACA;EACA;EACAC;IACAJ;MAAA;MACA;MACAK;QAAA;MAAA;IACA;IACA;IACAJ;MACA;MACA;IACA;IACA;IACA;IACAC;MACA;IACA;IACA;IACAxB;MAAA;MACA;QACA;QACA2B;UAAA;QAAA;MACA;IACA;EACA;EACAC;IACA;IACAC;MAEA;IAEA;IACA;IACAC;MAEA;IAEA;IACA;IACAC;MACA;MACA;MACA;QACA;MACA;IACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;MACAC;MACAC;MACAA;MACA;MACA;IACA;IACAC;MACA;MACA;MACA;IACA;IACA;IACAC;MACA;MACA;MACAC;MACA;MACA,uCACAC;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IACAC;MACA;MACA;MACA;QACA;QACA;UACA;UACA;QACA;QACA;UACA;UACA;QACA;QACA;UACA;UACA;QACA;QACA;UACA;UACA;QACA;QACA;UACA;UACA;QACA;QACA;UACA;UACA;QACA;MACA;QACA;UACA;UACA;QACA;QACA;UACA;UACA;QACA;QACA;UACA;UACA;QACA;MACA;QACA;MACA;QACA;QACA;MACA;MACA;IACA;IACA;IACAC;MACA;MACA;MACA;MACA;IACA;IACAC;MACA;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;MACA;MACA,0DACA,sCACA,qCACAC;MACA;MACA;MACA;MACA;IACA;IACAC;MACA;MACA;IACA;IACAC;MACA;MACA;IACA;IACAC;MACA;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;MACA;MACA;QACAC;QACAC;MACA,uEACAD;MACA;MACAjC;QACA;UACAiC;QACA;MACA;MACA;MACA;MACA;MACA;IACA;IACAE;MACA;MACA;MACA;MACA;QACAF;QACAC;MACA,uEACAD;MACAhC;QACA;UACAgC;QACA;MACA;MACA;MACA;MACA;IACA;IACAG;MACA;MACA;MACA;MACA;QACAH;QACAC;MACA,uEACAD;MACA/B;QACA;UACA+B;QACA;MACA;MACA;MACA;MACA;IACA;IACAI;MACA;IACA;IACA;IACAC;MACA;MACA;MACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;QACA;QACA;QACA;MACA;QACA;QACA;QACA;UACA;QACA;QACA;QACA;UACA;YACAC;YACAX;UACA;QACA;MACA;IACA;IACA;IACAY;MAAA;MAEA;MAEA;MACA;MACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;QACA;QACA;QACA;MACA;QACAC;MACA;QACAA;MACA;MACA;MACA;IACA;IACA;IACAC;MACA;MACA;MACA;IACA;EACA;AACA;AAAA,4B;;;;;;;;;;;;AChlBA;AAAA;AAAA;AAAA;AAAq8C,CAAgB,k6CAAG,EAAC,C;;;;;;;;;;;ACAz9C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "node-modules/uview-ui/components/u-picker/u-picker.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./u-picker.vue?vue&type=template&id=d45639b2&scoped=true&\"\nvar renderjs\nimport script from \"./u-picker.vue?vue&type=script&lang=js&\"\nexport * from \"./u-picker.vue?vue&type=script&lang=js&\"\nimport style0 from \"./u-picker.vue?vue&type=style&index=0&id=d45639b2&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"d45639b2\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"node_modules/uview-ui/components/u-picker/u-picker.vue\"\nexport default component.exports", "export * from \"-!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-picker.vue?vue&type=template&id=d45639b2&scoped=true&\"", "var components\ntry {\n  components = {\n    uPopup: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-popup/u-popup\" */ \"uview-ui/components/u-popup/u-popup.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l0 =\n    !(_vm.mode == \"region\") &&\n    _vm.mode == \"time\" &&\n    !_vm.reset &&\n    _vm.params.month\n      ? _vm.__map(_vm.months, function (item, index) {\n          var $orig = _vm.__get_orig(item)\n          var m0 = _vm.formatNumber(item)\n          return {\n            $orig: $orig,\n            m0: m0,\n          }\n        })\n      : null\n  var l1 =\n    !(_vm.mode == \"region\") &&\n    _vm.mode == \"time\" &&\n    !_vm.reset &&\n    _vm.params.day\n      ? _vm.__map(_vm.days, function (item, index) {\n          var $orig = _vm.__get_orig(item)\n          var m1 = _vm.formatNumber(item)\n          return {\n            $orig: $orig,\n            m1: m1,\n          }\n        })\n      : null\n  var l2 =\n    !(_vm.mode == \"region\") &&\n    _vm.mode == \"time\" &&\n    !_vm.reset &&\n    _vm.params.hour\n      ? _vm.__map(_vm.hours, function (item, index) {\n          var $orig = _vm.__get_orig(item)\n          var m2 = _vm.formatNumber(item)\n          return {\n            $orig: $orig,\n            m2: m2,\n          }\n        })\n      : null\n  var l3 =\n    !(_vm.mode == \"region\") &&\n    _vm.mode == \"time\" &&\n    !_vm.reset &&\n    _vm.params.minute\n      ? _vm.__map(_vm.minutes, function (item, index) {\n          var $orig = _vm.__get_orig(item)\n          var m3 = _vm.formatNumber(item)\n          return {\n            $orig: $orig,\n            m3: m3,\n          }\n        })\n      : null\n  var l4 =\n    !(_vm.mode == \"region\") &&\n    _vm.mode == \"time\" &&\n    !_vm.reset &&\n    _vm.params.second\n      ? _vm.__map(_vm.seconds, function (item, index) {\n          var $orig = _vm.__get_orig(item)\n          var m4 = _vm.formatNumber(item)\n          return {\n            $orig: $orig,\n            m4: m4,\n          }\n        })\n      : null\n  var l5 =\n    !(_vm.mode == \"region\") &&\n    !(_vm.mode == \"time\") &&\n    _vm.mode == \"selector\" &&\n    !_vm.reset\n      ? _vm.__map(_vm.range, function (item, index) {\n          var $orig = _vm.__get_orig(item)\n          var m5 = _vm.getItemValue(item, \"selector\")\n          return {\n            $orig: $orig,\n            m5: m5,\n          }\n        })\n      : null\n  var l7 =\n    !(_vm.mode == \"region\") &&\n    !(_vm.mode == \"time\") &&\n    !(_vm.mode == \"selector\") &&\n    _vm.mode == \"multiSelector\"\n      ? _vm.__map(_vm.range, function (item, index) {\n          var $orig = _vm.__get_orig(item)\n          var l6 = !_vm.reset\n            ? _vm.__map(item, function (item1, index1) {\n                var $orig = _vm.__get_orig(item1)\n                var m6 = _vm.getItemValue(item1, \"multiSelector\")\n                return {\n                  $orig: $orig,\n                  m6: m6,\n                }\n              })\n            : null\n          return {\n            $orig: $orig,\n            l6: l6,\n          }\n        })\n      : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l0: l0,\n        l1: l1,\n        l2: l2,\n        l3: l3,\n        l4: l4,\n        l5: l5,\n        l7: l7,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-picker.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-picker.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<u-popup :maskCloseAble=\"maskCloseAble\" mode=\"bottom\" :popup=\"false\" v-model=\"value\" length=\"auto\" :safeAreaInsetBottom=\"safeAreaInsetBottom\" @close=\"close\" :z-index=\"uZIndex\">\r\n\t\t<view class=\"u-datetime-picker\">\r\n\t\t\t<view class=\"u-picker-header\" @touchmove.stop.prevent=\"\">\r\n\t\t\t\t<view class=\"u-btn-picker u-btn-picker--tips\" \r\n\t\t\t\t\t:style=\"{ color: cancelColor }\" \r\n\t\t\t\t\thover-class=\"u-opacity\" \r\n\t\t\t\t\t:hover-stay-time=\"150\" \r\n\t\t\t\t\t@tap=\"getResult('cancel')\"\r\n\t\t\t\t>{{cancelText}}</view>\r\n\t\t\t\t<view class=\"u-picker__title\">{{ title }}</view>\r\n\t\t\t\t<view\r\n\t\t\t\t\tclass=\"u-btn-picker u-btn-picker--primary\"\r\n\t\t\t\t\t:style=\"{ color: moving ? cancelColor : confirmColor }\"\r\n\t\t\t\t\thover-class=\"u-opacity\"\r\n\t\t\t\t\t:hover-stay-time=\"150\"\r\n\t\t\t\t\****************=\"\"\r\n\t\t\t\t\**********=\"getResult('confirm')\"\r\n\t\t\t\t>\r\n\t\t\t\t\t{{confirmText}}\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"u-picker-body\">\r\n\t\t\t\t<picker-view v-if=\"mode == 'region'\" :value=\"valueArr\" @change=\"change\" class=\"u-picker-view\" @pickstart=\"pickstart\" @pickend=\"pickend\">\r\n\t\t\t\t\t<picker-view-column v-if=\"!reset && params.province\">\r\n\t\t\t\t\t\t<view class=\"u-column-item\" v-for=\"(item, index) in provinces\" :key=\"index\">\r\n\t\t\t\t\t\t\t<view class=\"u-line-1\">{{ item.label }}</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</picker-view-column>\r\n\t\t\t\t\t<picker-view-column v-if=\"!reset && params.city\">\r\n\t\t\t\t\t\t<view class=\"u-column-item\" v-for=\"(item, index) in citys\" :key=\"index\">\r\n\t\t\t\t\t\t\t<view class=\"u-line-1\">{{ item.label }}</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</picker-view-column>\r\n\t\t\t\t\t<picker-view-column v-if=\"!reset && params.area\">\r\n\t\t\t\t\t\t<view class=\"u-column-item\" v-for=\"(item, index) in areas\" :key=\"index\">\r\n\t\t\t\t\t\t\t<view class=\"u-line-1\">{{ item.label }}</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</picker-view-column>\r\n\t\t\t\t</picker-view>\r\n\t\t\t\t<picker-view v-else-if=\"mode == 'time'\" :value=\"valueArr\" @change=\"change\" class=\"u-picker-view\" @pickstart=\"pickstart\" @pickend=\"pickend\">\r\n\t\t\t\t\t<picker-view-column v-if=\"!reset && params.year\">\r\n\t\t\t\t\t\t<view class=\"u-column-item\" v-for=\"(item, index) in years\" :key=\"index\">\r\n\t\t\t\t\t\t\t{{ item }}\r\n\t\t\t\t\t\t\t<text class=\"u-text\" v-if=\"showTimeTag\">年</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</picker-view-column>\r\n\t\t\t\t\t<picker-view-column v-if=\"!reset && params.month\">\r\n\t\t\t\t\t\t<view class=\"u-column-item\" v-for=\"(item, index) in months\" :key=\"index\">\r\n\t\t\t\t\t\t\t{{ formatNumber(item) }}\r\n\t\t\t\t\t\t\t<text class=\"u-text\" v-if=\"showTimeTag\">月</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</picker-view-column>\r\n\t\t\t\t\t<picker-view-column v-if=\"!reset && params.day\">\r\n\t\t\t\t\t\t<view class=\"u-column-item\" v-for=\"(item, index) in days\" :key=\"index\">\r\n\t\t\t\t\t\t\t{{ formatNumber(item) }}\r\n\t\t\t\t\t\t\t<text class=\"u-text\" v-if=\"showTimeTag\">日</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</picker-view-column>\r\n\t\t\t\t\t<picker-view-column v-if=\"!reset && params.hour\">\r\n\t\t\t\t\t\t<view class=\"u-column-item\" v-for=\"(item, index) in hours\" :key=\"index\">\r\n\t\t\t\t\t\t\t{{ formatNumber(item) }}\r\n\t\t\t\t\t\t\t<text class=\"u-text\" v-if=\"showTimeTag\">时</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</picker-view-column>\r\n\t\t\t\t\t<picker-view-column v-if=\"!reset && params.minute\">\r\n\t\t\t\t\t\t<view class=\"u-column-item\" v-for=\"(item, index) in minutes\" :key=\"index\">\r\n\t\t\t\t\t\t\t{{ formatNumber(item) }}\r\n\t\t\t\t\t\t\t<text class=\"u-text\" v-if=\"showTimeTag\">分</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</picker-view-column>\r\n\t\t\t\t\t<picker-view-column v-if=\"!reset && params.second\">\r\n\t\t\t\t\t\t<view class=\"u-column-item\" v-for=\"(item, index) in seconds\" :key=\"index\">\r\n\t\t\t\t\t\t\t{{ formatNumber(item) }}\r\n\t\t\t\t\t\t\t<text class=\"u-text\" v-if=\"showTimeTag\">秒</text>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</picker-view-column>\r\n\t\t\t\t</picker-view>\r\n\t\t\t\t<picker-view v-else-if=\"mode == 'selector'\" :value=\"valueArr\" @change=\"change\" class=\"u-picker-view\" @pickstart=\"pickstart\" @pickend=\"pickend\">\r\n\t\t\t\t\t<picker-view-column v-if=\"!reset\">\r\n\t\t\t\t\t\t<view class=\"u-column-item\" v-for=\"(item, index) in range\" :key=\"index\">\r\n\t\t\t\t\t\t\t<view class=\"u-line-1\">{{ getItemValue(item, 'selector') }}</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</picker-view-column>\r\n\t\t\t\t</picker-view>\r\n\t\t\t\t<picker-view v-else-if=\"mode == 'multiSelector'\" :value=\"valueArr\" @change=\"change\" class=\"u-picker-view\" @pickstart=\"pickstart\" @pickend=\"pickend\">\r\n\t\t\t\t\t<picker-view-column v-if=\"!reset\" v-for=\"(item, index) in range\" :key=\"index\">\r\n\t\t\t\t\t\t<view class=\"u-column-item\" v-for=\"(item1, index1) in item\" :key=\"index1\">\r\n\t\t\t\t\t\t\t<view class=\"u-line-1\">{{ getItemValue(item1, 'multiSelector') }}</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</picker-view-column>\r\n\t\t\t\t</picker-view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</u-popup>\r\n</template>\r\n\r\n<script>\r\nimport provinces from '../../libs/util/province.js';\r\nimport citys from '../../libs/util/city.js';\r\nimport areas from '../../libs/util/area.js';\r\n\r\n/**\r\n * picker picker弹出选择器\r\n * @description 此选择器有两种弹出模式：一是时间模式，可以配置年，日，月，时，分，秒参数 二是地区模式，可以配置省，市，区参数\r\n * @tutorial https://www.uviewui.com/components/picker.html\r\n * @property {Object} params 需要显示的参数，见官网说明\r\n * @property {String} mode 模式选择，region-地区类型，time-时间类型（默认time）\r\n * @property {String Number} start-year 可选的开始年份，mode=time时有效（默认1950）\r\n * @property {String Number} end-year 可选的结束年份，mode=time时有效（默认2050）\r\n * @property {Boolean} safe-area-inset-bottom 是否开启底部安全区适配（默认false）\r\n * @property {Boolean} show-time-tag 时间模式时，是否显示后面的年月日中文提示\r\n * @property {String} cancel-color 取消按钮的颜色（默认#606266）\r\n * @property {String} confirm-color 确认按钮的颜色（默认#2979ff）\r\n * @property {String} default-time 默认选中的时间，mode=time时有效\r\n * @property {String} confirm-text 确认按钮的文字\r\n * @property {String} cancel-text 取消按钮的文字\r\n * @property {String} default-region 默认选中的地区，中文形式，mode=region时有效\r\n * @property {String} default-code 默认选中的地区，编号形式，mode=region时有效\r\n * @property {Boolean} mask-close-able 是否允许通过点击遮罩关闭Picker（默认true）\r\n * @property {String Number} z-index 弹出时的z-index值（默认1075）\r\n * @property {Array} default-selector 数组形式，其中每一项表示选择了range对应项中的第几个\r\n * @property {Array} range 自定义选择的数据，mode=selector或mode=multiSelector时有效\r\n * @property {String} range-key 当range参数的元素为对象时，指定Object中的哪个key的值作为选择器显示内容\r\n * @event {Function} confirm 点击确定按钮，返回当前选择的值\r\n * @event {Function} cancel 点击取消按钮，返回当前选择的值\r\n * @example <u-picker v-model=\"show\" mode=\"time\"></u-picker>\r\n */\r\nexport default {\r\n\tname: 'u-picker',\r\n\tprops: {\r\n\t\t// picker中需要显示的参数\r\n\t\tparams: {\r\n\t\t\ttype: Object,\r\n\t\t\tdefault() {\r\n\t\t\t\treturn {\r\n\t\t\t\t\tyear: true,\r\n\t\t\t\t\tmonth: true,\r\n\t\t\t\t\tday: true,\r\n\t\t\t\t\thour: false,\r\n\t\t\t\t\tminute: false,\r\n\t\t\t\t\tsecond: false,\r\n\t\t\t\t\tprovince: true,\r\n\t\t\t\t\tcity: true,\r\n\t\t\t\t\tarea: true,\r\n\t\t\t\t\ttimestamp: true,\r\n\t\t\t\t};\r\n\t\t\t}\r\n\t\t},\r\n\t\t// 当mode=selector或者mode=multiSelector时，提供的数组\r\n\t\trange: {\r\n\t\t\ttype: Array,\r\n\t\t\tdefault() {\r\n\t\t\t\treturn [];\r\n\t\t\t}\r\n\t\t},\r\n\t\t// 当mode=selector或者mode=multiSelector时，提供的默认选中的下标\r\n\t\tdefaultSelector: {\r\n\t\t\ttype: Array,\r\n\t\t\tdefault() {\r\n\t\t\t\treturn [0];\r\n\t\t\t}\r\n\t\t},\r\n\t\t// 当 range 是一个 Array＜Object＞ 时，通过 range-key 来指定 Object 中 key 的值作为选择器显示内容\r\n\t\trangeKey: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: ''\r\n\t\t},\r\n\t\t// 模式选择，region-地区类型，time-时间类型，selector-单列模式，multiSelector-多列模式\r\n\t\tmode: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: 'time'\r\n\t\t},\r\n\t\t// 年份开始时间\r\n\t\tstartYear: {\r\n\t\t\ttype: [String, Number],\r\n\t\t\tdefault: 1950\r\n\t\t},\r\n\t\t// 年份结束时间\r\n\t\tendYear: {\r\n\t\t\ttype: [String, Number],\r\n\t\t\tdefault: 2050\r\n\t\t},\r\n\t\t// \"取消\"按钮的颜色\r\n\t\tcancelColor: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: '#606266'\r\n\t\t},\r\n\t\t// \"确定\"按钮的颜色\r\n\t\tconfirmColor: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: '#2979ff'\r\n\t\t},\r\n\t\t// 默认显示的时间，2025-07-02 || 2025-07-02 13:01:00 || 2025/07/02\r\n\t\tdefaultTime: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: ''\r\n\t\t},\r\n\t\t// 默认显示的地区，可传类似[\"河北省\", \"秦皇岛市\", \"北戴河区\"]\r\n\t\tdefaultRegion: {\r\n\t\t\ttype: Array,\r\n\t\t\tdefault() {\r\n\t\t\t\treturn [];\r\n\t\t\t}\r\n\t\t},\r\n\t\t// 时间模式时，是否显示后面的年月日中文提示\r\n\t\tshowTimeTag: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: true\r\n\t\t},\r\n\t\t// 默认显示地区的编码，defaultRegion和areaCode同时存在，areaCode优先，可传类似[\"13\", \"1303\", \"130304\"]\r\n\t\tareaCode: {\r\n\t\t\ttype: Array,\r\n\t\t\tdefault() {\r\n\t\t\t\treturn [];\r\n\t\t\t}\r\n\t\t},\r\n\t\tsafeAreaInsetBottom: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: false\r\n\t\t},\r\n\t\t// 是否允许通过点击遮罩关闭Picker\r\n\t\tmaskCloseAble: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: true\r\n\t\t},\r\n\t\t// 通过双向绑定控制组件的弹出与收起\r\n\t\tvalue: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: false\r\n\t\t},\r\n\t\t// 弹出的z-index值\r\n\t\tzIndex: {\r\n\t\t\ttype: [String, Number],\r\n\t\t\tdefault: 0\r\n\t\t},\r\n\t\t// 顶部标题\r\n\t\ttitle: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: ''\r\n\t\t},\r\n\t\t// 取消按钮的文字\r\n\t\tcancelText: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: '取消'\r\n\t\t},\r\n\t\t// 确认按钮的文字\r\n\t\tconfirmText: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: '确认'\r\n\t\t}\r\n\t},\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\tyears: [],\r\n\t\t\tmonths: [],\r\n\t\t\tdays: [],\r\n\t\t\thours: [],\r\n\t\t\tminutes: [],\r\n\t\t\tseconds: [],\r\n\t\t\tyear: 0,\r\n\t\t\tmonth: 0,\r\n\t\t\tday: 0,\r\n\t\t\thour: 0,\r\n\t\t\tminute: 0,\r\n\t\t\tsecond: 0,\r\n\t\t\treset: false,\r\n\t\t\tstartDate: '',\r\n\t\t\tendDate: '',\r\n\t\t\tvalueArr: [],\r\n\t\t\tprovinces: provinces,\r\n\t\t\tcitys: citys[0],\r\n\t\t\tareas: areas[0][0],\r\n\t\t\tprovince: 0,\r\n\t\t\tcity: 0,\r\n\t\t\tarea: 0,\r\n\t\t\tmoving: false // 列是否还在滑动中，微信小程序如果在滑动中就点确定，结果可能不准确\r\n\t\t};\r\n\t},\r\n\tmounted() {\r\n\t\tthis.init();\r\n\t},\r\n\tcomputed: {\r\n\t\tpropsChange() {\r\n\t\t\t// 引用这几个变量，是为了监听其变化\r\n\t\t\treturn `${this.mode}-${this.defaultTime}-${this.startYear}-${this.endYear}-${this.defaultRegion}-${this.areaCode}`;\r\n\t\t},\r\n\t\tregionChange() {\r\n\t\t\t// 引用这几个变量，是为了监听其变化\r\n\t\t\treturn `${this.province}-${this.city}`;\r\n\t\t},\r\n\t\tyearAndMonth() {\r\n\t\t\treturn `${this.year}-${this.month}`;\r\n\t\t},\r\n\t\tuZIndex() {\r\n\t\t\t// 如果用户有传递z-index值，优先使用\r\n\t\t\treturn this.zIndex ? this.zIndex : this.$u.zIndex.popup;\r\n\t\t}\r\n\t},\r\n\twatch: {\r\n\t\tpropsChange() {\r\n\t\t\tthis.reset = true;\r\n\t\t\tsetTimeout(() => this.init(), 10);\r\n\t\t},\r\n\t\t// 如果地区发生变化，为了让picker联动起来，必须重置this.citys和this.areas\r\n\t\tregionChange(val) {\r\n\t\t\tthis.citys = citys[this.province];\r\n\t\t\tthis.areas = areas[this.province][this.city];\r\n\t\t},\r\n\t\t// watch监听月份的变化，实时变更日的天数，因为不同月份，天数不一样\r\n\t\t// 一个月可能有30，31天，甚至闰年2月的29天，平年2月28天\r\n\t\tyearAndMonth(val) {\r\n\t\t\tif (this.params.year) this.setDays();\r\n\t\t},\r\n\t\t// 微信和QQ小程序由于一些奇怪的原因(故同时对所有平台均初始化一遍)，需要重新初始化才能显示正确的值\r\n\t\tvalue(n) {\r\n\t\t\tif (n) {\r\n\t\t\t\tthis.reset = true;\r\n\t\t\t\tsetTimeout(() => this.init(), 10);\r\n\t\t\t}\r\n\t\t}\r\n\t},\r\n\tmethods: {\r\n\t\t// 标识滑动开始，只有微信小程序才有这样的事件\r\n\t\tpickstart() {\r\n\t\t\t// #ifdef MP-WEIXIN\r\n\t\t\tthis.moving = true;\r\n\t\t\t// #endif\r\n\t\t},\r\n\t\t// 标识滑动结束\r\n\t\tpickend() {\r\n\t\t\t// #ifdef MP-WEIXIN\r\n\t\t\tthis.moving = false;\r\n\t\t\t// #endif\r\n\t\t},\r\n\t\t// 对单列和多列形式的判断是否有传入变量的情况\r\n\t\tgetItemValue(item, mode) {\r\n\t\t\t// 目前(2020-05-25)uni-app对微信小程序编译有错误，导致v-if为false中的内容也执行，错误导致\r\n\t\t\t// 单列模式或者多列模式中的getItemValue同时被执行，故在这里再加一层判断\r\n\t\t\tif (this.mode == mode) {\r\n\t\t\t\treturn typeof item == 'object' ? item[this.rangeKey] : item;\r\n\t\t\t}\r\n\t\t},\r\n\t\t// 小于10前面补0，用于月份，日期，时分秒等\r\n\t\tformatNumber(num) {\r\n\t\t\treturn +num < 10 ? '0' + num : String(num);\r\n\t\t},\r\n\t\t// 生成递进的数组\r\n\t\tgenerateArray: function(start, end) {\r\n\t\t\t// 转为数值格式，否则用户给end-year等传递字符串值时，下面的end+1会导致字符串拼接，而不是相加\r\n\t\t\tstart = Number(start);\r\n\t\t\tend = Number(end);\r\n\t\t\tend = end > start ? end : start;\r\n\t\t\t// 生成数组，获取其中的索引，并剪出来\r\n\t\t\treturn [...Array(end + 1).keys()].slice(start);\r\n\t\t},\r\n\t\tgetIndex: function(arr, val) {\r\n\t\t\tlet index = arr.indexOf(val);\r\n\t\t\t// 如果index为-1(即找不到index值)，~(-1)=-(-1)-1=0，导致条件不成立\r\n\t\t\treturn ~index ? index : 0;\r\n\t\t},\r\n\t\t//日期时间处理\r\n\t\tinitTimeValue() {\r\n\t\t\t// 格式化时间，在IE浏览器(uni不存在此情况)，无法识别日期间的\"-\"间隔符号\r\n\t\t\tlet fdate = this.defaultTime.replace(/\\-/g, '/');\r\n\t\t\tfdate = fdate && fdate.indexOf('/') == -1 ? `2020/01/01 ${fdate}` : fdate;\r\n\t\t\tlet time = null;\r\n\t\t\tif (fdate) time = new Date(fdate);\r\n\t\t\telse time = new Date();\r\n\t\t\t// 获取年日月时分秒\r\n\t\t\tthis.year = time.getFullYear();\r\n\t\t\tthis.month = Number(time.getMonth()) + 1;\r\n\t\t\tthis.day = time.getDate();\r\n\t\t\tthis.hour = time.getHours();\r\n\t\t\tthis.minute = time.getMinutes();\r\n\t\t\tthis.second = time.getSeconds();\r\n\t\t},\r\n\t\tinit() {\r\n\t\t\tthis.valueArr = [];\r\n\t\t\tthis.reset = false;\r\n\t\t\tif (this.mode == 'time') {\r\n\t\t\t\tthis.initTimeValue();\r\n\t\t\t\tif (this.params.year) {\r\n\t\t\t\t\tthis.valueArr.push(0);\r\n\t\t\t\t\tthis.setYears();\r\n\t\t\t\t}\r\n\t\t\t\tif (this.params.month) {\r\n\t\t\t\t\tthis.valueArr.push(0);\r\n\t\t\t\t\tthis.setMonths();\r\n\t\t\t\t}\r\n\t\t\t\tif (this.params.day) {\r\n\t\t\t\t\tthis.valueArr.push(0);\r\n\t\t\t\t\tthis.setDays();\r\n\t\t\t\t}\r\n\t\t\t\tif (this.params.hour) {\r\n\t\t\t\t\tthis.valueArr.push(0);\r\n\t\t\t\t\tthis.setHours();\r\n\t\t\t\t}\r\n\t\t\t\tif (this.params.minute) {\r\n\t\t\t\t\tthis.valueArr.push(0);\r\n\t\t\t\t\tthis.setMinutes();\r\n\t\t\t\t}\r\n\t\t\t\tif (this.params.second) {\r\n\t\t\t\t\tthis.valueArr.push(0);\r\n\t\t\t\t\tthis.setSeconds();\r\n\t\t\t\t}\r\n\t\t\t} else if (this.mode == 'region') {\r\n\t\t\t\tif (this.params.province) {\r\n\t\t\t\t\tthis.valueArr.push(0);\r\n\t\t\t\t\tthis.setProvinces();\r\n\t\t\t\t}\r\n\t\t\t\tif (this.params.city) {\r\n\t\t\t\t\tthis.valueArr.push(0);\r\n\t\t\t\t\tthis.setCitys();\r\n\t\t\t\t}\r\n\t\t\t\tif (this.params.area) {\r\n\t\t\t\t\tthis.valueArr.push(0);\r\n\t\t\t\t\tthis.setAreas();\r\n\t\t\t\t}\r\n\t\t\t} else if (this.mode == 'selector') {\r\n\t\t\t\tthis.valueArr = this.defaultSelector;\r\n\t\t\t} else if (this.mode == 'multiSelector') {\r\n\t\t\t\tthis.valueArr = this.defaultSelector;\r\n\t\t\t\tthis.multiSelectorValue = this.defaultSelector;\r\n\t\t\t}\r\n\t\t\tthis.$forceUpdate();\r\n\t\t},\r\n\t\t// 设置picker的某一列值\r\n\t\tsetYears() {\r\n\t\t\t// 获取年份集合\r\n\t\t\tthis.years = this.generateArray(this.startYear, this.endYear);\r\n\t\t\t// 设置this.valueArr某一项的值，是为了让picker预选中某一个值\r\n\t\t\tthis.valueArr.splice(this.valueArr.length - 1, 1, this.getIndex(this.years, this.year));\r\n\t\t},\r\n\t\tsetMonths() {\r\n\t\t\tthis.months = this.generateArray(1, 12);\r\n\t\t\tthis.valueArr.splice(this.valueArr.length - 1, 1, this.getIndex(this.months, this.month));\r\n\t\t},\r\n\t\tsetDays() {\r\n\t\t\tlet totalDays = new Date(this.year, this.month, 0).getDate();\r\n\t\t\tthis.days = this.generateArray(1, totalDays);\r\n\t\t\tlet index = 0;\r\n\t\t\t// 这里不能使用类似setMonths()中的this.valueArr.splice(this.valueArr.length - 1, xxx)做法\r\n\t\t\t// 因为this.month和this.year变化时，会触发watch中的this.setDays()，导致this.valueArr.length计算有误\r\n\t\t\tif (this.params.year && this.params.month) index = 2;\r\n\t\t\telse if (this.params.month) index = 1;\r\n\t\t\telse if (this.params.year) index = 1;\r\n\t\t\telse index = 0;\r\n\t\t\t// 当月份变化时，会导致日期的天数也会变化，如果原来选的天数大于变化后的天数，则重置为变化后的最大值\r\n\t\t\t// 比如原来选中3月31日，调整为2月后，日期变为最大29，这时如果day值继续为31显然不合理，于是将其置为29(picker-column从1开始)\r\n\t\t\tif(this.day > this.days.length) this.day = this.days.length;\r\n\t\t\tthis.valueArr.splice(index, 1, this.getIndex(this.days, this.day));\r\n\t\t},\r\n\t\tsetHours() {\r\n\t\t\tthis.hours = this.generateArray(0, 23);\r\n\t\t\tthis.valueArr.splice(this.valueArr.length - 1, 1, this.getIndex(this.hours, this.hour));\r\n\t\t},\r\n\t\tsetMinutes() {\r\n\t\t\tthis.minutes = this.generateArray(0, 59);\r\n\t\t\tthis.valueArr.splice(this.valueArr.length - 1, 1, this.getIndex(this.minutes, this.minute));\r\n\t\t},\r\n\t\tsetSeconds() {\r\n\t\t\tthis.seconds = this.generateArray(0, 59);\r\n\t\t\tthis.valueArr.splice(this.valueArr.length - 1, 1, this.getIndex(this.seconds, this.second));\r\n\t\t},\r\n\t\tsetProvinces() {\r\n\t\t\t// 判断是否需要province参数\r\n\t\t\tif (!this.params.province) return;\r\n\t\t\tlet tmp = '';\r\n\t\t\tlet useCode = false;\r\n\t\t\t// 如果同时配置了defaultRegion和areaCode，优先使用areaCode参数\r\n\t\t\tif (this.areaCode.length) {\r\n\t\t\t\ttmp = this.areaCode[0];\r\n\t\t\t\tuseCode = true;\r\n\t\t\t} else if (this.defaultRegion.length) tmp = this.defaultRegion[0];\r\n\t\t\telse tmp = 0;\r\n\t\t\t// 历遍省份数组匹配\r\n\t\t\tprovinces.map((v, k) => {\r\n\t\t\t\tif (useCode ? v.value == tmp : v.label == tmp) {\r\n\t\t\t\t\ttmp = k;\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t\tthis.province = tmp;\r\n\t\t\tthis.provinces = provinces;\r\n\t\t\t// 设置默认省份的值\r\n\t\t\tthis.valueArr.splice(0, 1, this.province);\r\n\t\t},\r\n\t\tsetCitys() {\r\n\t\t\tif (!this.params.city) return;\r\n\t\t\tlet tmp = '';\r\n\t\t\tlet useCode = false;\r\n\t\t\tif (this.areaCode.length) {\r\n\t\t\t\ttmp = this.areaCode[1];\r\n\t\t\t\tuseCode = true;\r\n\t\t\t} else if (this.defaultRegion.length) tmp = this.defaultRegion[1];\r\n\t\t\telse tmp = 0;\r\n\t\t\tcitys[this.province].map((v, k) => {\r\n\t\t\t\tif (useCode ? v.value == tmp : v.label == tmp) {\r\n\t\t\t\t\ttmp = k;\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t\tthis.city = tmp;\r\n\t\t\tthis.citys = citys[this.province];\r\n\t\t\tthis.valueArr.splice(1, 1, this.city);\r\n\t\t},\r\n\t\tsetAreas() {\r\n\t\t\tif (!this.params.area) return;\r\n\t\t\tlet tmp = '';\r\n\t\t\tlet useCode = false;\r\n\t\t\tif (this.areaCode.length) {\r\n\t\t\t\ttmp = this.areaCode[2];\r\n\t\t\t\tuseCode = true;\r\n\t\t\t} else if (this.defaultRegion.length) tmp = this.defaultRegion[2];\r\n\t\t\telse tmp = 0;\r\n\t\t\tareas[this.province][this.city].map((v, k) => {\r\n\t\t\t\tif (useCode ? v.value == tmp : v.label == tmp) {\r\n\t\t\t\t\ttmp = k;\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t\tthis.area = tmp;\r\n\t\t\tthis.areas = areas[this.province][this.city];\r\n\t\t\tthis.valueArr.splice(2, 1, this.area);\r\n\t\t},\r\n\t\tclose() {\r\n\t\t\tthis.$emit('input', false);\r\n\t\t},\r\n\t\t// 用户更改picker的列选项\r\n\t\tchange(e) {\r\n\t\t\tthis.valueArr = e.detail.value;\r\n\t\t\tlet i = 0;\r\n\t\t\tif (this.mode == 'time') {\r\n\t\t\t\t// 这里使用i++，是因为this.valueArr数组的长度是不确定长度的，它根据this.params的值来配置长度\r\n\t\t\t\t// 进入if规则，i会加1，保证了能获取准确的值\r\n\t\t\t\tif (this.params.year) this.year = this.years[this.valueArr[i++]];\r\n\t\t\t\tif (this.params.month) this.month = this.months[this.valueArr[i++]];\r\n\t\t\t\tif (this.params.day) this.day = this.days[this.valueArr[i++]];\r\n\t\t\t\tif (this.params.hour) this.hour = this.hours[this.valueArr[i++]];\r\n\t\t\t\tif (this.params.minute) this.minute = this.minutes[this.valueArr[i++]];\r\n\t\t\t\tif (this.params.second) this.second = this.seconds[this.valueArr[i++]];\r\n\t\t\t} else if (this.mode == 'region') {\r\n\t\t\t\tif (this.params.province) this.province = this.valueArr[i++];\r\n\t\t\t\tif (this.params.city) this.city = this.valueArr[i++];\r\n\t\t\t\tif (this.params.area) this.area = this.valueArr[i++];\r\n\t\t\t} else if (this.mode == 'multiSelector') {\r\n\t\t\t\tlet index = null;\r\n\t\t\t\t// 对比前后两个数组，寻找变更的是哪一列，如果某一个元素不同，即可判定该列发生了变化\r\n\t\t\t\tthis.defaultSelector.map((val, idx) => {\r\n\t\t\t\t\tif (val != e.detail.value[idx]) index = idx;\r\n\t\t\t\t});\r\n\t\t\t\t// 为了让用户对多列变化时，对动态设置其他列的变更\r\n\t\t\t\tif (index != null) {\r\n\t\t\t\t\tthis.$emit('columnchange', {\r\n\t\t\t\t\t\tcolumn: index,\r\n\t\t\t\t\t\tindex: e.detail.value[index]\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\t\t// 用户点击确定按钮\r\n\t\tgetResult(event = null) {\r\n\t\t\t// #ifdef MP-WEIXIN\r\n\t\t\tif (this.moving) return;\r\n\t\t\t// #endif\r\n\t\t\tlet result = {};\r\n\t\t\t// 只返回用户在this.params中配置了为true的字段\r\n\t\t\tif (this.mode == 'time') {\r\n\t\t\t\tif (this.params.year) result.year = this.formatNumber(this.year || 0);\r\n\t\t\t\tif (this.params.month) result.month = this.formatNumber(this.month || 0);\r\n\t\t\t\tif (this.params.day) result.day = this.formatNumber(this.day || 0);\r\n\t\t\t\tif (this.params.hour) result.hour = this.formatNumber(this.hour || 0);\r\n\t\t\t\tif (this.params.minute) result.minute = this.formatNumber(this.minute || 0);\r\n\t\t\t\tif (this.params.second) result.second = this.formatNumber(this.second || 0);\r\n\t\t\t\tif (this.params.timestamp) result.timestamp = this.getTimestamp();\r\n\t\t\t} else if (this.mode == 'region') {\r\n\t\t\t\tif (this.params.province) result.province = provinces[this.province];\r\n\t\t\t\tif (this.params.city) result.city = citys[this.province][this.city];\r\n\t\t\t\tif (this.params.area) result.area = areas[this.province][this.city][this.area];\r\n\t\t\t} else if (this.mode == 'selector') {\r\n\t\t\t\tresult = this.valueArr;\r\n\t\t\t} else if (this.mode == 'multiSelector') {\r\n\t\t\t\tresult = this.valueArr;\r\n\t\t\t}\r\n\t\t\tif (event) this.$emit(event, result);\r\n\t\t\tthis.close();\r\n\t\t},\r\n\t\t// 获取时间戳\r\n\t\tgetTimestamp() {\r\n\t\t\t// yyyy-mm-dd为安卓写法，不支持iOS，需要使用\"/\"分隔，才能二者兼容\r\n\t\t\tlet time = this.year + '/' + this.month + '/' + this.day + ' ' + this.hour + ':' + this.minute + ':' + this.second;\r\n\t\t\treturn new Date(time).getTime() / 1000;\r\n\t\t}\r\n\t}\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n@import '../../libs/css/style.components.scss';\r\n\r\n.u-datetime-picker {\r\n\tposition: relative;\r\n\tz-index: 999;\r\n}\r\n\r\n.u-picker-view {\r\n\theight: 100%;\r\n\tbox-sizing: border-box;\r\n}\r\n\r\n.u-picker-header {\r\n\twidth: 100%;\r\n\theight: 90rpx;\r\n\tpadding: 0 40rpx;\r\n\t@include vue-flex;\r\n\tjustify-content: space-between;\r\n\talign-items: center;\r\n\tbox-sizing: border-box;\r\n\tfont-size: 30rpx;\r\n\tbackground: #fff;\r\n\tposition: relative;\r\n}\r\n\r\n.u-picker-header::after {\r\n\tcontent: '';\r\n\tposition: absolute;\r\n\tborder-bottom: 1rpx solid #eaeef1;\r\n\t-webkit-transform: scaleY(0.5);\r\n\ttransform: scaleY(0.5);\r\n\tbottom: 0;\r\n\tright: 0;\r\n\tleft: 0;\r\n}\r\n\r\n.u-picker__title {\r\n\tcolor: $u-content-color;\r\n}\r\n\r\n.u-picker-body {\r\n\twidth: 100%;\r\n\theight: 500rpx;\r\n\toverflow: hidden;\r\n\tbackground-color: #fff;\r\n}\r\n\r\n.u-column-item {\r\n\t@include vue-flex;\r\n\talign-items: center;\r\n\tjustify-content: center;\r\n\tfont-size: 32rpx;\r\n\tcolor: $u-main-color;\r\n\tpadding: 0 8rpx;\r\n}\r\n\r\n.u-text {\r\n\tfont-size: 24rpx;\r\n\tpadding-left: 8rpx;\r\n}\r\n\r\n.u-btn-picker {\r\n\tpadding: 16rpx;\r\n\tbox-sizing: border-box;\r\n\ttext-align: center;\r\n\ttext-decoration: none;\r\n}\r\n\r\n.u-opacity {\r\n\topacity: 0.5;\r\n}\r\n\r\n.u-btn-picker--primary {\r\n\tcolor: $u-type-primary;\r\n}\r\n\r\n.u-btn-picker--tips {\r\n\tcolor: $u-tips-color;\r\n}\r\n</style>\r\n", "import mod from \"-!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-picker.vue?vue&type=style&index=0&id=d45639b2&lang=scss&scoped=true&\"; export default mod; export * from \"-!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-picker.vue?vue&type=style&index=0&id=d45639b2&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1752590364296\n      var cssReload = require(\"D:/HBuilderX.3.3.13.20220314.full/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"../../\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}