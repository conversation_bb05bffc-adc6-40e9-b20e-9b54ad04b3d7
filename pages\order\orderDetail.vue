<template>
	<view class="page">
		<view style="position: relative;padding-bottom: 30rpx;">
			<u-navbar background="transparent" :back-text="orderObj.orderStatus | formatState" :back-icon-size="30"
				:back-text-style="{fontSize: '40rpx',marginLeft: '20rpx'}" :border-bottom="false"></u-navbar>
			<template>
				<view v-if="orderObj.orderStatus==0"
					style="font-size: 24rpx;color: #61687C;position: absolute;left: 60rpx;bottom: 30rpx;">请在
					<u-count-down :timestamp="orderObj.timestamp" font-size="24" bg-color="none" color="#FF0046"
						separator="zh" separator-size="24" separator-color="#FF0046"></u-count-down>
					前完成付款
				</view>
				<view v-if="orderObj.orderStatus==4"
					style="font-size: 24rpx;color: #61687C;position: absolute;left: 60rpx;bottom: 30rpx;">订单已取消，期待您再次光临</view>
				<view v-if="orderObj.orderStatus==1"
					style="font-size: 24rpx;color: #61687C;position: absolute;left: 60rpx;bottom: 30rpx;">请到店后使用</view>
				<view v-if="orderObj.orderStatus==3"
					style="font-size: 24rpx;color: #61687C;position: absolute;left: 60rpx;bottom: 30rpx;">订单已完成，期待您再次光临</view>
					<view v-if="orderObj.orderStatus==7"
						style="font-size: 24rpx;color: #61687C;position: absolute;left: 60rpx;bottom: 30rpx;">预计1-5个个工作日到账，请注意查收</view>
			</template>
		</view>
		<view class="address">
			<view v-if="storeInfo.id">
				<view style="display: flex;justify-content: space-between;align-items: center;">
					<view class="address_top">
						<view style="display: flex;">
							<image src="/static/img/shop/shop_store.png" mode="widthFix" style="width: 40rpx;height: 40rpx;"></image>
						</view>
						<view style="margin-left: 10rpx;ffont-size: 32rpx;font-weight: 600;">{{storeInfo.name}}</view>
					</view>
					<view style="display: flex; align-items: center;font-size: 24rpx;">切换门店 <view
							class="right-icon"></view>
					</view>
				</view>
				<view style="margin-top: 20rpx;color: #61687C;font-size: 24rpx;">{{storeInfo.address}}</view>
			</view>
			<view class="address_right" v-else
				style="width: 100%;display: flex;align-items: center;justify-content: space-between;">
				<view style="display: flex;align-items: center;">
					<image src="/static/img/shop/shop_store.png" mode="widthFix" style="width: 40rpx;height: 40rpx;">
						<text style="margin-left: 10rpx;">请选择门店</text>
				</view>
				<view class="right-icon"></view>
			</view>
		</view>
		<view class="product_content">
			<view class="product_info">
				<view class="prodct_left">
					<image style="width:164rpx;height: 164rpx;" :src="orderObj.goodsImg"></image>
				</view>
				<view class="product_center">
					<view style="font-size: 28rpx;line-height: 44rpx;" class="text-ellipsis_2">{{orderObj.goodsName}}
					</view>
					<view style="display: flex;justify-content: space-between;margin-top: 20rpx;">
						<view
							style="color: #61687C;font-size: 24rpx;background-color: #F2F4F7;text-align: center;padding: 6rpx 20rpx;border-radius: 6rpx;">
							{{orderObj.specification}}g</view>
					</view>
				</view>
				<view class="product_right">
					<view style="display: flex;justify-content: flex-end;"><text
							class="font_small">¥{{orderObj.price}}</text></view>
					<view style="font-size: 24rpx;color: #9FA3B0;margin-top: 8rpx;text-align: right;">x1</view>
					<view style="margin-top: 26rpx;"><text style="color: #61687C;font-size: 24rpx;">实付</text><text
							style="color: #171B25;font-size: 28rpx;margin-left: 6rpx;">¥{{orderObj.totalAmt}}</text>
					</view>
				</view>
			</view>
			<view v-if="orderObj.orderStatus==1" class="product_yzm">
				<view style="display: flex;align-items: center;"><text>验证码</text><u-icon @click="onEye"
						:name="eyeOff?'eye':'eye-off'" :custom-style="{marginLeft: '10rpx'}" size="36"></u-icon></view>
				<view>{{eyeOff?orderObj.code:(orderObj.code ? orderObj.code.replace(/./g, "*") : '')}}</view>
			</view>
		</view>
		<view class="inviter">
			<view>邀请人</view>
			<view style="display: flex;align-items: center;color: #61687C;">{{orderObj.yqrName.name||'-'}}</view>
		</view>
		<view class="pay_type">
			<view>订单信息</view>
			<view style="margin-top: 24rpx;"><text style="color: #61687C;">订单编号：</text><text>{{orderObj.orderNo}}</text>
			</view>
			<view style="margin-top: 16rpx;"><text
					style="color: #61687C;">下单时间：</text><text>{{orderObj.createDate}}</text></view>
			<view v-if="['1','3'].includes(orderObj.orderStatus)" style="margin-top: 16rpx;"><text
					style="color: #61687C;">付款时间：</text><text>{{orderObj.payTime}}</text></view>
			<view style="margin-top: 16rpx;"><text
					style="color: #61687C;">微信支付：</text><text>¥{{orderObj.totalAmt}}</text></view>
			<!-- 发票相关按钮 - 仅在已完成状态显示 -->
			<view v-if="String(orderObj.orderStatus) === '3'" class="invoice_btn_container">
				<!-- 未开票时显示开具发票按钮 -->
				<button v-if="String(orderObj.ticketState) === '0'" class="invoice_btn" @click="goToInvoice">开具发票</button>
				<!-- 已开票时显示下载发票按钮 -->
				<button v-if="String(orderObj.ticketState) === '1'" class="invoice_btn download_btn" @click="downloadInvoice">下载发票</button>
			</view>
		</view>
		<view v-if="orderObj.orderStatus==0" class="goods_detail_footer">
			<button class="btn" @click="settleOrder">确认支付</button>
		</view>
	</view>
</template>

<script>
	const api = require('../../config/api')
	const util = require('../../utils/util')
	export default {
		name: 'orderDetail',
		data() {
			return {
				addressObj: {
					realName: '',
					contactPhone: '',
					address: ''
				},
				orderObj: {
					orderNo: '',
					orderStatus: '',
					goodsImg: '',
					goodsName: '',
					price: '',
					goodsNum: 0,
					totalAmt: '',
					createDate: '',
					ticketState: '',    // 开票状态:0-未开票、1-已开票
					downloadUrl: ''     // 发票pdf下载地址
				},
				storeInfo: {},
				eyeOff: false
			}
		},
		filters: {
			formatState: function(_state) {
				if (_state === '0') {
					return '待付款'
				} else if (_state === '1') {
					return '待核销'
				} else if (_state === '3') {
					return '已完成'
				} else if (_state === '4') {
					return '已取消'
				} else if (_state === '7') {
					return '已退款'
				}
			}
		},
		onLoad: function(option) {
			this.orderNo = option.orderNo
			this.getOrderInfo()
		},
		onShow() {

		},
		methods: {
			// 获取订单详情
			getOrderInfo() {
				uni.showLoading({
					title: '加载中'
				});
				util.request(api.orderDetailUrl + this.orderNo, {}, 'POST').then((res) => {
					console.log(res)
					if (res.code !== 0) {
						uni.showToast({
							title: res.message,
							icon: 'none'
						})
					} else {
						uni.hideLoading();
						let nowTime = new Date()
						// let startTime = new Date(records[i].createDate);
						let endTime = new Date(res.data.createDate)
						let nowSeconds = (nowTime.getTime()) / 1000; // 开始时间转换为秒
						// let startSeconds = startTime.getTime() / 1000; // 开始时间转换为秒
						let endSeconds = (endTime.getTime() + 30 * 60 * 1000) / 1000; // 结束时间转换为秒
						let timestamp = endSeconds - nowSeconds; // 持续时间（秒）
						res.data.timestamp = timestamp
						this.orderObj = res.data || {}
						this.storeInfo = res.data.storeName || {}
					}
				})
			},
			async settleOrder() {
				uni.showLoading({
					title: '支付中'
				});
				let that = this;
				// #ifdef MP-WEIXIN
				uni.login({
					"provider": "weixin",
					"onlyAuthorize": true, // 微信登录仅请求授权认证
					success: async function(event){
						const {code} = event
						const result = await util.request(
							api.orderPayUrl, {
								orderNo: that.orderObj.orderNo,
								payTyle: '8',
								code: event.code
							},
							'POST'
						);
						if (result.code !== 0) {
							uni.showToast({
								title: result.msg,
								icon: "none"
							})
						
						} else {
							that.paymentRequest(result.data)
						}
					},
					fail: function (err) {
				        // 登录授权失败
				        // err.code是错误码
				    }
				})
				// #endif
				// #ifdef WEB
				const result = await util.request(
					api.orderPayUrl, {
						orderNo: that.orderObj.orderNo,
						payTyle: '7',
						code: ''
					},
					'POST'
				);
				if (result.code !== 0) {
					uni.showToast({
						title: result.msg,
						icon: "none"
					})
				
				} else {
					const payWeb = JSON.parse(result.data.jspay_info)
					let param = {
						"appId": "wxa56aa346588ae42f", //公众号ID，由你传入     
						"timeStamp": payWeb.timeStamp, //时间戳，自1970年以来的秒数     
						"nonceStr": payWeb.nonceStr, //随机串     
						"package": payWeb.package,
						"signType": payWeb.signType, //微信签名方式：     
						"paySign": payWeb.paySign, //微信签名 
					}
					that.onBridgeReady(param)
				}
				// #endif
			},
			paymentRequest(params) {
				let that = this
				const payInfo = JSON.parse(params.jspay_info)
				uni.requestPayment({
					timeStamp: payInfo.timeStamp,
					nonceStr: payInfo.nonceStr,
					package: payInfo.package,
					signType: payInfo.signType,
					paySign: payInfo.paySign,
					success: function(res) {
						uni.showToast({
							title: '支付成功',
							icon: 'success'
						})
						setTimeout(() => {
							// 订单详情页面
							that.getOrderInfo()
						}, 1000)
						console.log('success:' + JSON.stringify(res));
					},
					fail: function(err) {
						// uni.showToast({
						// 	title: '支付失败',
						// 	icon: 'none'
						// })
						uni.hideLoading()
						setTimeout(() => {
							// 订单详情页面
							that.getOrderInfo()
						}, 1000)
					}
				});
			},
			onBridgeReady(param) {
				let that = this;
				WeixinJSBridge.invoke('getBrandWCPayRequest', param,
					function(res) {
						if (res.err_msg == "get_brand_wcpay_request:ok") {
							console.log("微信支付成功了！！！")
							// 支付成功的回调中
							uni.showToast({
								title: '支付成功',
								icon: 'success'
							});
							//支付成功后重新回到订单详情界面并刷新
							setTimeout(() => {
								// 订单详情页面
								that.getOrderInfo()
							}, 1000);
						}else{
							// uni.showToast({
							// 	title: res.err_msg,
							// 	icon:'none'
							// })
							uni.hideLoading()
							setTimeout(()=>{
								// 订单详情页面
								that.getOrderInfo()
							},1000)
						}
					});
			},
			// 确认收货
			toSureReceipt() {},
			// 取消订单
			cancelOrderEvent() {
				util.request(api.cancelOrderUrl + this.orderNo, {}, 'POST').then((res) => {
					console.log(res)
					if (res.code !== 0) {
						uni.showToast({
							title: res.message,
							icon: 'none'
						})
					} else {
						uni.showToast({
							title: '取消订单成功',
							icon: 'none'
						})
						let time1 = setTimeout(() => {
							clearTimeout(time1)
							uni.navigateBack({
								delta: 1
							})
						}, 1000)
					}
				})
			},
			// 去支付
			onEye() {
				this.eyeOff = !this.eyeOff
			},
			// 跳转到开具发票页面
			goToInvoice() {
				uni.navigateTo({
					url: `/pages/invoice/invoice?orderNo=${this.orderObj.orderNo}&totalAmt=${this.orderObj.totalAmt}&goodsName=${encodeURIComponent(this.orderObj.goodsName)}`
				})
			},
			// 下载发票
			downloadInvoice() {
				if (!this.orderObj.downloadUrl) {
					uni.showToast({
						title: '发票下载地址不存在',
						icon: 'none'
					})
					return
				}

				// 在微信小程序中预览PDF
				uni.showLoading({
					title: '加载中...'
				})

				// 先下载文件到本地
				uni.downloadFile({
					url: this.orderObj.downloadUrl,
					success: (res) => {
						uni.hideLoading()
						if (res.statusCode === 200) {
							// 预览PDF文件
							uni.openDocument({
								filePath: res.tempFilePath,
								fileType: 'pdf',
								success: () => {
									console.log('PDF预览成功')
								},
								fail: (err) => {
									console.log('PDF预览失败:', err)
									uni.showToast({
										title: '文件预览失败',
										icon: 'none'
									})
								}
							})
						} else {
							uni.showToast({
								title: '文件下载失败',
								icon: 'none'
							})
						}
					},
					fail: (err) => {
						uni.hideLoading()
						console.log('下载失败:', err)
						uni.showToast({
							title: '网络错误，下载失败',
							icon: 'none'
						})
					}
				})
			}
		}
	}
</script>
<style>
	page {
		background-color: #F7F7F7;
	}
</style>

<style lang="scss">
	.page {
		padding: 24rpx 24rpx 0 24rpx;
	}

	.address {
		// margin-top: 24rpx;
		padding: 30rpx 24rpx;
		background-color: white;
		border-radius: 10rpx;
	}

	.address_top {
		display: flex;
		justify-content: space-between;
		align-items: center;
	}

	.product_content {
		margin-top: 24rpx;
		padding: 24rpx;
		background-color: #FFFFFF;
		border-radius: 12rpx;

		.product_info {
			display: flex;
			padding-bottom: 20rpx;

			.prodct_left {
				display: flex;
				border-radius: 10rpx;
				overflow: hidden;
			}

			.product_center {
				flex: 1;
				margin-left: 20rpx;
			}

			.product_right {
				margin-left: 32rpx;

				.font_small {
					font-size: 36rpx;
					color: #171B25;
					font-weight: 600;
				}
			}
		}
	}

	.product_yzm {
		display: flex;
		justify-content: space-between;
		align-items: center;
		border-top: 1px dashed #F8F8F8;
		padding-top: 24rpx;
	}

	.inviter {
		margin-top: 24rpx;
		padding: 24rpx 32rpx;
		// width: 100%;
		display: flex;
		align-items: center;
		justify-content: space-between;
		background-color: #fff;
		border-radius: 10rpx;

		.uni-input {
			text-align: right;
		}
	}

	.pay_type {
		font-size: 28rpx;
		margin-top: 24rpx;
		// display: flex;
		// align-items: center;
		// justify-content: space-between;
		padding: 24rpx 32rpx;
		background-color: #fff;
		border-radius: 10rpx;
	}

	.invoice_btn_container {
		margin-top: 24rpx;
		display: flex;
		justify-content: center;
	}

	.invoice_btn {
		width: 200rpx;
		height: 64rpx;
		background-color: #BBA186;
		color: #FFFFFF;
		font-size: 26rpx;
		border-radius: 32rpx;
		border: none;
		display: flex;
		align-items: center;
		justify-content: center;

		&.download_btn {
			background-color: #4CAF50;
		}
	}

	.goods_detail_footer {
		position: fixed;
		bottom: 0;
		left: 0;
		right: 0;
		width: 100%;
		height: 170rpx;
		display: flex;
		justify-content: center;
		background-color: #FFFFFF;
		padding-top: 6rpx;

		.btn {
			height: 84rpx;
			width: 93%;
			border-radius: 9999px;
			background-color: #BBA186;
			color: #FFFFFF;
			font-size: 28rpx;
			text-align: center;
			line-height: 84rpx;
		}
	}
</style>