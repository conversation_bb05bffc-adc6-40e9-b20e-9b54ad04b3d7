(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-order-orderDetail"],{"03e4":function(t,e,n){var i=n("7263");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var r=n("4f06").default;r("0732193c",i,!0,{sourceMap:!1,shadowMode:!1})},"0e55":function(t,e,n){"use strict";n("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("a9e3");var i={name:"u-count-down",props:{timestamp:{type:[Number,String],default:0},autoplay:{type:Boolean,default:!0},separator:{type:String,default:"colon"},separatorSize:{type:[Number,String],default:30},separatorColor:{type:String,default:"#303133"},color:{type:String,default:"#303133"},fontSize:{type:[Number,String],default:30},bgColor:{type:String,default:"#fff"},height:{type:[Number,String],default:"auto"},showBorder:{type:Boolean,default:!1},borderColor:{type:String,default:"#303133"},showSeconds:{type:Boolean,default:!0},showMinutes:{type:Boolean,default:!0},showHours:{type:Boolean,default:!0},showDays:{type:Boolean,default:!0},hideZeroDay:{type:Boolean,default:!1}},watch:{timestamp:function(t,e){this.clearTimer(),this.start()}},data:function(){return{d:"00",h:"00",i:"00",s:"00",timer:null,seconds:0}},computed:{itemStyle:function(){var t={};return this.height&&(t.height=this.height+"rpx",t.width=this.height+"rpx"),this.showBorder&&(t.borderStyle="solid",t.borderColor=this.borderColor,t.borderWidth="1px"),this.bgColor&&(t.backgroundColor=this.bgColor),t},letterStyle:function(){var t={};return this.fontSize&&(t.fontSize=this.fontSize+"rpx"),this.color&&(t.color=this.color),t}},mounted:function(){this.autoplay&&this.timestamp&&this.start()},methods:{start:function(){var t=this;this.clearTimer(),this.timestamp<=0||(this.seconds=Number(this.timestamp),this.formatTime(this.seconds),this.timer=setInterval((function(){if(t.seconds--,t.$emit("change",t.seconds),t.seconds<0)return t.end();t.formatTime(t.seconds)}),1e3))},formatTime:function(t){t<=0&&this.end();var e,n=0,i=0,r=0;n=Math.floor(t/86400),e=Math.floor(t/3600)-24*n;var o=null;o=this.showDays?e:Math.floor(t/3600),i=Math.floor(t/60)-60*e-24*n*60,r=Math.floor(t)-24*n*60*60-60*e*60-60*i,o=o<10?"0"+o:o,i=i<10?"0"+i:i,r=r<10?"0"+r:r,n=n<10?"0"+n:n,this.d=n,this.h=o,this.i=i,this.s=r},end:function(){this.clearTimer(),this.$emit("end",{})},clearTimer:function(){this.timer&&(clearInterval(this.timer),this.timer=null)}},beforeDestroy:function(){clearInterval(this.timer),this.timer=null}};e.default=i},"157b":function(t,e,n){var i=n("1e5d");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var r=n("4f06").default;r("4fec3a72",i,!0,{sourceMap:!1,shadowMode:!1})},"1e5d":function(t,e,n){var i=n("24fb");e=i(!1),e.push([t.i,'@charset "UTF-8";\r\n/* uni.scss */\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 字体变量 */\r\n/* 颜色变量 */\r\n/* 行为相关颜色 */\r\n/* 文字基本颜色 */\r\n/* 背景颜色 */\r\n/* 边框颜色 */\r\n/* 尺寸变量 */\r\n/* 文字尺寸 */\r\n/* 图片尺寸 */\r\n/* Border Radius */\r\n/* 水平间距 */\r\n/* 垂直间距 */\r\n/* 透明度 */\r\n/* 文章场景相关 */.u-navbar[data-v-2920cc37]{width:100%}.u-navbar-fixed[data-v-2920cc37]{position:fixed;left:0;right:0;top:0;z-index:991}.u-status-bar[data-v-2920cc37]{width:100%}.u-navbar-inner[data-v-2920cc37]{display:flex;flex-direction:row;justify-content:space-between;position:relative;align-items:center}.u-back-wrap[data-v-2920cc37]{display:flex;flex-direction:row;align-items:center;flex:1;flex-grow:0;padding:%?14?% %?14?% %?14?% %?24?%}.u-back-text[data-v-2920cc37]{padding-left:%?4?%;font-size:%?30?%}.u-navbar-content-title[data-v-2920cc37]{display:flex;flex-direction:row;align-items:center;justify-content:center;flex:1;position:absolute;left:0;right:0;height:%?60?%;text-align:center;flex-shrink:0}.u-navbar-centent-slot[data-v-2920cc37]{flex:1}.u-title[data-v-2920cc37]{line-height:%?60?%;font-size:%?32?%;flex:1}.u-navbar-right[data-v-2920cc37]{flex:1;display:flex;flex-direction:row;align-items:center;justify-content:flex-end}.u-slot-content[data-v-2920cc37]{flex:1;display:flex;flex-direction:row;align-items:center}',""]),t.exports=e},2013:function(t,e,n){"use strict";var i=n("5b24"),r=n.n(i);r.a},"25f3":function(t,e,n){var i=n("359d");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var r=n("4f06").default;r("43ca72be",i,!0,{sourceMap:!1,shadowMode:!1})},"283d":function(t,e,n){"use strict";n.r(e);var i=n("bce9"),r=n("3bcf");for(var o in r)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return r[t]}))}(o);n("2013"),n("b680");var a=n("f0c5"),s=Object(a["a"])(r["default"],i["b"],i["c"],!1,null,"7004bd02",null,!1,i["a"],void 0);e["default"]=s.exports},"2b4a":function(t,e,n){"use strict";var i=n("03e4"),r=n.n(i);r.a},"359d":function(t,e,n){var i=n("24fb");e=i(!1),e.push([t.i,'@charset "UTF-8";\r\n/* uni.scss */\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 字体变量 */\r\n/* 颜色变量 */\r\n/* 行为相关颜色 */\r\n/* 文字基本颜色 */\r\n/* 背景颜色 */\r\n/* 边框颜色 */\r\n/* 尺寸变量 */\r\n/* 文字尺寸 */\r\n/* 图片尺寸 */\r\n/* Border Radius */\r\n/* 水平间距 */\r\n/* 垂直间距 */\r\n/* 透明度 */\r\n/* 文章场景相关 */.page[data-v-7004bd02]{padding:%?24?% %?24?% 0 %?24?%}.address[data-v-7004bd02]{padding:%?30?% %?24?%;background-color:#fff;border-radius:%?10?%}.address_top[data-v-7004bd02]{display:flex;justify-content:space-between;align-items:center}.product_content[data-v-7004bd02]{margin-top:%?24?%;padding:%?24?%;background-color:#fff;border-radius:%?12?%}.product_content .product_info[data-v-7004bd02]{display:flex;padding-bottom:%?20?%}.product_content .product_info .prodct_left[data-v-7004bd02]{display:flex;border-radius:%?10?%;overflow:hidden}.product_content .product_info .product_center[data-v-7004bd02]{flex:1;margin-left:%?20?%}.product_content .product_info .product_right[data-v-7004bd02]{margin-left:%?32?%}.product_content .product_info .product_right .font_small[data-v-7004bd02]{font-size:%?36?%;color:#171b25;font-weight:600}.product_yzm[data-v-7004bd02]{display:flex;justify-content:space-between;align-items:center;border-top:1px dashed #f8f8f8;padding-top:%?24?%}.inviter[data-v-7004bd02]{margin-top:%?24?%;padding:%?24?% %?32?%;display:flex;align-items:center;justify-content:space-between;background-color:#fff;border-radius:%?10?%}.inviter .uni-input[data-v-7004bd02]{text-align:right}.pay_type[data-v-7004bd02]{font-size:%?28?%;margin-top:%?24?%;padding:%?24?% %?32?%;background-color:#fff;border-radius:%?10?%}.goods_detail_footer[data-v-7004bd02]{position:fixed;bottom:0;left:0;right:0;width:100%;height:%?170?%;display:flex;justify-content:center;background-color:#fff;padding-top:%?6?%}.goods_detail_footer .btn[data-v-7004bd02]{height:%?84?%;width:93%;border-radius:9999px;background-color:#bba186;color:#fff;font-size:%?28?%;text-align:center;line-height:%?84?%}',""]),t.exports=e},"3bcf":function(t,e,n){"use strict";n.r(e);var i=n("6567"),r=n.n(i);for(var o in i)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(o);e["default"]=r.a},"454d":function(t,e,n){"use strict";n("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("a9e3");var i=uni.getSystemInfoSync(),r={},o={name:"u-navbar",props:{height:{type:[String,Number],default:""},backIconColor:{type:String,default:"#606266"},backIconName:{type:String,default:"nav-back"},backIconSize:{type:[String,Number],default:"44"},backText:{type:String,default:""},backTextStyle:{type:Object,default:function(){return{color:"#606266"}}},title:{type:String,default:""},titleWidth:{type:[String,Number],default:"250"},titleColor:{type:String,default:"#606266"},titleBold:{type:Boolean,default:!1},titleSize:{type:[String,Number],default:32},isBack:{type:[Boolean,String],default:!0},background:{type:Object,default:function(){return{background:"#ffffff"}}},isFixed:{type:Boolean,default:!0},immersive:{type:Boolean,default:!1},borderBottom:{type:Boolean,default:!0},zIndex:{type:[String,Number],default:""},customBack:{type:Function,default:null}},data:function(){return{menuButtonInfo:r,statusBarHeight:i.statusBarHeight}},computed:{navbarInnerStyle:function(){var t={};return t.height=this.navbarHeight+"px",t},navbarStyle:function(){var t={};return t.zIndex=this.zIndex?this.zIndex:this.$u.zIndex.navbar,Object.assign(t,this.background),t},titleStyle:function(){var t={};return t.left=(i.windowWidth-uni.upx2px(this.titleWidth))/2+"px",t.right=(i.windowWidth-uni.upx2px(this.titleWidth))/2+"px",t.width=uni.upx2px(this.titleWidth)+"px",t},navbarHeight:function(){return this.height?this.height:44}},created:function(){},methods:{goBack:function(){"function"===typeof this.customBack?this.customBack.bind(this.$u.$parent.call(this))():uni.navigateBack()}}};e.default=o},"4c12":function(t,e,n){var i=n("24fb");e=i(!1),e.push([t.i,"uni-page-body[data-v-7004bd02]{background-color:#f7f7f7}body.?%PAGE?%[data-v-7004bd02]{background-color:#f7f7f7}",""]),t.exports=e},5703:function(t,e,n){"use strict";n.d(e,"b",(function(){return i})),n.d(e,"c",(function(){return r})),n.d(e,"a",(function(){}));var i=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("v-uni-view",{staticClass:"u-countdown"},[t.showDays&&(t.hideZeroDay||!t.hideZeroDay&&"00"!=t.d)?n("v-uni-view",{staticClass:"u-countdown-item",style:[t.itemStyle]},[n("v-uni-view",{staticClass:"u-countdown-time",style:[t.letterStyle]},[t._v(t._s(t.d))])],1):t._e(),t.showDays&&(t.hideZeroDay||!t.hideZeroDay&&"00"!=t.d)?n("v-uni-view",{staticClass:"u-countdown-colon",style:{fontSize:t.separatorSize+"rpx",color:t.separatorColor,paddingBottom:"colon"==t.separator?"4rpx":0}},[t._v(t._s("colon"==t.separator?":":"天"))]):t._e(),t.showHours?n("v-uni-view",{staticClass:"u-countdown-item",style:[t.itemStyle]},[n("v-uni-view",{staticClass:"u-countdown-time",style:{fontSize:t.fontSize+"rpx",color:t.color}},[t._v(t._s(t.h))])],1):t._e(),t.showHours?n("v-uni-view",{staticClass:"u-countdown-colon",style:{fontSize:t.separatorSize+"rpx",color:t.separatorColor,paddingBottom:"colon"==t.separator?"4rpx":0}},[t._v(t._s("colon"==t.separator?":":"时"))]):t._e(),t.showMinutes?n("v-uni-view",{staticClass:"u-countdown-item",style:[t.itemStyle]},[n("v-uni-view",{staticClass:"u-countdown-time",style:{fontSize:t.fontSize+"rpx",color:t.color}},[t._v(t._s(t.i))])],1):t._e(),t.showMinutes?n("v-uni-view",{staticClass:"u-countdown-colon",style:{fontSize:t.separatorSize+"rpx",color:t.separatorColor,paddingBottom:"colon"==t.separator?"4rpx":0}},[t._v(t._s("colon"==t.separator?":":"分"))]):t._e(),t.showSeconds?n("v-uni-view",{staticClass:"u-countdown-item",style:[t.itemStyle]},[n("v-uni-view",{staticClass:"u-countdown-time",style:{fontSize:t.fontSize+"rpx",color:t.color}},[t._v(t._s(t.s))])],1):t._e(),t.showSeconds&&"zh"==t.separator?n("v-uni-view",{staticClass:"u-countdown-colon",style:{fontSize:t.separatorSize+"rpx",color:t.separatorColor,paddingBottom:"colon"==t.separator?"4rpx":0}},[t._v("秒")]):t._e()],1)},r=[]},"5b24":function(t,e,n){var i=n("4c12");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var r=n("4f06").default;r("72fd1423",i,!0,{sourceMap:!1,shadowMode:!1})},6567:function(t,e,n){"use strict";n("7a82");var i=n("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("e9c4");var r=i(n("c7eb")),o=i(n("1da1")),a=n("30ea"),s=n("9e56"),c={name:"orderDetail",data:function(){return{addressObj:{realName:"",contactPhone:"",address:""},orderObj:{orderNo:"",orderStatus:"",goodsImg:"",goodsName:"",price:"",goodsNum:0,totalAmt:"",createDate:""},storeInfo:{},eyeOff:!1}},filters:{formatState:function(t){return"0"===t?"待付款":"1"===t?"待核销":"3"===t?"已完成":"4"===t?"已取消":"7"===t?"已退款":void 0}},onLoad:function(t){this.orderNo=t.orderNo,this.getOrderInfo()},onShow:function(){},methods:{getOrderInfo:function(){var t=this;uni.showLoading({title:"加载中"}),s.request(a.orderDetailUrl+this.orderNo,{},"POST").then((function(e){if(console.log(e),0!==e.code)uni.showToast({title:e.message,icon:"none"});else{uni.hideLoading();var n=new Date,i=new Date(e.data.createDate),r=n.getTime()/1e3,o=(i.getTime()+18e5)/1e3,a=o-r;e.data.timestamp=a,t.orderObj=e.data||{},t.storeInfo=e.data.storeName||{}}}))},settleOrder:function(){var t=this;return(0,o.default)((0,r.default)().mark((function e(){var n,i,o,c;return(0,r.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return uni.showLoading({title:"支付中"}),n=t,e.next=4,s.request(a.orderPayUrl,{orderNo:n.orderObj.orderNo,payTyle:"7",code:""},"POST");case 4:i=e.sent,0!==i.code?uni.showToast({title:i.msg,icon:"none"}):(o=JSON.parse(i.data.jspay_info),c={appId:"wxa56aa346588ae42f",timeStamp:o.timeStamp,nonceStr:o.nonceStr,package:o.package,signType:o.signType,paySign:o.paySign},n.onBridgeReady(c));case 6:case"end":return e.stop()}}),e)})))()},paymentRequest:function(t){var e=this,n=JSON.parse(t.jspay_info);uni.requestPayment({timeStamp:n.timeStamp,nonceStr:n.nonceStr,package:n.package,signType:n.signType,paySign:n.paySign,success:function(t){uni.showToast({title:"支付成功",icon:"success"}),setTimeout((function(){e.getOrderInfo()}),1e3),console.log("success:"+JSON.stringify(t))},fail:function(t){uni.hideLoading(),setTimeout((function(){e.getOrderInfo()}),1e3)}})},onBridgeReady:function(t){var e=this;WeixinJSBridge.invoke("getBrandWCPayRequest",t,(function(t){"get_brand_wcpay_request:ok"==t.err_msg?(console.log("微信支付成功了！！！"),uni.showToast({title:"支付成功",icon:"success"}),setTimeout((function(){e.getOrderInfo()}),1e3)):(uni.hideLoading(),setTimeout((function(){e.getOrderInfo()}),1e3))}))},toSureReceipt:function(){},cancelOrderEvent:function(){s.request(a.cancelOrderUrl+this.orderNo,{},"POST").then((function(t){if(console.log(t),0!==t.code)uni.showToast({title:t.message,icon:"none"});else{uni.showToast({title:"取消订单成功",icon:"none"});var e=setTimeout((function(){clearTimeout(e),uni.navigateBack({delta:1})}),1e3)}}))},onEye:function(){this.eyeOff=!this.eyeOff}}};e.default=c},7263:function(t,e,n){var i=n("24fb");e=i(!1),e.push([t.i,'@charset "UTF-8";\r\n/* uni.scss */\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 字体变量 */\r\n/* 颜色变量 */\r\n/* 行为相关颜色 */\r\n/* 文字基本颜色 */\r\n/* 背景颜色 */\r\n/* 边框颜色 */\r\n/* 尺寸变量 */\r\n/* 文字尺寸 */\r\n/* 图片尺寸 */\r\n/* Border Radius */\r\n/* 水平间距 */\r\n/* 垂直间距 */\r\n/* 透明度 */\r\n/* 文章场景相关 */.u-countdown[data-v-4f122086]{display:inline-flex;align-items:center}.u-countdown-item[data-v-4f122086]{display:flex;flex-direction:row;align-items:center;justify-content:center;padding:%?2?%;border-radius:%?6?%;white-space:nowrap;-webkit-transform:translateZ(0);transform:translateZ(0)}.u-countdown-time[data-v-4f122086]{margin:0;padding:0;line-height:1}.u-countdown-colon[data-v-4f122086]{display:flex;flex-direction:row;justify-content:center;padding:0 %?5?%;line-height:1;align-items:center;padding-bottom:%?4?%}.u-countdown-scale[data-v-4f122086]{-webkit-transform:scale(.9);transform:scale(.9);-webkit-transform-origin:center center;transform-origin:center center}',""]),t.exports=e},7353:function(t,e,n){"use strict";var i=n("157b"),r=n.n(i);r.a},"76c1":function(t,e,n){"use strict";n.r(e);var i=n("0e55"),r=n.n(i);for(var o in i)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(o);e["default"]=r.a},8084:function(t,e,n){"use strict";n.r(e);var i=n("5703"),r=n("76c1");for(var o in r)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return r[t]}))}(o);n("2b4a");var a=n("f0c5"),s=Object(a["a"])(r["default"],i["b"],i["c"],!1,null,"4f122086",null,!1,i["a"],void 0);e["default"]=s.exports},"8bf6":function(t,e,n){"use strict";n.r(e);var i=n("454d"),r=n.n(i);for(var o in i)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(o);e["default"]=r.a},b680:function(t,e,n){"use strict";var i=n("25f3"),r=n.n(i);r.a},bce9:function(t,e,n){"use strict";n.d(e,"b",(function(){return r})),n.d(e,"c",(function(){return o})),n.d(e,"a",(function(){return i}));var i={uNavbar:n("e501").default,uCountDown:n("8084").default,uIcon:n("c0fb").default},r=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("v-uni-view",{staticClass:"page"},[n("v-uni-view",{staticStyle:{position:"relative","padding-bottom":"30rpx"}},[n("u-navbar",{attrs:{background:"transparent","back-text":t._f("formatState")(t.orderObj.orderStatus),"back-icon-size":30,"back-text-style":{fontSize:"40rpx",marginLeft:"20rpx"},"border-bottom":!1}}),[0==t.orderObj.orderStatus?n("v-uni-view",{staticStyle:{"font-size":"24rpx",color:"#61687C",position:"absolute",left:"60rpx",bottom:"30rpx"}},[t._v("请在"),n("u-count-down",{attrs:{timestamp:t.orderObj.timestamp,"font-size":"24","bg-color":"none",color:"#FF0046",separator:"zh","separator-size":"24","separator-color":"#FF0046"}}),t._v("前完成付款")],1):t._e(),4==t.orderObj.orderStatus?n("v-uni-view",{staticStyle:{"font-size":"24rpx",color:"#61687C",position:"absolute",left:"60rpx",bottom:"30rpx"}},[t._v("订单已取消，期待您再次光临")]):t._e(),1==t.orderObj.orderStatus?n("v-uni-view",{staticStyle:{"font-size":"24rpx",color:"#61687C",position:"absolute",left:"60rpx",bottom:"30rpx"}},[t._v("请到店后使用")]):t._e(),3==t.orderObj.orderStatus?n("v-uni-view",{staticStyle:{"font-size":"24rpx",color:"#61687C",position:"absolute",left:"60rpx",bottom:"30rpx"}},[t._v("订单已完成，期待您再次光临")]):t._e(),7==t.orderObj.orderStatus?n("v-uni-view",{staticStyle:{"font-size":"24rpx",color:"#61687C",position:"absolute",left:"60rpx",bottom:"30rpx"}},[t._v("预计1-5个个工作日到账，请注意查收")]):t._e()]],2),n("v-uni-view",{staticClass:"address"},[t.storeInfo.id?n("v-uni-view",[n("v-uni-view",{staticStyle:{display:"flex","justify-content":"space-between","align-items":"center"}},[n("v-uni-view",{staticClass:"address_top"},[n("v-uni-view",{staticStyle:{display:"flex"}},[n("v-uni-image",{staticStyle:{width:"40rpx",height:"40rpx"},attrs:{src:"/static/img/shop/shop_store.png",mode:"widthFix"}})],1),n("v-uni-view",{staticStyle:{"margin-left":"10rpx","ffont-size":"32rpx","font-weight":"600"}},[t._v(t._s(t.storeInfo.name))])],1),n("v-uni-view",{staticStyle:{display:"flex","align-items":"center","font-size":"24rpx"}},[t._v("切换门店"),n("v-uni-view",{staticClass:"right-icon"})],1)],1),n("v-uni-view",{staticStyle:{"margin-top":"20rpx",color:"#61687C","font-size":"24rpx"}},[t._v(t._s(t.storeInfo.address))])],1):n("v-uni-view",{staticClass:"address_right",staticStyle:{width:"100%",display:"flex","align-items":"center","justify-content":"space-between"}},[n("v-uni-view",{staticStyle:{display:"flex","align-items":"center"}},[n("v-uni-image",{staticStyle:{width:"40rpx",height:"40rpx"},attrs:{src:"/static/img/shop/shop_store.png",mode:"widthFix"}}),n("v-uni-text",{staticStyle:{"margin-left":"10rpx"}},[t._v("请选择门店")])],1),n("v-uni-view",{staticClass:"right-icon"})],1)],1),n("v-uni-view",{staticClass:"product_content"},[n("v-uni-view",{staticClass:"product_info"},[n("v-uni-view",{staticClass:"prodct_left"},[n("v-uni-image",{staticStyle:{width:"164rpx",height:"164rpx"},attrs:{src:t.orderObj.goodsImg}})],1),n("v-uni-view",{staticClass:"product_center"},[n("v-uni-view",{staticClass:"text-ellipsis_2",staticStyle:{"font-size":"28rpx","line-height":"44rpx"}},[t._v(t._s(t.orderObj.goodsName))]),n("v-uni-view",{staticStyle:{display:"flex","justify-content":"space-between","margin-top":"20rpx"}},[n("v-uni-view",{staticStyle:{color:"#61687C","font-size":"24rpx","background-color":"#F2F4F7","text-align":"center",padding:"6rpx 20rpx","border-radius":"6rpx"}},[t._v(t._s(t.orderObj.specification)+"g")])],1)],1),n("v-uni-view",{staticClass:"product_right"},[n("v-uni-view",{staticStyle:{display:"flex","justify-content":"flex-end"}},[n("v-uni-text",{staticClass:"font_small"},[t._v("¥"+t._s(t.orderObj.price))])],1),n("v-uni-view",{staticStyle:{"font-size":"24rpx",color:"#9FA3B0","margin-top":"8rpx","text-align":"right"}},[t._v("x1")]),n("v-uni-view",{staticStyle:{"margin-top":"26rpx"}},[n("v-uni-text",{staticStyle:{color:"#61687C","font-size":"24rpx"}},[t._v("实付")]),n("v-uni-text",{staticStyle:{color:"#171B25","font-size":"28rpx","margin-left":"6rpx"}},[t._v("¥"+t._s(t.orderObj.totalAmt))])],1)],1)],1),1==t.orderObj.orderStatus?n("v-uni-view",{staticClass:"product_yzm"},[n("v-uni-view",{staticStyle:{display:"flex","align-items":"center"}},[n("v-uni-text",[t._v("验证码")]),n("u-icon",{attrs:{name:t.eyeOff?"eye":"eye-off","custom-style":{marginLeft:"10rpx"},size:"36"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.onEye.apply(void 0,arguments)}}})],1),n("v-uni-view",[t._v(t._s(t.eyeOff?t.orderObj.code:t.orderObj.code.replace(/./g,"*")))])],1):t._e()],1),n("v-uni-view",{staticClass:"inviter"},[n("v-uni-view",[t._v("邀请人")]),n("v-uni-view",{staticStyle:{display:"flex","align-items":"center",color:"#61687C"}},[t._v(t._s(t.orderObj.yqrName.name||"-"))])],1),n("v-uni-view",{staticClass:"pay_type"},[n("v-uni-view",[t._v("订单信息")]),n("v-uni-view",{staticStyle:{"margin-top":"24rpx"}},[n("v-uni-text",{staticStyle:{color:"#61687C"}},[t._v("订单编号：")]),n("v-uni-text",[t._v(t._s(t.orderObj.orderNo))])],1),n("v-uni-view",{staticStyle:{"margin-top":"16rpx"}},[n("v-uni-text",{staticStyle:{color:"#61687C"}},[t._v("下单时间：")]),n("v-uni-text",[t._v(t._s(t.orderObj.createDate))])],1),["1","3"].includes(t.orderObj.orderStatus)?n("v-uni-view",{staticStyle:{"margin-top":"16rpx"}},[n("v-uni-text",{staticStyle:{color:"#61687C"}},[t._v("付款时间：")]),n("v-uni-text",[t._v(t._s(t.orderObj.payTime))])],1):t._e(),n("v-uni-view",{staticStyle:{"margin-top":"16rpx"}},[n("v-uni-text",{staticStyle:{color:"#61687C"}},[t._v("微信支付：")]),n("v-uni-text",[t._v("¥"+t._s(t.orderObj.totalAmt))])],1)],1),0==t.orderObj.orderStatus?n("v-uni-view",{staticClass:"goods_detail_footer"},[n("v-uni-button",{staticClass:"btn",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.settleOrder.apply(void 0,arguments)}}},[t._v("确认支付")])],1):t._e()],1)},o=[]},d0ba:function(t,e,n){"use strict";n.d(e,"b",(function(){return r})),n.d(e,"c",(function(){return o})),n.d(e,"a",(function(){return i}));var i={uIcon:n("c0fb").default},r=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("v-uni-view",{},[n("v-uni-view",{staticClass:"u-navbar",class:{"u-navbar-fixed":t.isFixed,"u-border-bottom":t.borderBottom},style:[t.navbarStyle]},[n("v-uni-view",{staticClass:"u-status-bar",style:{height:t.statusBarHeight+"px"}}),n("v-uni-view",{staticClass:"u-navbar-inner",style:[t.navbarInnerStyle]},[t.isBack?n("v-uni-view",{staticClass:"u-back-wrap",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.goBack.apply(void 0,arguments)}}},[n("v-uni-view",{staticClass:"u-icon-wrap"},[n("u-icon",{attrs:{name:t.backIconName,color:t.backIconColor,size:t.backIconSize}})],1),t.backText?n("v-uni-view",{staticClass:"u-icon-wrap u-back-text u-line-1",style:[t.backTextStyle]},[t._v(t._s(t.backText))]):t._e()],1):t._e(),t.title?n("v-uni-view",{staticClass:"u-navbar-content-title",style:[t.titleStyle]},[n("v-uni-view",{staticClass:"u-title u-line-1",style:{color:t.titleColor,fontSize:t.titleSize+"rpx",fontWeight:t.titleBold?"bold":"normal"}},[t._v(t._s(t.title))])],1):t._e(),n("v-uni-view",{staticClass:"u-slot-content"},[t._t("default")],2),n("v-uni-view",{staticClass:"u-slot-right"},[t._t("right")],2)],1)],1),t.isFixed&&!t.immersive?n("v-uni-view",{staticClass:"u-navbar-placeholder",style:{width:"100%",height:Number(t.navbarHeight)+t.statusBarHeight+"px"}}):t._e()],1)},o=[]},e501:function(t,e,n){"use strict";n.r(e);var i=n("d0ba"),r=n("8bf6");for(var o in r)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return r[t]}))}(o);n("7353");var a=n("f0c5"),s=Object(a["a"])(r["default"],i["b"],i["c"],!1,null,"2920cc37",null,!1,i["a"],void 0);e["default"]=s.exports}}]);