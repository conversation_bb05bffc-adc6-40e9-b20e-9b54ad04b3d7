{"version": 3, "sources": ["webpack:///E:/pos/黄金/gold_client/node_modules/uview-ui/components/u-field/u-field.vue?cfd2", "webpack:///E:/pos/黄金/gold_client/node_modules/uview-ui/components/u-field/u-field.vue?4396", "webpack:///E:/pos/黄金/gold_client/node_modules/uview-ui/components/u-field/u-field.vue?b71b", "webpack:///E:/pos/黄金/gold_client/node_modules/uview-ui/components/u-field/u-field.vue?5f60", "uni-app:///node_modules/uview-ui/components/u-field/u-field.vue", "webpack:///E:/pos/黄金/gold_client/node_modules/uview-ui/components/u-field/u-field.vue?d7cc", "webpack:///E:/pos/黄金/gold_client/node_modules/uview-ui/components/u-field/u-field.vue?dca5"], "names": ["name", "props", "icon", "rightIcon", "required", "label", "password", "clearable", "type", "default", "labelWidth", "labelAlign", "inputAlign", "iconColor", "autoHeight", "errorMessage", "placeholder", "placeholder<PERSON><PERSON><PERSON>", "focus", "fixed", "value", "disabled", "maxlength", "confirmType", "labelPosition", "fieldStyle", "clearSize", "iconStyle", "borderTop", "borderBottom", "trim", "data", "focused", "itemIndex", "computed", "inputWrapStyle", "style", "rightIconStyle", "labelStyle", "justifyContent", "inputMaxlength", "fieldInnerStyle", "methods", "onInput", "onFocus", "onBlur", "setTimeout", "onConfirm", "onClear", "rightIconClick", "fieldClick"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAgI;AAChI;AAC2D;AACL;AACsC;;;AAG5F;AACgL;AAChL,gBAAgB,yLAAU;AAC1B,EAAE,6EAAM;AACR,EAAE,8FAAM;AACR,EAAE,uGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,kGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,+NAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AChDA;AAAA;AAAA;AAAA;AAAwpB,CAAgB,6qBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACmD5qB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AApCA,gBAqCA;EACAA;EACAC;IACAC;IACAC;IACA;IACA;IACA;IACA;IACAC;IACAC;IACAC;IACAC;MACAC;MACAC;IACA;IACA;IACAC;MACAF;MACAC;IACA;IACA;IACAE;MACAH;MACAC;IACA;IACAG;MACAJ;MACAC;IACA;IACAI;MACAL;MACAC;IACA;IACAK;MACAN;MACAC;IACA;IACAM;MACAP;MACAC;IACA;IACAO;IACAC;IACAC;IACAC;IACAC;IACAZ;MACAA;MACAC;IACA;IACAY;MACAb;MACAC;IACA;IACAa;MACAd;MACAC;IACA;IACAc;MACAf;MACAC;IACA;IACA;IACAe;MACAhB;MACAC;IACA;IACA;IACAgB;MACAjB;MACAC;QACA;MACA;IACA;IACA;IACAiB;MACAlB;MACAC;IACA;IACA;IACAkB;MACAnB;MACAC;QACA;MACA;IACA;IACA;IACAmB;MACApB;MACAC;IACA;IACA;IACAoB;MACArB;MACAC;IACA;IACA;IACAqB;MACAtB;MACAC;IACA;EACA;EACAsB;IACA;MACAC;MACAC;IACA;EACA;EACAC;IACAC;MACA;MACAC;MACA;MACA;QACAA;MACA;QACA;QACAA;MACA;MACA;IACA;IACAC;MACA;MACA;MACA,2EACAD;MACA;IACA;IACAE;MACA;MACA;MACA;MACA;MACA;IACA;IACA;IACAC;MACA;MACA;MACA;IACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;MACA;QACAL;MACA;QACAA;MACA;MAEA;IACA;EACA;EACAM;IACAC;MACA;MACA;MACA;MACA;IACA;IACAC;MACA;MACA;IACA;IACAC;MAAA;MACA;MACA;MACAC;QACA;MACA;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACA;IACA;IACAC;MACA;IACA;EACA;AACA;AAAA,4B;;;;;;;;;;;;ACvRA;AAAA;AAAA;AAAA;AAAuwC,CAAgB,ouCAAG,EAAC,C;;;;;;;;;;;ACA3xC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "node-modules/uview-ui/components/u-field/u-field.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./u-field.vue?vue&type=template&id=578c626d&scoped=true&\"\nvar renderjs\nimport script from \"./u-field.vue?vue&type=script&lang=js&\"\nexport * from \"./u-field.vue?vue&type=script&lang=js&\"\nimport style0 from \"./u-field.vue?vue&type=style&index=0&id=578c626d&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"578c626d\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"node_modules/uview-ui/components/u-field/u-field.vue\"\nexport default component.exports", "export * from \"-!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-field.vue?vue&type=template&id=578c626d&scoped=true&\"", "var components\ntry {\n  components = {\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-icon/u-icon\" */ \"uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var s0 = _vm.__get_style([_vm.inputWrapStyle])\n  var s1 = _vm.type == \"textarea\" ? _vm.__get_style([_vm.fieldStyle]) : null\n  var s2 = !(_vm.type == \"textarea\") ? _vm.__get_style([_vm.fieldStyle]) : null\n  var s3 = _vm.rightIcon ? _vm.__get_style([_vm.rightIconStyle]) : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        s0: s0,\n        s1: s1,\n        s2: s2,\n        s3: s3,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-field.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-field.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"u-field\" :class=\"{'u-border-top': borderTop, 'u-border-bottom': borderBottom }\">\r\n\t\t<view class=\"u-field-inner\" :class=\"[type == 'textarea' ? 'u-textarea-inner' : '', 'u-label-postion-' + labelPosition]\">\r\n\t\t\t<view class=\"u-label\" :class=\"[required ? 'u-required' : '']\" :style=\"{\r\n\t\t\t\tjustifyContent: justifyContent, \r\n\t\t\t\tflex: labelPosition == 'left' ? `0 0 ${labelWidth}rpx` : '1'\r\n\t\t\t}\">\r\n\t\t\t\t<view class=\"u-icon-wrap\" v-if=\"icon\">\r\n\t\t\t\t\t<u-icon size=\"32\" :custom-style=\"iconStyle\" :name=\"icon\" :color=\"iconColor\" class=\"u-icon\"></u-icon>\r\n\t\t\t\t</view>\r\n\t\t\t\t<slot name=\"icon\"></slot>\r\n\t\t\t\t<text class=\"u-label-text\" :class=\"[this.$slots.icon || icon ? 'u-label-left-gap' : '']\">{{ label }}</text>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"fild-body\">\r\n\t\t\t\t<view class=\"u-flex-1 u-flex\" :style=\"[inputWrapStyle]\">\r\n\t\t\t\t\t<textarea v-if=\"type == 'textarea'\" class=\"u-flex-1 u-textarea-class\" :style=\"[fieldStyle]\" :value=\"value\"\r\n\t\t\t\t\t :placeholder=\"placeholder\" :placeholderStyle=\"placeholderStyle\" :disabled=\"disabled\" :maxlength=\"inputMaxlength\"\r\n\t\t\t\t\t :focus=\"focus\" :autoHeight=\"autoHeight\" :fixed=\"fixed\" @input=\"onInput\" @blur=\"onBlur\" @focus=\"onFocus\" @confirm=\"onConfirm\"\r\n\t\t\t\t\t @tap=\"fieldClick\" />\r\n\t\t\t\t\t<input\r\n\t\t\t\t\t\tv-else\r\n\t\t\t\t\t\t:style=\"[fieldStyle]\"\r\n\t\t\t\t\t\t:type=\"type\"\r\n\t\t\t\t\t\tclass=\"u-flex-1 u-field__input-wrap\"\r\n\t\t\t\t\t\t:value=\"value\"\r\n\t\t\t\t\t\t:password=\"password || this.type === 'password'\"\r\n\t\t\t\t\t\t:placeholder=\"placeholder\"\r\n\t\t\t\t\t\t:placeholderStyle=\"placeholderStyle\"\r\n\t\t\t\t\t\t:disabled=\"disabled\"\r\n\t\t\t\t\t\t:maxlength=\"inputMaxlength\"\r\n\t\t\t\t\t\t:focus=\"focus\"\r\n\t\t\t\t\t\t:confirmType=\"confirmType\"\r\n\t\t\t\t\t\t@focus=\"onFocus\"\r\n\t\t\t\t\t\t@blur=\"onBlur\"\r\n\t\t\t\t\t\t@input=\"onInput\"\r\n\t\t\t\t\t\t@confirm=\"onConfirm\"\r\n\t\t\t\t\t\t@tap=\"fieldClick\"\r\n\t\t\t\t\t/>\r\n\t\t\t\t</view>\r\n\t\t\t\t<u-icon :size=\"clearSize\" v-if=\"clearable && value != '' && focused\" name=\"close-circle-fill\" color=\"#c0c4cc\" class=\"u-clear-icon\" @click=\"onClear\"/>\r\n\t\t\t\t<view class=\"u-button-wrap\"><slot name=\"right\" /></view>\r\n\t\t\t\t<u-icon v-if=\"rightIcon\" @click=\"rightIconClick\" :name=\"rightIcon\" color=\"#c0c4cc\" :style=\"[rightIconStyle]\" size=\"26\" class=\"u-arror-right\" />\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view v-if=\"errorMessage !== false && errorMessage != ''\" class=\"u-error-message\" :style=\"{\r\n\t\t\tpaddingLeft: labelWidth + 'rpx'\r\n\t\t}\">{{ errorMessage }}</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n/**\r\n * field 输入框\r\n * @description 借助此组件，可以实现表单的输入， 有\"text\"和\"textarea\"类型的，此外，借助uView的picker和actionSheet组件可以快速实现上拉菜单，时间，地区选择等， 为表单解决方案的利器。\r\n * @tutorial https://www.uviewui.com/components/field.html\r\n * @property {String} type 输入框的类型（默认text）\r\n * @property {String} icon label左边的图标，限uView的图标名称\r\n * @property {Object} icon-style 左边图标的样式，对象形式\r\n * @property {Boolean} right-icon 输入框右边的图标名称，限uView的图标名称（默认false）\r\n * @property {Boolean} required 是否必填，左边您显示红色\"*\"号（默认false）\r\n * @property {String} label 输入框左边的文字提示\r\n * @property {Boolean} password 是否密码输入方式(用点替换文字)，type为text时有效（默认false）\r\n * @property {Boolean} clearable 是否显示右侧清空内容的图标控件(输入框有内容，且获得焦点时才显示)，点击可清空输入框内容（默认true）\r\n * @property {Number String} label-width label的宽度，单位rpx（默认130）\r\n * @property {String} label-align label的文字对齐方式（默认left）\r\n * @property {Object} field-style 自定义输入框的样式，对象形式\r\n * @property {Number | String} clear-size 清除图标的大小，单位rpx（默认30）\r\n * @property {String} input-align 输入框内容对齐方式（默认left）\r\n * @property {Boolean} border-bottom 是否显示field的下边框（默认true）\r\n * @property {Boolean} border-top 是否显示field的上边框（默认false）\r\n * @property {String} icon-color 左边通过icon配置的图标的颜色（默认#606266）\r\n * @property {Boolean} auto-height 是否自动增高输入区域，type为textarea时有效（默认true）\r\n * @property {String Boolean} error-message 显示的错误提示内容，如果为空字符串或者false，则不显示错误信息\r\n * @property {String} placeholder 输入框的提示文字\r\n * @property {String} placeholder-style placeholder的样式(内联样式，字符串)，如\"color: #ddd\"\r\n * @property {Boolean} focus 是否自动获得焦点（默认false）\r\n * @property {Boolean} fixed 如果type为textarea，且在一个\"position:fixed\"的区域，需要指明为true（默认false）\r\n * @property {Boolean} disabled 是否不可输入（默认false）\r\n * @property {Number String} maxlength 最大输入长度，设置为 -1 的时候不限制最大长度（默认140）\r\n * @property {String} confirm-type 设置键盘右下角按钮的文字，仅在type=\"text\"时生效（默认done）\r\n * @event {Function} input 输入框内容发生变化时触发\r\n * @event {Function} focus 输入框获得焦点时触发\r\n * @event {Function} blur 输入框失去焦点时触发\r\n * @event {Function} confirm 点击完成按钮时触发\r\n * @event {Function} right-icon-click 通过right-icon生成的图标被点击时触发\r\n * @event {Function} click 输入框被点击或者通过right-icon生成的图标被点击时触发，这样设计是考虑到传递右边的图标，一般都为需要弹出\"picker\"等操作时的场景，点击倒三角图标，理应发出此事件，见上方说明\r\n * @example <u-field v-model=\"mobile\" label=\"手机号\" required :error-message=\"errorMessage\"></u-field>\r\n */\r\nexport default {\r\n\tname:\"u-field\",\r\n\tprops: {\r\n\t\ticon: String,\r\n\t\trightIcon: String,\r\n\t\t// arrowDirection: {\r\n\t\t// \ttype: String,\r\n\t\t// \tdefault: 'right'\r\n\t\t// },\r\n\t\trequired: Boolean,\r\n\t\tlabel: String,\r\n\t\tpassword: Boolean,\r\n\t\tclearable: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: true\r\n\t\t},\r\n\t\t// 左边标题的宽度单位rpx\r\n\t\tlabelWidth: {\r\n\t\t\ttype: [Number, String],\r\n\t\t\tdefault: 130\r\n\t\t},\r\n\t\t// 对齐方式，left|center|right\r\n\t\tlabelAlign: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: 'left'\r\n\t\t},\r\n\t\tinputAlign: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: 'left'\r\n\t\t},\r\n\t\ticonColor: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: '#606266'\r\n\t\t},\r\n\t\tautoHeight: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: true\r\n\t\t},\r\n\t\terrorMessage: {\r\n\t\t\ttype: [String, Boolean],\r\n\t\t\tdefault: ''\r\n\t\t},\r\n\t\tplaceholder: String,\r\n\t\tplaceholderStyle: String,\r\n\t\tfocus: Boolean,\r\n\t\tfixed: Boolean,\r\n\t\tvalue: [Number, String],\r\n\t\ttype: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: 'text'\r\n\t\t},\r\n\t\tdisabled: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: false\r\n\t\t},\r\n\t\tmaxlength: {\r\n\t\t\ttype: [Number, String],\r\n\t\t\tdefault: 140\r\n\t\t},\r\n\t\tconfirmType: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: 'done'\r\n\t\t},\r\n\t\t// lable的位置，可选为 left-左边，top-上边\r\n\t\tlabelPosition: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: 'left'\r\n\t\t},\r\n\t\t// 输入框的自定义样式\r\n\t\tfieldStyle: {\r\n\t\t\ttype: Object,\r\n\t\t\tdefault() {\r\n\t\t\t\treturn {}\r\n\t\t\t}\r\n\t\t},\r\n\t\t// 清除按钮的大小\r\n\t\tclearSize: {\r\n\t\t\ttype: [Number, String],\r\n\t\t\tdefault: 30\r\n\t\t},\r\n\t\t// lable左边的图标样式，对象形式\r\n\t\ticonStyle: {\r\n\t\t\ttype: Object,\r\n\t\t\tdefault() {\r\n\t\t\t\treturn {}\r\n\t\t\t}\r\n\t\t},\r\n\t\t// 是否显示上边框\r\n\t\tborderTop: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: false\r\n\t\t},\r\n\t\t// 是否显示下边框\r\n\t\tborderBottom: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: true\r\n\t\t},\r\n\t\t// 是否自动去除两端的空格\r\n\t\ttrim: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: true\r\n\t\t}\r\n\t},\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\tfocused: false,\r\n\t\t\titemIndex: 0,\r\n\t\t};\r\n\t},\r\n\tcomputed: {\r\n\t\tinputWrapStyle() {\r\n\t\t\tlet style = {};\r\n\t\t\tstyle.textAlign = this.inputAlign;\r\n\t\t\t// 判断lable的位置，如果是left的话，让input左边两边有间隙\r\n\t\t\tif(this.labelPosition == 'left') {\r\n\t\t\t\tstyle.margin = `0 8rpx`;\r\n\t\t\t} else {\r\n\t\t\t\t// 如果lable是top的，input的左边就没必要有间隙了\r\n\t\t\t\tstyle.marginRight = `8rpx`;\r\n\t\t\t}\r\n\t\t\treturn style;\r\n\t\t},\r\n\t\trightIconStyle() {\r\n\t\t\tlet style = {};\r\n\t\t\tif (this.arrowDirection == 'top') style.transform = 'roate(-90deg)';\r\n\t\t\tif (this.arrowDirection == 'bottom') style.transform = 'roate(90deg)';\r\n\t\t\telse style.transform = 'roate(0deg)';\r\n\t\t\treturn style;\r\n\t\t},\r\n\t\tlabelStyle() {\r\n\t\t\tlet style = {};\r\n\t\t\tif(this.labelAlign == 'left') style.justifyContent = 'flext-start';\r\n\t\t\tif(this.labelAlign == 'center') style.justifyContent = 'center';\r\n\t\t\tif(this.labelAlign == 'right') style.justifyContent = 'flext-end';\r\n\t\t\treturn style;\r\n\t\t},\r\n\t\t// uni不支持在computed中写style.justifyContent = 'center'的形式，故用此方法\r\n\t\tjustifyContent() {\r\n\t\t\tif(this.labelAlign == 'left') return 'flex-start';\r\n\t\t\tif(this.labelAlign == 'center') return 'center';\r\n\t\t\tif(this.labelAlign == 'right') return 'flex-end';\r\n\t\t},\r\n\t\t// 因为uniapp的input组件的maxlength组件必须要数值，这里转为数值，给用户可以传入字符串数值\r\n\t\tinputMaxlength() {\r\n\t\t\treturn Number(this.maxlength)\r\n\t\t},\r\n\t\t// label的位置\r\n\t\tfieldInnerStyle() {\r\n\t\t\tlet style = {};\r\n\t\t\tif(this.labelPosition == 'left') {\r\n\t\t\t\tstyle.flexDirection = 'row';\r\n\t\t\t} else {\r\n\t\t\t\tstyle.flexDirection = 'column';\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\treturn style;\r\n\t\t}\r\n\t},\r\n\tmethods: {\r\n\t\tonInput(event) {\r\n\t\t\tlet value = event.detail.value;\r\n\t\t\t// 判断是否去除空格\r\n\t\t\tif(this.trim) value = this.$u.trim(value);\r\n\t\t\tthis.$emit('input', value);\r\n\t\t},\r\n\t\tonFocus(event) {\r\n\t\t\tthis.focused = true;\r\n\t\t\tthis.$emit('focus', event);\r\n\t\t},\r\n\t\tonBlur(event) {\r\n\t\t\t// 最开始使用的是监听图标@touchstart事件，自从hx2.8.4后，此方法在微信小程序出错\r\n\t\t\t// 这里改为监听点击事件，手点击清除图标时，同时也发生了@blur事件，导致图标消失而无法点击，这里做一个延时\r\n\t\t\tsetTimeout(() => {\r\n\t\t\t\tthis.focused = false;\r\n\t\t\t}, 100)\r\n\t\t\tthis.$emit('blur', event);\r\n\t\t},\r\n\t\tonConfirm(e) {\r\n\t\t\tthis.$emit('confirm', e.detail.value);\r\n\t\t},\r\n\t\tonClear(event) {\r\n\t\t\tthis.$emit('input', '');\r\n\t\t},\r\n\t\trightIconClick() {\r\n\t\t\tthis.$emit('right-icon-click');\r\n\t\t\tthis.$emit('click');\r\n\t\t},\r\n\t\tfieldClick() {\r\n\t\t\tthis.$emit('click');\r\n\t\t}\r\n\t}\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n@import \"../../libs/css/style.components.scss\";\r\n\t\r\n.u-field {\r\n\tfont-size: 28rpx;\r\n\tpadding: 20rpx 28rpx;\r\n\ttext-align: left;\r\n\tposition: relative;\r\n\tcolor: $u-main-color;\r\n}\r\n\r\n.u-field-inner {\r\n\t@include vue-flex;\r\n\talign-items: center;\r\n}\r\n\r\n.u-textarea-inner {\r\n\talign-items: flex-start;\r\n}\r\n\r\n.u-textarea-class {\r\n\tmin-height: 96rpx;\r\n\twidth: auto;\r\n\tfont-size: 28rpx;\r\n}\r\n\r\n.fild-body {\r\n\t@include vue-flex;\r\n\tflex: 1;\r\n\talign-items: center;\r\n}\r\n\r\n.u-arror-right {\r\n\tmargin-left: 8rpx;\r\n}\r\n\r\n.u-label-text {\r\n\t/* #ifndef APP-NVUE */\r\n\tdisplay: inline-flex;\t\t\r\n\t/* #endif */\r\n}\r\n\r\n.u-label-left-gap {\r\n\tmargin-left: 6rpx;\r\n}\r\n\r\n.u-label-postion-top {\r\n\tflex-direction: column;\r\n\talign-items: flex-start;\r\n}\r\n\r\n.u-label {\r\n\twidth: 130rpx;\r\n\tflex: 1 1 130rpx;\r\n\ttext-align: left;\r\n\tposition: relative;\r\n\t@include vue-flex;\r\n\talign-items: center;\r\n}\r\n\r\n.u-required::before {\r\n\tcontent: '*';\r\n\tposition: absolute;\r\n\tleft: -16rpx;\r\n\tfont-size: 14px;\r\n\tcolor: $u-type-error;\r\n\theight: 9px;\r\n\tline-height: 1;\r\n}\r\n\r\n.u-field__input-wrap {\r\n\tposition: relative;\r\n\toverflow: hidden;\r\n\tfont-size: 28rpx;\r\n\theight: 48rpx;\r\n\tflex: 1;\r\n\twidth: auto;\r\n}\r\n\r\n.u-clear-icon {\r\n\t@include vue-flex;\r\n\talign-items: center;\r\n}\r\n\r\n.u-error-message {\r\n\tcolor: $u-type-error;\r\n\tfont-size: 26rpx;\r\n\ttext-align: left;\r\n}\r\n\r\n.placeholder-style {\r\n\tcolor: rgb(150, 151, 153);\r\n}\r\n\r\n.u-input-class {\r\n\tfont-size: 28rpx;\r\n}\r\n\r\n.u-button-wrap {\r\n\tmargin-left: 8rpx;\r\n}\r\n</style>\r\n", "import mod from \"-!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-field.vue?vue&type=style&index=0&id=578c626d&lang=scss&scoped=true&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-field.vue?vue&type=style&index=0&id=578c626d&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754273099982\n      var cssReload = require(\"D:/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}