(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-notice-noticesList"],{2596:function(t,e,n){var i=n("e887");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var a=n("4f06").default;a("482d7ce9",i,!0,{sourceMap:!1,shadowMode:!1})},"2cca":function(t,e,n){var i=n("24fb");e=i(!1),e.push([t.i,'@charset "UTF-8";\r\n/* uni.scss */\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 字体变量 */\r\n/* 颜色变量 */\r\n/* 行为相关颜色 */\r\n/* 文字基本颜色 */\r\n/* 背景颜色 */\r\n/* 边框颜色 */\r\n/* 尺寸变量 */\r\n/* 文字尺寸 */\r\n/* 图片尺寸 */\r\n/* Border Radius */\r\n/* 水平间距 */\r\n/* 垂直间距 */\r\n/* 透明度 */\r\n/* 文章场景相关 */.notice-list .each-notice[data-v-719e5e34]{display:flex;align-items:center;justify-content:space-between;padding:%?24?%;border-bottom:%?1?% solid #f7f7f7;background-color:#fff}.notice-list .each-notice .left[data-v-719e5e34]{flex:1}.notice-list .each-notice .left .notice-title[data-v-719e5e34]{flex:1;flex-grow:1;display:flex;flex-direction:column;font-size:%?28?%;margin-bottom:%?10?%}.notice-list .each-notice .left .notice-title .notice-name[data-v-719e5e34]{color:#333;margin-right:%?20?%}.notice-list .each-notice .left .notice-title .notice-content[data-v-719e5e34]{color:#a7a7a7;font-size:%?24?%;margin-top:%?10?%;overflow:hidden;text-overflow:ellipsis;display:-webkit-box;-webkit-line-clamp:2;-webkit-box-orient:vertical}.notice-list .each-notice .left .notice-time[data-v-719e5e34]{font-size:%?24?%;color:#999;text-align:right}.notice-list .each-notice .right[data-v-719e5e34]{width:%?40?%}',""]),t.exports=e},"3bb5":function(t,e,n){"use strict";var i=n("2596"),a=n.n(i);a.a},4039:function(t,e,n){"use strict";n("7a82");var i=n("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var a=i(n("c7eb")),o=i(n("1da1")),c=n("30ea"),r=n("9e56"),s={name:"noticesList",data:function(){return{noticeType:"",noticesData:[{id:20250002,name:"更新通知",time:"2023-12-12 06:45:18",content:"我们已经更新了一些功能，欢迎您继续使用！tabbar是原生的，层级高于前端元素，所以会遮挡住前端元素。uni-app插件市场有封装的前端tabbar，但性能不如原生tabbar"},{id:20250001,name:"上线通知",time:"2023-09-01 20:32:05",content:"今天是我们上线的日子，我们将为您带来更好的服务，请您多多支持！如果想要一个中间带+号的tabbar，在HBuilderX中新建uni-app项目、选择 底部选项卡 模板以上大部分操作 tabbar 的 API 需要在 tabbar 渲染后才能使用，避免在 tabbar 未初始化前使用"}]}},onLoad:function(t){this.noticeType=t.type,uni.setNavigationBarTitle({title:this.noticeType}),this.getDataList()},methods:{getDataList:function(){var t=this;return(0,o.default)((0,a.default)().mark((function e(){var n;return(0,a.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,r.request(c.noticeListUrl,{page:1,limit:10,type:"官方公告"===t.noticeType?"0":"1"},"POST");case 2:n=e.sent,console.log(n),0!==n.code?uni.showToast({title:n.msg,icon:"none"}):t.noticesData=n.data.records||[];case 5:case"end":return e.stop()}}),e)})))()},toNoticeDetail:function(t){uni.setStorageSync("noticeData",t),uni.navigateTo({url:"/pages/notice/noticeDetail?type="+this.noticeType+"&id="+t.id})}}};e.default=s},"5a6f":function(t,e,n){"use strict";n.d(e,"b",(function(){return i})),n.d(e,"c",(function(){return a})),n.d(e,"a",(function(){}));var i=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("v-uni-view",{staticClass:"notice-list"},t._l(t.noticesData,(function(e,i){return n("v-uni-view",{key:i,staticClass:"each-notice",on:{click:function(n){arguments[0]=n=t.$handleEvent(n),t.toNoticeDetail(e)}}},[n("v-uni-view",{staticClass:"left"},[n("v-uni-view",{staticClass:"notice-title"},[n("v-uni-view",{staticClass:"notice-name"},[t._v(t._s(e.title))]),n("v-uni-view",{staticClass:"notice-content"},[t._v(t._s(e.content))])],1),n("v-uni-view",{staticClass:"notice-time"},[t._v(t._s(e.createDate))])],1),n("v-uni-view",{staticClass:"right-icon"})],1)})),1)},a=[]},8333:function(t,e,n){"use strict";n.r(e);var i=n("4039"),a=n.n(i);for(var o in i)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(o);e["default"]=a.a},ab0a:function(t,e,n){"use strict";var i=n("b673"),a=n.n(i);a.a},b4c5:function(t,e,n){"use strict";n.r(e);var i=n("5a6f"),a=n("8333");for(var o in a)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(o);n("3bb5"),n("ab0a");var c=n("f0c5"),r=Object(c["a"])(a["default"],i["b"],i["c"],!1,null,"719e5e34",null,!1,i["a"],void 0);e["default"]=r.exports},b673:function(t,e,n){var i=n("2cca");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var a=n("4f06").default;a("8c4fd214",i,!0,{sourceMap:!1,shadowMode:!1})},e887:function(t,e,n){var i=n("24fb");e=i(!1),e.push([t.i,"uni-page-body[data-v-719e5e34]{background-color:#fafafa}body.?%PAGE?%[data-v-719e5e34]{background-color:#fafafa}",""]),t.exports=e}}]);