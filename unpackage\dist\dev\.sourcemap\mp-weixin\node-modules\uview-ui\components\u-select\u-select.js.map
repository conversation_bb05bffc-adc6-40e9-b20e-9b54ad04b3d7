{"version": 3, "sources": ["webpack:///E:/Home/ma-Yi/gold/node_modules/uview-ui/components/u-select/u-select.vue?43ef", "webpack:///E:/Home/ma-Yi/gold/node_modules/uview-ui/components/u-select/u-select.vue?4d3c", "webpack:///E:/Home/ma-Yi/gold/node_modules/uview-ui/components/u-select/u-select.vue?0d4b", "webpack:///E:/Home/ma-Yi/gold/node_modules/uview-ui/components/u-select/u-select.vue?be27", "uni-app:///node_modules/uview-ui/components/u-select/u-select.vue", "webpack:///E:/Home/ma-Yi/gold/node_modules/uview-ui/components/u-select/u-select.vue?34d7", "webpack:///E:/Home/ma-Yi/gold/node_modules/uview-ui/components/u-select/u-select.vue?6612"], "names": ["props", "list", "type", "default", "border", "value", "cancelColor", "confirmColor", "zIndex", "safeAreaInsetBottom", "maskCloseAble", "defaultValue", "mode", "valueName", "labelName", "<PERSON><PERSON><PERSON>", "title", "cancelText", "confirmText", "data", "defaultSelector", "columnData", "selectValue", "lastSelectIndex", "columnNum", "moving", "watch", "immediate", "handler", "computed", "uZIndex", "methods", "pickstart", "pickend", "init", "setDefaultSelector", "setColumnNum", "column", "num", "setColumnData", "setSelectValue", "tmp", "label", "columnChange", "columnIndex", "close", "getResult", "<PERSON><PERSON><PERSON><PERSON>"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAiI;AACjI;AAC4D;AACL;AACsC;;;AAG7F;AACqM;AACrM,gBAAgB,8MAAU;AAC1B,EAAE,8EAAM;AACR,EAAE,+FAAM;AACR,EAAE,wGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,mGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,qOAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjCA;AAAA;AAAA;AAAA;AAAkwB,CAAgB,uxBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACoDtxB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AApBA,gBAsBA;EACAA;IACA;IACAC;MACAC;MACAC;QACA;MACA;IACA;IACA;IACAC;MACAF;MACAC;IACA;IACA;IACAE;MACAH;MACAC;IACA;IACA;IACAG;MACAJ;MACAC;IACA;IACA;IACAI;MACAL;MACAC;IACA;IACA;IACAK;MACAN;MACAC;IACA;IACAM;MACAP;MACAC;IACA;IACA;IACAO;MACAR;MACAC;IACA;IACA;IACAQ;MACAT;MACAC;QACA;MACA;IACA;IACA;IACAS;MACAV;MACAC;IACA;IACA;IACAU;MACAX;MACAC;IACA;IACA;IACAW;MACAZ;MACAC;IACA;IACA;IACAY;MACAb;MACAC;IACA;IACA;IACAa;MACAd;MACAC;IACA;IACA;IACAc;MACAf;MACAC;IACA;IACA;IACAe;MACAhB;MACAC;IACA;EACA;EACAgB;IACA;MACA;MACAC;MACA;MACAC;MACA;MACAC;MACA;MACAC;MACA;MACAC;MACA;MACAC;IACA;EACA;EACAC;IACA;IACArB;MACAsB;MACAC;QAAA;QACA;UAAA;QAAA;MACA;IACA;EACA;EACAC;IACAC;MACA;MACA;IACA;EACA;EACAC;IACA;IACAC;MAEA;IAEA;IACA;IACAC;MAEA;IAEA;IACAC;MACA;MACA;MACA;MACA;IACA;IACA;IACAC;MACA;MACA;MACA;IACA;IACA;IACAC;MACA;MACA;MACA;MAAA,KACA;MACA;MAAA,KACA;QACA;QACA;QACA;QACA;UACAC;UACAC;QACA;QACA;MACA;IACA;IACA;IACAC;MACA;MACA;MACA;QACA;QACA;QACA;QACA;UACA;UACA;YACApB;YACAkB;UACA;YACA;YACAlB;YACAkB;UACA;QACA;MACA;QACAlB;MACA;QACAA;MACA;MACA;IACA;IACA;IACAqB;MACA;MACA;QACAC;QACA;UACApC;UACAqC;QACA;QACA;QACA;QACA;MACA;IACA;IACA;IACAC;MAAA;MACA;MACA;MACA;MACA;MACA;MACA;QACA;QACA;UACA;QACA;QAEA;UACA;UACA;UACA;UACA;UACA;QACA;QACA;QACA;QACAC;UACA;UACA;YACAvC;YACAqC;UACA;UACA;UACA;UACA;QAEA;QACA;QACA;MACA;QACA;QACA;QACA;UACArC;UACAqC;QACA;QACA;QACA;QACA;MACA;QACA;QACAE;UACA;UACA;UACA;YACAvC;YACAqC;UACA;UACA;UACA;UACA;QACA;MACA;IACA;IACAG;MACA;MACA;MACA;IACA;IACA;IACAC;MAAA;MAEA;MAEA;MACA;IACA;IACAC;MACA;IACA;EACA;AACA;AAAA,4B;;;;;;;;;;;;AC/VA;AAAA;AAAA;AAAA;AAAq8C,CAAgB,k6CAAG,EAAC,C;;;;;;;;;;;ACAz9C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "node-modules/uview-ui/components/u-select/u-select.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./u-select.vue?vue&type=template&id=7b211ee7&scoped=true&\"\nvar renderjs\nimport script from \"./u-select.vue?vue&type=script&lang=js&\"\nexport * from \"./u-select.vue?vue&type=script&lang=js&\"\nimport style0 from \"./u-select.vue?vue&type=style&index=0&id=7b211ee7&scoped=true&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"7b211ee7\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"node_modules/uview-ui/components/u-select/u-select.vue\"\nexport default component.exports", "export * from \"-!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-select.vue?vue&type=template&id=7b211ee7&scoped=true&\"", "var components\ntry {\n  components = {\n    uPopup: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-popup/u-popup\" */ \"uview-ui/components/u-popup/u-popup.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-select.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-select.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"u-select\">\r\n\t\t<!-- <view class=\"u-select__action\" :class=\"{\r\n\t\t\t'u-select--border': border\r\n\t\t}\" @tap.stop=\"selectHandler\">\r\n\t\t\t<view class=\"u-select__action__icon\" :class=\"{\r\n\t\t\t\t'u-select__action__icon--reverse': value == true\r\n\t\t\t}\">\r\n\t\t\t\t<u-icon name=\"arrow-down-fill\" size=\"26\" color=\"#c0c4cc\"></u-icon>\r\n\t\t\t</view>\r\n\t\t</view> -->\r\n\t\t<u-popup :maskCloseAble=\"maskCloseAble\" mode=\"bottom\" :popup=\"false\" v-model=\"value\" length=\"auto\" :safeAreaInsetBottom=\"safeAreaInsetBottom\" @close=\"close\" :z-index=\"uZIndex\">\r\n\t\t\t<view class=\"u-select\">\r\n\t\t\t\t<view class=\"u-select__header\" @touchmove.stop.prevent=\"\">\r\n\t\t\t\t\t<view\r\n\t\t\t\t\t\tclass=\"u-select__header__cancel u-select__header__btn\"\r\n\t\t\t\t\t\t:style=\"{ color: cancelColor }\"\r\n\t\t\t\t\t\thover-class=\"u-hover-class\"\r\n\t\t\t\t\t\t:hover-stay-time=\"150\"\r\n\t\t\t\t\t\t@tap=\"getResult('cancel')\"\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t{{cancelText}}\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"u-select__header__title\">\r\n\t\t\t\t\t\t{{title}}\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view\r\n\t\t\t\t\t\tclass=\"u-select__header__confirm u-select__header__btn\"\r\n\t\t\t\t\t\t:style=\"{ color: moving ? cancelColor : confirmColor }\"\r\n\t\t\t\t\t\thover-class=\"u-hover-class\"\r\n\t\t\t\t\t\t:hover-stay-time=\"150\"\r\n\t\t\t\t\t\****************=\"\"\r\n\t\t\t\t\t\**********=\"getResult('confirm')\"\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t{{confirmText}}\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"u-select__body\">\r\n\t\t\t\t\t<picker-view @change=\"columnChange\" class=\"u-select__body__picker-view\" :value=\"defaultSelector\" @pickstart=\"pickstart\" @pickend=\"pickend\" v-if=\"value\">\r\n\t\t\t\t\t\t<picker-view-column v-for=\"(item, index) in columnData\" :key=\"index\">\r\n\t\t\t\t\t\t\t<view class=\"u-select__body__picker-view__item\" v-for=\"(item1, index1) in item\" :key=\"index1\">\r\n\t\t\t\t\t\t\t\t<view class=\"u-line-1\">{{ item1[labelName] }}</view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</picker-view-column>\r\n\t\t\t\t\t</picker-view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</u-popup>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\t/**\r\n\t * select 列选择器\r\n\t * @description 此选择器用于单列，多列，多列联动的选择场景。(从1.3.0版本起，不建议使用Picker组件的单列和多列模式，Select组件是专门为列选择而构造的组件，更简单易用。)\r\n\t * @tutorial http://uviewui.com/components/select.html\r\n\t * @property {String} mode 模式选择，\"single-column\"-单列模式，\"mutil-column\"-多列模式，\"mutil-column-auto\"-多列联动模式\r\n\t * @property {Array} list 列数据，数组形式，见官网说明\r\n\t * @property {Boolean} v-model 布尔值变量，用于控制选择器的弹出与收起\r\n\t * @property {Boolean} safe-area-inset-bottom 是否开启底部安全区适配(默认false)\r\n\t * @property {String} cancel-color 取消按钮的颜色（默认#606266）\r\n\t * @property {String} confirm-color 确认按钮的颜色(默认#2979ff)\r\n\t * @property {String} confirm-text 确认按钮的文字\r\n\t * @property {String} cancel-text 取消按钮的文字\r\n\t * @property {String} default-value 提供的默认选中的下标，见官网说明\r\n\t * @property {Boolean} mask-close-able 是否允许通过点击遮罩关闭Picker(默认true)\r\n\t * @property {String Number} z-index 弹出时的z-index值(默认10075)\r\n\t * @property {String} value-name 自定义list数据的value属性名 1.3.6\r\n\t * @property {String} label-name 自定义list数据的label属性名 1.3.6\r\n\t * @property {String} child-name 自定义list数据的children属性名，只对多列联动模式有效 1.3.7\r\n\t * @event {Function} confirm 点击确定按钮，返回当前选择的值\r\n\t * @example <u-select v-model=\"show\" :list=\"list\"></u-select>\r\n\t */\r\n\r\nexport default {\r\n\tprops: {\r\n\t\t// 列数据\r\n\t\tlist: {\r\n\t\t\ttype: Array,\r\n\t\t\tdefault() {\r\n\t\t\t\treturn [];\r\n\t\t\t}\r\n\t\t},\r\n\t\t// 是否显示边框\r\n\t\tborder: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: true\r\n\t\t},\r\n\t\t// 通过双向绑定控制组件的弹出与收起\r\n\t\tvalue: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: false\r\n\t\t},\r\n\t\t// \"取消\"按钮的颜色\r\n\t\tcancelColor: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: '#606266'\r\n\t\t},\r\n\t\t// \"确定\"按钮的颜色\r\n\t\tconfirmColor: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: '#2979ff'\r\n\t\t},\r\n\t\t// 弹出的z-index值\r\n\t\tzIndex: {\r\n\t\t\ttype: [String, Number],\r\n\t\t\tdefault: 0\r\n\t\t},\r\n\t\tsafeAreaInsetBottom: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: false\r\n\t\t},\r\n\t\t// 是否允许通过点击遮罩关闭Picker\r\n\t\tmaskCloseAble: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: true\r\n\t\t},\r\n\t\t// 提供的默认选中的下标\r\n\t\tdefaultValue: {\r\n\t\t\ttype: Array,\r\n\t\t\tdefault() {\r\n\t\t\t\treturn [0];\r\n\t\t\t}\r\n\t\t},\r\n\t\t// 模式选择，single-column-单列，mutil-column-多列，mutil-column-auto-多列联动\r\n\t\tmode: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: 'single-column'\r\n\t\t},\r\n\t\t// 自定义value属性名\r\n\t\tvalueName: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: 'value'\r\n\t\t},\r\n\t\t// 自定义label属性名\r\n\t\tlabelName: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: 'label'\r\n\t\t},\r\n\t\t// 自定义多列联动模式的children属性名\r\n\t\tchildName: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: 'children'\r\n\t\t},\r\n\t\t// 顶部标题\r\n\t\ttitle: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: ''\r\n\t\t},\r\n\t\t// 取消按钮的文字\r\n\t\tcancelText: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: '取消'\r\n\t\t},\r\n\t\t// 确认按钮的文字\r\n\t\tconfirmText: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: '确认'\r\n\t\t}\r\n\t},\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\t// 用于列改变时，保存当前的索引，下一次变化时比较得出是哪一列发生了变化\r\n\t\t\tdefaultSelector: [0],\r\n\t\t\t// picker-view的数据\r\n\t\t\tcolumnData: [],\r\n\t\t\t// 每次队列发生变化时，保存选择的结果\r\n\t\t\tselectValue: [],\r\n\t\t\t// 上一次列变化时的index\r\n\t\t\tlastSelectIndex: [],\r\n\t\t\t// 列数\r\n\t\t\tcolumnNum: 0,\r\n\t\t\t// 列是否还在滑动中，微信小程序如果在滑动中就点确定，结果可能不准确\r\n\t\t\tmoving: false\r\n\t\t};\r\n\t},\r\n\twatch: {\r\n\t\t// 在select弹起的时候，重新初始化所有数据\r\n\t\tvalue: {\r\n\t\t\timmediate: true,\r\n\t\t\thandler(val) {\r\n\t\t\t\tif(val) setTimeout(() => this.init(), 10);\r\n\t\t\t}\r\n\t\t},\r\n\t},\r\n\tcomputed: {\r\n\t\tuZIndex() {\r\n\t\t\t// 如果用户有传递z-index值，优先使用\r\n\t\t\treturn this.zIndex ? this.zIndex : this.$u.zIndex.popup;\r\n\t\t},\r\n\t},\r\n\tmethods: {\r\n\t\t// 标识滑动开始，只有微信小程序才有这样的事件\r\n\t\tpickstart() {\r\n\t\t\t// #ifdef MP-WEIXIN\r\n\t\t\tthis.moving = true;\r\n\t\t\t// #endif\r\n\t\t},\r\n\t\t// 标识滑动结束\r\n\t\tpickend() {\r\n\t\t\t// #ifdef MP-WEIXIN\r\n\t\t\tthis.moving = false;\r\n\t\t\t// #endif\r\n\t\t},\r\n\t\tinit() {\r\n\t\t\tthis.setColumnNum();\r\n\t\t\tthis.setDefaultSelector();\r\n\t\t\tthis.setColumnData();\r\n\t\t\tthis.setSelectValue();\r\n\t\t},\r\n\t\t// 获取默认选中列下标\r\n\t\tsetDefaultSelector() {\r\n\t\t\t// 如果没有传入默认选中的值，生成长度为columnNum，用0填充的数组\r\n\t\t\tthis.defaultSelector = this.defaultValue.length == this.columnNum ? this.defaultValue : Array(this.columnNum).fill(0);\r\n\t\t\tthis.lastSelectIndex = this.$u.deepClone(this.defaultSelector);\r\n\t\t},\r\n\t\t// 计算列数\r\n\t\tsetColumnNum() {\r\n\t\t\t// 单列的列数为1\r\n\t\t\tif(this.mode == 'single-column') this.columnNum = 1;\r\n\t\t\t// 多列时，this.list数组长度就是列数\r\n\t\t\telse if(this.mode == 'mutil-column') this.columnNum = this.list.length;\r\n\t\t\t// 多列联动时，通过历遍this.list的第一个元素，得出有多少列\r\n\t\t\telse if(this.mode == 'mutil-column-auto') {\r\n\t\t\t\tlet num = 1;\r\n\t\t\t\tlet column = this.list;\r\n\t\t\t\t// 只要有元素并且第一个元素有children属性，继续历遍\r\n\t\t\t\twhile(column[0][this.childName]) {\r\n\t\t\t\t\tcolumn = column[0] ? column[0][this.childName] : {};\r\n\t\t\t\t\tnum ++;\r\n\t\t\t\t}\r\n\t\t\t\tthis.columnNum = num;\r\n\t\t\t}\r\n\t\t},\r\n\t\t// 获取需要展示在picker中的列数据\r\n\t\tsetColumnData() {\r\n\t\t\tlet data = [];\r\n\t\t\tthis.selectValue = [];\r\n\t\t\tif(this.mode == 'mutil-column-auto') {\r\n\t\t\t\t// 获得所有数据中的第一个元素\r\n\t\t\t\tlet column = this.list[this.defaultSelector.length ? this.defaultSelector[0] : 0];\r\n\t\t\t\t// 通过循环所有的列数，再根据设定列的数组，得出当前需要渲染的整个列数组\r\n\t\t\t\tfor (let i = 0; i < this.columnNum; i++) {\r\n\t\t\t\t\t// 第一列默认为整个list数组\r\n\t\t\t\t\tif (i == 0) {\r\n\t\t\t\t\t\tdata[i] = this.list;\r\n\t\t\t\t\t\tcolumn = column[this.childName];\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\t// 大于第一列时，判断是否有默认选中的，如果没有就用该列的第一项\r\n\t\t\t\t\t\tdata[i] = column;\r\n\t\t\t\t\t\tcolumn = column[this.defaultSelector[i]][this.childName];\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t} else if(this.mode == 'single-column') {\r\n\t\t\t\tdata[0] = this.list;\r\n\t\t\t} else {\r\n\t\t\t\tdata = this.list;\r\n\t\t\t}\r\n\t\t\tthis.columnData = data;\r\n\t\t},\r\n\t\t// 获取默认选中的值，如果没有设置defaultValue，就默认选中每列的第一个\r\n\t\tsetSelectValue() {\r\n\t\t\tlet tmp = null;\r\n\t\t\tfor(let i = 0; i < this.columnNum; i++) {\r\n\t\t\t\ttmp = this.columnData[i][this.defaultSelector[i]];\r\n\t\t\t\tlet data = {\r\n\t\t\t\t\tvalue: tmp ? tmp[this.valueName] : null,\r\n\t\t\t\t\tlabel: tmp ? tmp[this.labelName] : null\r\n\t\t\t\t};\r\n\t\t\t\t// 判断是否存在额外的参数，如果存在，就返回\r\n\t\t\t\tif(tmp && tmp.extra !== undefined) data.extra = tmp.extra;\r\n\t\t\t\tthis.selectValue.push(data)\r\n\t\t\t}\r\n\t\t},\r\n\t\t// 列选项\r\n\t\tcolumnChange(e) {\r\n\t\t\tlet index = null;\r\n\t\t\tlet columnIndex = e.detail.value;\r\n\t\t\t// 由于后面是需要push进数组的，所以需要先清空数组\r\n\t\t\tthis.selectValue = [];\r\n\t\t\tthis.defaultSelector = columnIndex;\r\n\t\t\tif(this.mode == 'mutil-column-auto') {\r\n\t\t\t\t// 对比前后两个数组，寻找变更的是哪一列，如果某一个元素不同，即可判定该列发生了变化\r\n\t\t\t\tthis.lastSelectIndex.map((val, idx) => {\r\n\t\t\t\t\tif (val != columnIndex[idx]) index = idx;\r\n\t\t\t\t});\r\n\t\t\t\t\r\n\t\t\t\tfor (let i = index + 1; i < this.columnNum; i++) {\r\n\t\t\t\t\t// 当前变化列的下一列的数据，需要获取上一列的数据，同时需要指定是上一列的第几个的children，再往后的\r\n\t\t\t\t\t// 默认是队列的第一个为默认选项\r\n\t\t\t\t\tthis.columnData[i] = this.columnData[i - 1][i - 1 == index ? columnIndex[index] : 0][this.childName];\r\n\t\t\t\t\t// 改变的列之后的所有列，默认选中第一个\r\n\t\t\t\t\tthis.defaultSelector[i] = 0;\r\n\t\t\t\t}\r\n\t\t\t\t// 在历遍的过程中，可能由于上一步修改this.columnData，导致产生连锁反应，程序触发columnChange，会有多次调用\r\n\t\t\t\t// 只有在最后一次数据稳定后的结果是正确的，此前的历遍中，可能会产生undefined，故需要判断\r\n\t\t\t\tcolumnIndex.map((item, index) => {\r\n\t\t\t\t\tlet data = this.columnData[index][columnIndex[index]];\r\n\t\t\t\t\tlet tmp = {\r\n\t\t\t\t\t\tvalue: data ? data[this.valueName] : null,\r\n\t\t\t\t\t\tlabel: data ? data[this.labelName] : null,\r\n\t\t\t\t\t};\r\n\t\t\t\t\t// 判断是否有需要额外携带的参数\r\n\t\t\t\t\tif(data && data.extra !== undefined) tmp.extra = data.extra;\r\n\t\t\t\t\tthis.selectValue.push(tmp);\r\n\r\n\t\t\t\t})\r\n\t\t\t\t// 保存这一次的结果，用于下次列发生变化时作比较\r\n\t\t\t\tthis.lastSelectIndex = columnIndex;\r\n\t\t\t} else if(this.mode == 'single-column') {\r\n\t\t\t\tlet data = this.columnData[0][columnIndex[0]];\r\n\t\t\t\t// 初始默认选中值\r\n\t\t\t\tlet tmp = {\r\n\t\t\t\t\tvalue: data ? data[this.valueName] : null,\r\n\t\t\t\t\tlabel: data ? data[this.labelName] : null,\r\n\t\t\t\t};\r\n\t\t\t\t// 判断是否有需要额外携带的参数\r\n\t\t\t\tif(data && data.extra !== undefined) tmp.extra = data.extra;\r\n\t\t\t\tthis.selectValue.push(tmp);\r\n\t\t\t} else if(this.mode == 'mutil-column') {\r\n\t\t\t\t// 初始默认选中值\r\n\t\t\t\tcolumnIndex.map((item, index) => {\r\n\t\t\t\t\tlet data = this.columnData[index][columnIndex[index]];\r\n\t\t\t\t\t// 初始默认选中值\r\n\t\t\t\t\tlet tmp = {\r\n\t\t\t\t\t\tvalue: data ? data[this.valueName] : null,\r\n\t\t\t\t\t\tlabel: data ? data[this.labelName] : null,\r\n\t\t\t\t\t};\r\n\t\t\t\t\t// 判断是否有需要额外携带的参数\r\n\t\t\t\t\tif(data && data.extra !== undefined) tmp.extra = data.extra;\r\n\t\t\t\t\tthis.selectValue.push(tmp);\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t},\r\n\t\tclose() {\r\n\t\t\tthis.$emit('input', false);\r\n\t\t\t// 重置default-value默认值\r\n\t\t\tthis.$set(this, 'defaultSelector', [0]);\r\n\t\t},\r\n\t\t// 点击确定或者取消\r\n\t\tgetResult(event = null) {\r\n\t\t\t// #ifdef MP-WEIXIN\r\n\t\t\tif (this.moving) return;\r\n\t\t\t// #endif\r\n\t\t\tif (event) this.$emit(event, this.selectValue);\r\n\t\t\tthis.close();\r\n\t\t},\r\n\t\tselectHandler() {\r\n\t\t\tthis.$emit('click');\r\n\t\t}\r\n\t}\r\n};\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n@import \"../../libs/css/style.components.scss\";\r\n\r\n.u-select {\r\n\r\n\t&__action {\r\n\t\tposition: relative;\r\n\t\tline-height: $u-form-item-height;\r\n\t\theight: $u-form-item-height;\r\n\r\n\t\t&__icon {\r\n\t\t\tposition: absolute;\r\n\t\t\tright: 20rpx;\r\n\t\t\ttop: 50%;\r\n\t\t\ttransition: transform .4s;\r\n\t\t\ttransform: translateY(-50%);\r\n\t\t\tz-index: 1;\r\n\r\n\t\t\t&--reverse {\r\n\t\t\t\ttransform: rotate(-180deg) translateY(50%);\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t&__hader {\r\n\t\t&__title {\r\n\t\t\tcolor: $u-content-color;\r\n\t\t}\r\n\t}\r\n\r\n\t&--border {\r\n\t\tborder-radius: 6rpx;\r\n\t\tborder-radius: 4px;\r\n\t\tborder: 1px solid $u-form-item-border-color;\r\n\t}\r\n\r\n\t&__header {\r\n\t\t@include vue-flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: space-between;\r\n\t\theight: 80rpx;\r\n\t\tpadding: 0 40rpx;\r\n\t}\r\n\r\n\t&__body {\r\n\t\twidth: 100%;\r\n\t\theight: 500rpx;\r\n\t\toverflow: hidden;\r\n\t\tbackground-color: #fff;\r\n\r\n\t\t&__picker-view {\r\n\t\t\theight: 100%;\r\n\t\t\tbox-sizing: border-box;\r\n\r\n\t\t\t&__item {\r\n\t\t\t\t@include vue-flex;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\tjustify-content: center;\r\n\t\t\t\tfont-size: 32rpx;\r\n\t\t\t\tcolor: $u-main-color;\r\n\t\t\t\tpadding: 0 8rpx;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n</style>\r\n", "import mod from \"-!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-select.vue?vue&type=style&index=0&id=7b211ee7&scoped=true&lang=scss&\"; export default mod; export * from \"-!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-select.vue?vue&type=style&index=0&id=7b211ee7&scoped=true&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1752590364571\n      var cssReload = require(\"D:/HBuilderX.3.3.13.20220314.full/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"../../\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}