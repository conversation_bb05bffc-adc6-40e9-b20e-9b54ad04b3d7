{"version": 3, "sources": ["uni-app:///main.js", "webpack:///E:/pos/黄金/gold_client/pages/index/index.vue?af5d", "webpack:///E:/pos/黄金/gold_client/pages/index/index.vue?3781", "webpack:///E:/pos/黄金/gold_client/pages/index/index.vue?d53a", "webpack:///E:/pos/黄金/gold_client/pages/index/index.vue?edf9", "uni-app:///pages/index/index.vue", "webpack:///E:/pos/黄金/gold_client/pages/index/index.vue?8c8d", "webpack:///E:/pos/黄金/gold_client/pages/index/index.vue?a2b1", "webpack:///E:/pos/黄金/gold_client/pages/index/index.vue?d0f4", "webpack:///E:/pos/黄金/gold_client/pages/index/index.vue?cf02"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "number", "bannerlist", "loadStatus", "loadText", "loadmore", "loading", "nomore", "goodsList", "storeInfo", "show", "tzGold", "gy<PERSON><PERSON>", "timeGold", "onLoad", "onShow", "that", "uni", "key", "success", "methods", "goShop", "url", "location", "onSearch", "onGoldPrice", "getIndexinfo", "util", "res", "title", "icon", "getCurrentDateTime"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACa;AACyB;;;AAG1F;AACgL;AAChL,gBAAgB,yLAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACxBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,+NAEN;AACP,KAAK;AACL;AACA,aAAa,2OAEN;AACP,KAAK;AACL;AACA,aAAa,iPAEN;AACP,KAAK;AACL;AACA,aAAa,6PAEN;AACP,KAAK;AACL;AACA,aAAa,uPAEN;AACP,KAAK;AACL;AACA,aAAa,qOAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACxEA;AAAA;AAAA;AAAA;AAAspB,CAAgB,2qBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;AC0G1qB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACA;AACA;AAAA,eACA;EACAC;IACA;MACAC;MAAA;MACAC;MAAA;MACAC;MACAC;QACAC;QACAC;QACAC;MACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACA;EAAA,CACA;EACAC;IAAA;IAAA;MAAA;MAAA;QAAA;UAAA;YAAA;cACAC;cAAA;cAAA,OACA;YAAA;cACAC;gBACAC;gBACAC;kBACAH;gBACA;cACA;cACA;cACA;YAAA;YAAA;cAAA;UAAA;QAAA;MAAA;IAAA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACAI;IACAC;MACAJ;QACAK;MACA;IACA;IACAC;MACAN;QACAK;MACA;IACA;IACAE;MACAP;QACAK;MACA;IACA;IACAG;MACA;IACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,OACAC;cAAA;gBAAAC;gBACA;gBACA;kBACAX;oBACAY;oBACAC;kBACA;gBACA;kBACA;kBACA;kBACA;kBACA;kBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAC;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC5MA;AAAA;AAAA;AAAA;AAA87B,CAAgB,w7BAAG,EAAC,C;;;;;;;;;;;ACAl9B;AACA,OAAO,KAAU,EAAE,kBAKd;;;;;;;;;;;;;ACNL;AAAA;AAAA;AAAA;AAAqwC,CAAgB,kuCAAG,EAAC,C;;;;;;;;;;;ACAzxC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/index/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/index/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=57280228&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&lang=css&\"\nimport style1 from \"./index.vue?vue&type=style&index=1&id=57280228&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"57280228\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/index/index.vue\"\nexport default component.exports", "export * from \"-!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=template&id=57280228&scoped=true&\"", "var components\ntry {\n  components = {\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-icon/u-icon\" */ \"uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n    uSwiper: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-swiper/u-swiper\" */ \"uview-ui/components/u-swiper/u-swiper.vue\"\n      )\n    },\n    uDivider: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-divider/u-divider\" */ \"uview-ui/components/u-divider/u-divider.vue\"\n      )\n    },\n    uWaterfall: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-waterfall/u-waterfall\" */ \"uview-ui/components/u-waterfall/u-waterfall.vue\"\n      )\n    },\n    uLoadmore: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-loadmore/u-loadmore\" */ \"uview-ui/components/u-loadmore/u-loadmore.vue\"\n      )\n    },\n    uPopup: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-popup/u-popup\" */ \"uview-ui/components/u-popup/u-popup.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.goodsList.length\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event) {\n      _vm.show = false\n    }\n  }\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"page\">\r\n\t\t<view class=\"status_bar\">\r\n\t\t\t<view class=\"location_info\" @click=\"location\">\r\n\t\t\t\t<view>{{storeInfo.name||'祥杰金条专家'}}</view>\r\n\t\t\t\t<view style=\"display: flex;\"><image style=\"width: 32rpx;height: 32rpx;\" src=\"/static/img/index/index_location.png\" alt=\"\" /></view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"search_icon\" @click=\"onSearch\"><u-icon size=\"32\" name=\"/static/img/index/search-normal.png\"></u-icon></view>\r\n\t\t</view>\r\n\t\t<!-- 轮播图 -->\r\n\t\t<view class=\"index-concent-item\">\r\n\t\t\t<u-swiper borderRadius=\"0\" :height=\"868\" name=\"imgUrl\" :list=\"bannerlist\" mode=\"none\"></u-swiper>\r\n\t\t\t<!-- 金价 -->\r\n<!-- \t\t\t<view :class=\"scrollTop<86?'location_info search_info active':'location_info search_info'\">\r\n\t\t\t\t<view style=\"display: flex;justify-content: center;\">\r\n\t\t\t\t\t<image style=\"width: 36rpx;\" mode=\"widthFix\" src=\"/static/img/index/index-gold-price.png\" alt=\"\" srcset=\"\" />\r\n\t\t\t\t</view>\r\n\t\t\t\t<view>金价</view>\r\n\t\t\t\t<u-line length=\"30\" direction=\"col\" margin=\"0 6rpx\" color=\"white\" />\r\n\t\t\t\t<view style=\"display: flex;\"><image style=\"width: 32rpx;height: 32rpx;\" src=\"/static/img/index/search-normal.png\" alt=\"\" /></view>\r\n\t\t\t</view> -->\r\n\t\t</view>\r\n\t\t<view style=\"padding: 30rpx 0 6rpx 0;\">\r\n\t\t\t<u-divider half-width=\"260\" border-color=\"#BBA186\" bg-color=\"#F5F5F5\">\r\n\t\t\t\t<image style=\"width: 128rpx;height: 34rpx;\" mode=\"widthFix\" src=\"/static/img/index/index_tuijian.png\" alt=\"\" srcset=\"\" />\r\n\t\t\t</u-divider>\r\n\t\t</view>\r\n\t\t<view class=\"shop_content\">\r\n\t\t\t<!-- Waterfall 瀑布流 -->\r\n\t\t\t<u-waterfall v-model=\"goodsList\">\r\n\t\t\t\t<template v-slot:left=\"{leftList}\">\r\n\t\t\t\t\t<view class=\"shop_list\" v-for=\"(item, index) in leftList\" :key=\"index\" @click=\"goShop(item)\">\r\n\t\t\t\t\t\t<!-- 这里编写您的内容，item为您传递给v-model的数组元素 -->\r\n\t\t\t\t\t\t<!-- <u-lazy-load :height=\"340\" threshold=\"100\" :image=\"item.goodsImg\" :index=\"index\"></u-lazy-load> -->\r\n\t\t\t\t\t\t<image style=\"width: 100%;height: 340rpx;\" :src=\"item.goodsImg\" alt=\"\" />\r\n\t\t\t\t\t\t<view class=\"demo-title text-ellipsis_2\">\r\n\t\t\t\t\t\t\t{{item.goodsName}}\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"shop-price\">\r\n\t\t\t\t\t\t\t<view class=\"shop-price-num\">\r\n\t\t\t\t\t\t\t\t<view><text>¥</text><text style=\"font-size: 32rpx;\">{{item.price}}</text><text style=\"font-weight: 400;color: #9FA3B0;margin-left: 10rpx;\">{{item.shop}}</text></view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"shop_add\">\r\n\t\t\t\t\t\t\t\t<image style=\"width: 32rpx;height: 32rpx;\" src=\"/static/img/index/index-add-circle.png\" alt=\"\" srcset=\"\" />\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</template>\r\n\t\t\t\t<template v-slot:right=\"{rightList}\">\r\n\t\t\t\t\t<view class=\"shop_list\" v-for=\"(item, index) in rightList\" :key=\"index\" @click=\"goShop(item)\">\r\n\t\t\t\t\t\t<!-- 这里编写您的内容，item为您传递给v-model的数组元素 -->\r\n\t\t\t\t\t\t<!-- <u-lazy-load threshold=\"100\" :image=\"item.goodsImg\" :index=\"index\"></u-lazy-load> -->\r\n\t\t\t\t\t\t<image style=\"width: 100%;height: 340rpx;\" :src=\"item.goodsImg\" alt=\"\" />\r\n\t\t\t\t\t\t<view class=\"demo-title text-ellipsis_2\">\r\n\t\t\t\t\t\t\t{{item.goodsName}}\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"shop-price\">\r\n\t\t\t\t\t\t\t<view class=\"shop-price-num\">\r\n\t\t\t\t\t\t\t\t<view><text>¥</text><text style=\"font-size: 32rpx;\">{{item.price}}</text><text style=\"font-weight: 400;color: #9FA3B0;margin-left: 10rpx;\">{{item.shop}}</text></view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"shop_add\">\r\n\t\t\t\t\t\t\t\t<image style=\"width: 32rpx;height: 32rpx;\" src=\"/static/img/index/index-add-circle.png\" alt=\"\" srcset=\"\" />\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</template>\r\n\t\t\t</u-waterfall>\r\n\t\t</view>\r\n\t\t<!-- #ifdef MP-WEIXIN -->\r\n\t\t<u-loadmore :margin-top=\"goodsList.length?'':50\" :status=\"loadStatus\" :load-text=\"loadText\"></u-loadmore>\r\n\t\t<!-- #endif -->\r\n\t\t<!-- #ifdef WEB -->\r\n\t\t<u-loadmore :status=\"loadStatus\" :load-text=\"loadText\" @loadmore=\"addRandomData\"></u-loadmore>\r\n\t\t<view class=\"page_bottom\"></view>\r\n\t\t<!-- #endif -->\r\n\t\t<view class=\"gold-price\" @click=\"onGoldPrice\">\r\n\t\t\t<view style=\"display: flex;justify-content: center;\">\r\n\t\t\t\t<image style=\"width: 36rpx;height: 36rpx;\" src=\"/static/img/index/index-gold-price.png\" alt=\"\" srcset=\"\" />\r\n\t\t\t</view>\r\n\t\t\t<view style=\"font-size: 24rpx;text-align: center;margin-top: 4rpx;\">金价</view>\r\n\t\t</view>\r\n\t\t<u-popup v-model=\"show\" mode=\"center\" :border-radius=\"14\" length=\"90%\" :close-icon-color=\"'#FFFFFF'\" :closeable=\"true\">\r\n\t\t\t<view>\r\n\t\t\t\t<view class=\"gold_new\">\r\n\t\t\t\t\t<view class=\"gold_new_title\">今日金价</view>\r\n\t\t\t\t\t<view>更新于 {{timeGold}}</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view style=\"padding: 0 32rpx;\">\r\n\t\t\t\t\t<view class=\"gold_price_show\">\r\n\t\t\t\t\t\t<view style=\"font-size: 28rpx;\">{{gyGold.remarks}}</view>\r\n\t\t\t\t\t\t<view><text style=\"font-size: 32rpx;color: #BBA186;\">{{gyGold.configValue}}</text><text style=\"font-size: 24rpx;margin-left: 4rpx;\">元/克</text></view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"gold_price_show\">\r\n\t\t\t\t\t\t<view style=\"font-size: 28rpx;\">{{tzGold.remarks}}</view>\r\n\t\t\t\t\t\t<view><text style=\"font-size: 32rpx;color: #BBA186;\">{{tzGold.configValue}}</text><text style=\"font-size: 24rpx;margin-left: 4rpx;\">元/克</text></view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"goods_detail_footer\">\r\n\t\t\t\t\t<view @click=\"show=false\">我知道了</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</u-popup>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport number from '../../utils/number.js'\r\n\tconst api = require('../../config/api')\r\n\tconst util = require('../../utils/util')\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tnumber: number, //声明number属性并赋值为引入的number模块\r\n\t\t\t\tbannerlist: [], //轮播图\r\n\t\t\t\tloadStatus: 'loading',\r\n\t\t\t\tloadText: {\r\n\t\t\t\t\tloadmore: '加载更多',\r\n\t\t\t\t\tloading: '努力加载中',\r\n\t\t\t\t\tnomore: '已经到底了'\r\n\t\t\t\t},\r\n\t\t\t\tgoodsList: [],\r\n\t\t\t\tstoreInfo: {},\r\n\t\t\t\tshow: false,\r\n\t\t\t\ttzGold: {},\r\n\t\t\t\tgyGold: {},\r\n\t\t\t\ttimeGold: ''\r\n\t\t\t}\r\n\t\t},\r\n\t\tonLoad() {\r\n\t\t\t// this.upadetaApp();\r\n\t\t\t// this.addRandomData();\r\n\t\t},\r\n\t\tasync onShow() {\r\n\t\t\tlet that = this\r\n\t\t\tawait this.getIndexinfo()\r\n\t\t\tuni.getStorage({\r\n\t\t\t\tkey: 'store_info',\r\n\t\t\t\tsuccess: function (res) {\r\n\t\t\t\t\tthat.storeInfo = res.data\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t\t// this.getNoticeList()\r\n\t\t\t// this.getInfoAmt()\r\n\t\t},\r\n\t\t// onReachBottom() {\r\n\t\t// \tthis.addRandomData();\r\n\t\t// },\r\n\t\t// onPageScroll(e) {\r\n\t\t// \tlet that = this;\r\n\t\t// \tclearTimeout(this.scrollTimer);\r\n\t\t// \tthis.scrollTimer = setTimeout(function() {\r\n\t\t// \t  // 执行滚动相关逻辑（如调整界面状态）\r\n\t\t// \t  that.scrollTop = e.scrollTop\r\n\t\t// \t  // console.log('防抖触发', e.scrollTop);\r\n\t\t// \t}, 100); // 设置防抖时长为1000毫秒\r\n\t\t// },\r\n\t\tmethods: {\r\n\t\t\tgoShop(item) {\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: '/pages/indexChild/GoodsDetails/GoodsDetails?goodsId='+item.id\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tlocation(){\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: \"/pages/promotion/store\"\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tonSearch() {\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: \"/pages/search/search\"\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tonGoldPrice(){\r\n\t\t\t\tthis.show = true\r\n\t\t\t},\r\n\t\t\tasync getIndexinfo() {\r\n\t\t\t\tconst res = await util.request(api.indexInfoUrl, {}, 'POST')\r\n\t\t\t\t// console.log(res)\r\n\t\t\t\tif (res.code !== 0) {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: res.msg,\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t})\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.bannerlist = res.data.imgList\r\n\t\t\t\t\tthis.goodsList = res.data.goodsList\r\n\t\t\t\t\tthis.gyGold = res.data.gyGold\r\n\t\t\t\t\tthis.tzGold = res.data.tzGold\r\n\t\t\t\t\tthis.timeGold = this.getCurrentDateTime() // 今日金价\r\n\t\t\t\t\tthis.loadStatus = 'nomore'\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tgetCurrentDateTime() {\r\n\t\t\t\tconst now = new Date();\r\n\t\t\t\tconst year = now.getFullYear();\r\n\t\t\t\tconst month = String(now.getMonth() + 1).padStart(2, '0'); // 月份从0开始，需要加1\r\n\t\t\t\tconst day = String(now.getDate()).padStart(2, '0');\r\n\t\t\t\tconst hours = String(now.getHours()).padStart(2, '0');\r\n\t\t\t\tconst minutes = String(now.getMinutes()).padStart(2, '0');\r\n\t\t\t\tconst seconds = String(now.getSeconds()).padStart(2, '0');\r\n\t\t\t\tthis.dateTime = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;\r\n\t\t\t\treturn `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n<style>\r\n\tpage{\r\n\t\tbackground-color: #F9F5F2;\r\n\t}\r\n</style>\r\n<style lang=\"scss\" scoped>\r\n.page {\r\n\tpadding-bottom: 20rpx;\r\n}\r\n/* #ifdef MP-WEIXIN */\r\n.status_bar {\r\n\tposition: absolute;\r\n\tz-index: 5;\r\n\tleft: 0;\r\n\ttop: 0;\r\n\tright: 0;\r\n\tmargin-top: 98rpx;\r\n\tpadding: 0 24rpx 10rpx 24rpx;\r\n\tfont-size: 28rpx;\r\n\tcolor: #171B25;\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tjustify-content: space-between;\r\n\t.location_info{\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\theight: 32px;\r\n\t\tpadding: 5px 12px;\r\n\t\tgap: 4px;\r\n\t\tborder-radius: 999px;\r\n\t\tbackground: rgba(255,255,255,0.3);\r\n\t\tcolor: #fff;\r\n\t\tfont-size: 24rpx;\r\n\t\tbackdrop-filter: blur(7.5px);\r\n\t\topacity: 1;\r\n\t\ttransition: opacity 0.3s ease-in;\r\n\t}\r\n\t.search_icon{\r\n\t\twidth: 64rpx;\r\n\t\theight: 64rpx;\r\n\t\tborder-radius: 50%;\r\n\t\t// border: 1rpx solid #E5E6EB;\r\n\t\tdisplay: flex;\r\n\t\tjustify-items: center;\r\n\t\tjustify-content: center;\r\n\t\tmargin-right: 25vw;\r\n\t\tbackground: rgba(255,255,255,0.3);\r\n\t}\r\n}\r\n/* #endif */\r\n/* #ifdef WEB */\r\n.status_bar {\r\n\tposition: absolute;\r\n\tz-index: 5;\r\n\tleft: 0;\r\n\ttop: 0;\r\n\tright: 0;\r\n\t// margin-top: 98rpx;\r\n\tpadding: 24rpx 24rpx 10rpx 24rpx;\r\n\tfont-size: 28rpx;\r\n\tcolor: #171B25;\r\n\tdisplay: flex;\r\n\talign-items: center;\r\n\tjustify-content: space-between;\r\n\t.location_info{\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\theight: 32px;\r\n\t\tpadding: 5px 12px;\r\n\t\tgap: 4px;\r\n\t\tborder-radius: 999px;\r\n\t\tbackground: rgba(255,255,255,0.3);\r\n\t\tcolor: #fff;\r\n\t\tfont-size: 24rpx;\r\n\t\tbackdrop-filter: blur(7.5px);\r\n\t\topacity: 1;\r\n\t\ttransition: opacity 0.3s ease-in;\r\n\t}\r\n\t.search_icon{\r\n\t\twidth: 64rpx;\r\n\t\theight: 64rpx;\r\n\t\tborder-radius: 50%;\r\n\t\t// border: 1rpx solid #E5E6EB;\r\n\t\tdisplay: flex;\r\n\t\tjustify-items: center;\r\n\t\tjustify-content: center;\r\n\t\t// margin-right: 25vw;\r\n\t\tbackground: rgba(255,255,255,0.3);\r\n\t}\r\n}\r\n/* #endif */\r\n\r\n.index-concent-item{\r\n\t// margin-top: 10rpx;\r\n\t// position: relative;\r\n\r\n}\r\n.shop_content{\r\n\tpadding-left: 24rpx;\r\n\t.shop_list{\r\n\t\tborder-radius: 10rpx 10rpx 0 0;\r\n\t\toverflow: hidden;\r\n\t\tbackground-color: #fff;\r\n\t\tmargin: 24rpx 0;\r\n\t\tmargin-right: 24rpx;\r\n\t\t.demo-title {\r\n\t\t\tfont-size: 28rpx;\r\n\t\t\tmargin: 16rpx;\r\n\t\t\tcolor: #171B25;\r\n\t\t}\r\n\t\t.shop-price{\r\n\t\t\tdisplay: flex;\r\n\t\t\tjustify-content: space-between;\r\n\t\t\talign-items: center;\r\n\t\t\tpadding: 10rpx 24rpx 20rpx 24rpx;\r\n\t\t\t.shop-price-num{\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\tcolor: #FF0046;\r\n\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\tfont-weight: 600;\r\n\t\t\t}\r\n\t\t\t.shop_add{\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n.gold-price{\r\n\tposition: fixed;\r\n\ttop: 60vh;\r\n\tright: 24rpx;\r\n\twidth: 88rpx;\r\n\theight: 88rpx;\r\n\tpadding: 10rpx 0;\r\n\tflex-direction: column;\r\n\tjustify-content: center;\r\n\tflex-shrink: 0;\r\n\tborder-radius: 8px;\r\n\tbackground: #FFF;\r\n\tbox-shadow: 0 4px 10px 0 #0000001a;\r\n}\r\n\r\n\r\n.gold_new{\r\n\t\tpadding: 40rpx 0;\r\n\t\tbackground: linear-gradient(94deg, #8F8174 -2.53%, #C4B39F 131.45%);\r\n\t\tview{\r\n\t\t\tcolor: #FFFFFF;\r\n\t\t\ttext-align: center;\r\n\t\t\tfont-size: 24rpx;\r\n\t\t}\r\n\t.gold_new_title{\r\n\t\tfont-size: 36rpx;\r\n\t\t// padding-top: 40rpx;\r\n\t\tpadding-bottom: 20rpx;\r\n\t}\r\n}\r\n.gold_price_show{\r\n\tdisplay: flex;\r\n\tjustify-content: space-between;\r\n\tpadding: 24rpx 0;\r\n\tborder-bottom: 1rpx solid #F1F2F5;\r\n}\r\n\t.goods_detail_footer {\r\n\t\tmargin-top: 32rpx;\r\n\t\twidth: 100%;\r\n\t\theight: 50px;\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: center;\r\n\t}\r\n\r\n\t.goods_detail_footer>view {\r\n\t\theight: 84rpx;\r\n\t\twidth: 80%;\r\n\t\tborder-radius: 9999px;\r\n\t\tbackground-color: #BBA186;\r\n\t\tcolor: #FFFFFF;\r\n\t\tfont-size: 28rpx;\r\n\t\ttext-align: center;\r\n\t\tline-height: 84rpx;\r\n\t}\r\n</style>", "import mod from \"-!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754273097002\n      var cssReload = require(\"D:/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  ", "import mod from \"-!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=1&id=57280228&lang=scss&scoped=true&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=1&id=57280228&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754273099827\n      var cssReload = require(\"D:/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}