(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-order-myOrder"],{"03e4":function(t,e,r){var a=r("7263");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var o=r("4f06").default;o("0732193c",a,!0,{sourceMap:!1,shadowMode:!1})},"0e55":function(t,e,r){"use strict";r("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,r("a9e3");var a={name:"u-count-down",props:{timestamp:{type:[Number,String],default:0},autoplay:{type:Boolean,default:!0},separator:{type:String,default:"colon"},separatorSize:{type:[Number,String],default:30},separatorColor:{type:String,default:"#303133"},color:{type:String,default:"#303133"},fontSize:{type:[Number,String],default:30},bgColor:{type:String,default:"#fff"},height:{type:[Number,String],default:"auto"},showBorder:{type:Boolean,default:!1},borderColor:{type:String,default:"#303133"},showSeconds:{type:Boolean,default:!0},showMinutes:{type:Boolean,default:!0},showHours:{type:Boolean,default:!0},showDays:{type:Boolean,default:!0},hideZeroDay:{type:Boolean,default:!1}},watch:{timestamp:function(t,e){this.clearTimer(),this.start()}},data:function(){return{d:"00",h:"00",i:"00",s:"00",timer:null,seconds:0}},computed:{itemStyle:function(){var t={};return this.height&&(t.height=this.height+"rpx",t.width=this.height+"rpx"),this.showBorder&&(t.borderStyle="solid",t.borderColor=this.borderColor,t.borderWidth="1px"),this.bgColor&&(t.backgroundColor=this.bgColor),t},letterStyle:function(){var t={};return this.fontSize&&(t.fontSize=this.fontSize+"rpx"),this.color&&(t.color=this.color),t}},mounted:function(){this.autoplay&&this.timestamp&&this.start()},methods:{start:function(){var t=this;this.clearTimer(),this.timestamp<=0||(this.seconds=Number(this.timestamp),this.formatTime(this.seconds),this.timer=setInterval((function(){if(t.seconds--,t.$emit("change",t.seconds),t.seconds<0)return t.end();t.formatTime(t.seconds)}),1e3))},formatTime:function(t){t<=0&&this.end();var e,r=0,a=0,o=0;r=Math.floor(t/86400),e=Math.floor(t/3600)-24*r;var n=null;n=this.showDays?e:Math.floor(t/3600),a=Math.floor(t/60)-60*e-24*r*60,o=Math.floor(t)-24*r*60*60-60*e*60-60*a,n=n<10?"0"+n:n,a=a<10?"0"+a:a,o=o<10?"0"+o:o,r=r<10?"0"+r:r,this.d=r,this.h=n,this.i=a,this.s=o},end:function(){this.clearTimer(),this.$emit("end",{})},clearTimer:function(){this.timer&&(clearInterval(this.timer),this.timer=null)}},beforeDestroy:function(){clearInterval(this.timer),this.timer=null}};e.default=a},"14d96":function(t,e,r){"use strict";r.d(e,"b",(function(){return o})),r.d(e,"c",(function(){return n})),r.d(e,"a",(function(){return a}));var a={uCountDown:r("8084").default,uLoadmore:r("84c5").default},o=function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("v-uni-view",{staticClass:"order-list-page"},[r("v-uni-view",{staticClass:"order-list-header"},t._l(t.orderTypes,(function(e,a){return r("v-uni-view",{key:a,staticClass:"one-status",class:a==t.tabIndex?"active":"",on:{click:function(r){arguments[0]=r=t.$handleEvent(r),t.onClickItem(e,a)}}},[t._v(t._s(e.name))])})),1),r("v-uni-view",{staticClass:"order-list"},[t._l(t.orderList,(function(e,a){return r("v-uni-view",{key:a,staticClass:"each-order"},[r("v-uni-view",{staticClass:"header"},[r("v-uni-view",{staticClass:"order-no-status"},[r("v-uni-view",{staticClass:"no"},[r("v-uni-view",[t._v("订单编："+t._s(e.orderNo))]),r("v-uni-view",{staticStyle:{display:"flex","margin-left":"20rpx"},on:{click:function(r){arguments[0]=r=t.$handleEvent(r),t.onCopy(e.orderNo)}}},[r("v-uni-image",{staticStyle:{width:"22rpx",height:"22rpx"},attrs:{src:"/static/img/shop/shop-copy.png",alt:""}})],1)],1),r("v-uni-view",{staticClass:"status"},[t._v(t._s(t._f("formatState")(e.orderStatus)))])],1)],1),r("v-uni-view",{staticClass:"good-detail"},[r("v-uni-view",{staticClass:"product_info"},[r("v-uni-view",{staticClass:"prodct_left"},[r("v-uni-image",{staticStyle:{width:"164rpx",height:"164rpx"},attrs:{src:e.goodsImg}})],1),r("v-uni-view",{staticClass:"product_center"},[r("v-uni-view",{staticClass:"text-ellipsis_2",staticStyle:{"font-size":"28rpx","line-height":"44rpx"}},[t._v(t._s(e.goodsName))]),r("v-uni-view",{staticStyle:{display:"flex","margin-top":"20rpx"}},[r("v-uni-view",{staticStyle:{color:"#61687C","font-size":"24rpx","background-color":"#F2F4F7","text-align":"center",padding:"6rpx 20rpx","border-radius":"6rpx"}},[t._v(t._s(e.specification)+"g")]),r("v-uni-view")],1)],1),r("v-uni-view",{staticClass:"product_right"},[r("v-uni-view",[r("v-uni-text",{staticClass:"font_small"},[t._v("¥"+t._s(e.price))])],1),r("v-uni-view",{staticStyle:{"font-size":"24rpx",color:"#9FA3B0","margin-top":"8rpx","text-align":"right"}},[t._v("x1")])],1)],1)],1),r("v-uni-view",{staticClass:"opera-btns"},["0"==e.orderStatus?r("v-uni-view",{staticStyle:{display:"flex","justify-content":"space-between","align-items":"center"}},[r("v-uni-view",{staticStyle:{display:"flex","background-color":"rgba(255, 0, 70, 0.05)","align-items":"center",padding:"4rpx 8rpx","border-radius":"8rpx"}},[r("v-uni-view",{staticStyle:{"font-size":"24rpx",color:"#FF0046"}},[t._v("支付剩余：")]),r("u-count-down",{attrs:{timestamp:e.timestamp,"font-size":"24","bg-color":"none",color:"#FF0046",separator:"zh","separator-size":"24","separator-color":"#FF0046"}})],1),r("v-uni-view",{staticStyle:{display:"flex","justify-content":"space-between","align-items":"center"}},[r("v-uni-view",{staticClass:"each-btn cancel",on:{click:function(r){arguments[0]=r=t.$handleEvent(r),t.cancelOrderEvent(e)}}},[t._v("取消订单")]),r("v-uni-view",{staticClass:"each-btn pay",on:{click:function(r){arguments[0]=r=t.$handleEvent(r),t.enterOrderDetailPage(e)}}},[t._v("立即付款")])],1)],1):t._e(),"1"==e.orderStatus?r("v-uni-view",{staticStyle:{display:"flex","justify-content":"space-between","align-items":"center"}},[r("v-uni-view",{staticStyle:{color:"#9FA3B0","font-size":"24rpx"}},[t._v("下单时间："+t._s(e.createDate))]),r("v-uni-view",{staticStyle:{display:"flex","justify-content":"space-between","align-items":"center"}},[r("v-uni-view",{staticClass:"each-btn pay",on:{click:function(r){arguments[0]=r=t.$handleEvent(r),t.enterOrderDetailPage(e)}}},[t._v("去核销")])],1)],1):t._e(),["3","4","7"].includes(e.orderStatus)?r("v-uni-view",{staticStyle:{display:"flex","justify-content":"space-between","align-items":"center"}},[r("v-uni-view",{staticStyle:{color:"#9FA3B0","font-size":"24rpx"}}),r("v-uni-view",{staticStyle:{display:"flex","justify-content":"space-between","align-items":"center"}},[r("v-uni-view",{staticClass:"each-btn pay",on:{click:function(r){arguments[0]=r=t.$handleEvent(r),t.enterOrderDetailPage(e)}}},[t._v("查看订单")])],1)],1):t._e()],1)],1)})),r("u-loadmore",{attrs:{status:t.loadStatus,"load-text":t.loadText}}),r("v-uni-view",{staticClass:"page_bottom"})],2)],1)},n=[]},"1cbb":function(t,e,r){var a=r("24fb");e=a(!1),e.push([t.i,'@charset "UTF-8";\r\n/* uni.scss */\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 字体变量 */\r\n/* 颜色变量 */\r\n/* 行为相关颜色 */\r\n/* 文字基本颜色 */\r\n/* 背景颜色 */\r\n/* 边框颜色 */\r\n/* 尺寸变量 */\r\n/* 文字尺寸 */\r\n/* 图片尺寸 */\r\n/* Border Radius */\r\n/* 水平间距 */\r\n/* 垂直间距 */\r\n/* 透明度 */\r\n/* 文章场景相关 */uni-page-body[data-v-bac61a4e]{background-color:#f7f7f7}body.?%PAGE?%[data-v-bac61a4e]{background-color:#f7f7f7}.order-list-page[data-v-bac61a4e]{height:100%}.order-list-page .type-tabs .segmented-control__text[data-v-bac61a4e]{font-size:%?24?%}',""]),t.exports=e},"1ea1":function(t,e,r){"use strict";var a=r("e254"),o=r.n(a);o.a},"2b4a":function(t,e,r){"use strict";var a=r("03e4"),o=r.n(a);o.a},"2f8d":function(t,e,r){"use strict";r.r(e);var a=r("93d2"),o=r.n(a);for(var n in a)["default"].indexOf(n)<0&&function(t){r.d(e,t,(function(){return a[t]}))}(n);e["default"]=o.a},3578:function(t,e,r){var a=r("1cbb");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var o=r("4f06").default;o("d39d8ea2",a,!0,{sourceMap:!1,shadowMode:!1})},"3f71":function(t,e,r){var a=r("24fb");e=a(!1),e.push([t.i,'@charset "UTF-8";\r\n/* uni.scss */\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 字体变量 */\r\n/* 颜色变量 */\r\n/* 行为相关颜色 */\r\n/* 文字基本颜色 */\r\n/* 背景颜色 */\r\n/* 边框颜色 */\r\n/* 尺寸变量 */\r\n/* 文字尺寸 */\r\n/* 图片尺寸 */\r\n/* Border Radius */\r\n/* 水平间距 */\r\n/* 垂直间距 */\r\n/* 透明度 */\r\n/* 文章场景相关 */.order-list-page .order-list-header[data-v-bac61a4e]{padding:%?20?%;display:flex;align-items:center;justify-content:space-around;background-color:#fff}.order-list-page .order-list-header .one-status[data-v-bac61a4e]{padding:%?10?% 0;font-size:%?28?%;position:relative;color:#9fa3b0}.order-list-page .order-list-header .one-status.active[data-v-bac61a4e]{color:#171b25}.order-list-page .order-list-header .one-status.active[data-v-bac61a4e]:after{content:"";display:block;width:100%;height:2px;background-color:#bba186;position:absolute;bottom:0;left:0;transition:all .3s}.order-list-page .order-list[data-v-bac61a4e]{padding:%?20?%;overflow:auto}.order-list-page .order-list .each-order[data-v-bac61a4e]{background-color:#fff;padding:%?20?%;margin-bottom:%?20?%;border-radius:%?10?%}.order-list-page .order-list .each-order .header[data-v-bac61a4e]{display:flex;align-items:center;justify-content:space-between}.order-list-page .order-list .each-order .header .order-no-status[data-v-bac61a4e]{width:100%;display:flex;align-items:center;justify-content:space-between}.order-list-page .order-list .each-order .header .no[data-v-bac61a4e]{display:flex;align-items:center;color:#171b25;font-size:%?28?%}.order-list-page .order-list .each-order .header .status[data-v-bac61a4e]{color:#ff0046;font-size:%?28?%}.order-list-page .order-list .each-order .good-detail[data-v-bac61a4e]{border-bottom:%?1?% solid #f7f7f7}.order-list-page .order-list .each-order .good-detail .product_info[data-v-bac61a4e]{display:flex;padding:%?24?%;background-color:#fff;border-radius:%?10?%}.order-list-page .order-list .each-order .good-detail .product_info .prodct_left[data-v-bac61a4e]{display:flex;border-radius:%?10?%;overflow:hidden}.order-list-page .order-list .each-order .good-detail .product_info .product_center[data-v-bac61a4e]{flex:1;margin-left:%?20?%}.order-list-page .order-list .each-order .good-detail .product_info .product_right[data-v-bac61a4e]{margin-left:%?32?%}.order-list-page .order-list .each-order .good-detail .product_info .product_right .font_small[data-v-bac61a4e]{font-size:%?36?%;color:#171b25;font-weight:600}.order-list-page .order-list .each-order .opera-btns[data-v-bac61a4e]{margin-top:%?20?%}.order-list-page .order-list .each-order .opera-btns .each-btn[data-v-bac61a4e]{padding:%?10?% %?20?%;font-size:%?24?%;border:1px solid #ececec;border-radius:%?200?%;margin-left:%?20?%}.order-list-page .order-list .each-order .opera-btns .each-btn.pay[data-v-bac61a4e]{color:#fff;background-color:#bba186}',""]),t.exports=e},5703:function(t,e,r){"use strict";r.d(e,"b",(function(){return a})),r.d(e,"c",(function(){return o})),r.d(e,"a",(function(){}));var a=function(){var t=this,e=t.$createElement,r=t._self._c||e;return r("v-uni-view",{staticClass:"u-countdown"},[t.showDays&&(t.hideZeroDay||!t.hideZeroDay&&"00"!=t.d)?r("v-uni-view",{staticClass:"u-countdown-item",style:[t.itemStyle]},[r("v-uni-view",{staticClass:"u-countdown-time",style:[t.letterStyle]},[t._v(t._s(t.d))])],1):t._e(),t.showDays&&(t.hideZeroDay||!t.hideZeroDay&&"00"!=t.d)?r("v-uni-view",{staticClass:"u-countdown-colon",style:{fontSize:t.separatorSize+"rpx",color:t.separatorColor,paddingBottom:"colon"==t.separator?"4rpx":0}},[t._v(t._s("colon"==t.separator?":":"天"))]):t._e(),t.showHours?r("v-uni-view",{staticClass:"u-countdown-item",style:[t.itemStyle]},[r("v-uni-view",{staticClass:"u-countdown-time",style:{fontSize:t.fontSize+"rpx",color:t.color}},[t._v(t._s(t.h))])],1):t._e(),t.showHours?r("v-uni-view",{staticClass:"u-countdown-colon",style:{fontSize:t.separatorSize+"rpx",color:t.separatorColor,paddingBottom:"colon"==t.separator?"4rpx":0}},[t._v(t._s("colon"==t.separator?":":"时"))]):t._e(),t.showMinutes?r("v-uni-view",{staticClass:"u-countdown-item",style:[t.itemStyle]},[r("v-uni-view",{staticClass:"u-countdown-time",style:{fontSize:t.fontSize+"rpx",color:t.color}},[t._v(t._s(t.i))])],1):t._e(),t.showMinutes?r("v-uni-view",{staticClass:"u-countdown-colon",style:{fontSize:t.separatorSize+"rpx",color:t.separatorColor,paddingBottom:"colon"==t.separator?"4rpx":0}},[t._v(t._s("colon"==t.separator?":":"分"))]):t._e(),t.showSeconds?r("v-uni-view",{staticClass:"u-countdown-item",style:[t.itemStyle]},[r("v-uni-view",{staticClass:"u-countdown-time",style:{fontSize:t.fontSize+"rpx",color:t.color}},[t._v(t._s(t.s))])],1):t._e(),t.showSeconds&&"zh"==t.separator?r("v-uni-view",{staticClass:"u-countdown-colon",style:{fontSize:t.separatorSize+"rpx",color:t.separatorColor,paddingBottom:"colon"==t.separator?"4rpx":0}},[t._v("秒")]):t._e()],1)},o=[]},7263:function(t,e,r){var a=r("24fb");e=a(!1),e.push([t.i,'@charset "UTF-8";\r\n/* uni.scss */\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 字体变量 */\r\n/* 颜色变量 */\r\n/* 行为相关颜色 */\r\n/* 文字基本颜色 */\r\n/* 背景颜色 */\r\n/* 边框颜色 */\r\n/* 尺寸变量 */\r\n/* 文字尺寸 */\r\n/* 图片尺寸 */\r\n/* Border Radius */\r\n/* 水平间距 */\r\n/* 垂直间距 */\r\n/* 透明度 */\r\n/* 文章场景相关 */.u-countdown[data-v-4f122086]{display:inline-flex;align-items:center}.u-countdown-item[data-v-4f122086]{display:flex;flex-direction:row;align-items:center;justify-content:center;padding:%?2?%;border-radius:%?6?%;white-space:nowrap;-webkit-transform:translateZ(0);transform:translateZ(0)}.u-countdown-time[data-v-4f122086]{margin:0;padding:0;line-height:1}.u-countdown-colon[data-v-4f122086]{display:flex;flex-direction:row;justify-content:center;padding:0 %?5?%;line-height:1;align-items:center;padding-bottom:%?4?%}.u-countdown-scale[data-v-4f122086]{-webkit-transform:scale(.9);transform:scale(.9);-webkit-transform-origin:center center;transform-origin:center center}',""]),t.exports=e},"726c":function(t,e,r){"use strict";var a=r("3578"),o=r.n(a);o.a},"76c1":function(t,e,r){"use strict";r.r(e);var a=r("0e55"),o=r.n(a);for(var n in a)["default"].indexOf(n)<0&&function(t){r.d(e,t,(function(){return a[t]}))}(n);e["default"]=o.a},8084:function(t,e,r){"use strict";r.r(e);var a=r("5703"),o=r("76c1");for(var n in o)["default"].indexOf(n)<0&&function(t){r.d(e,t,(function(){return o[t]}))}(n);r("2b4a");var i=r("f0c5"),s=Object(i["a"])(o["default"],a["b"],a["c"],!1,null,"4f122086",null,!1,a["a"],void 0);e["default"]=s.exports},"93d2":function(t,e,r){"use strict";r("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,r("99af");var a=r("30ea"),o=r("9e56"),n={name:"orderList",data:function(){return{orderTypes:[{status:5,name:"全部"},{status:0,name:"待付款"},{status:1,name:"待核销"},{status:3,name:"已完成"}],tabIndex:"0",orderParams:{orderStatus:5,page:1,limit:10},loadStatus:"loading",loadText:{loadmore:"加载更多",loading:"努力加载中",nomore:"已经到底了"},activeColor:"#007aff",orderList:[],isLoadAll:!1}},filters:{formatState:function(t){return"0"===t?"待付款":"1"===t?"待核销":"3"===t?"已完成":"4"===t?"已取消":"7"===t?"已退款":void 0}},onPullDownRefresh:function(){this.isLoadAll=!1,this.orderParams.page=1,this.getOrderData()},onReachBottom:function(){console.log("加载中"),this.isLoadAll||(this.orderParams.page++,this.getOrderData())},onShow:function(){this.orderList=[],this.orderParams.page=1,this.getOrderData()},methods:{getOrderData:function(){var t=this;this.loadStatus="loading",o.request(a.myOrderUrl,this.orderParams,"POST").then((function(e){if(console.log(e),0!==e.code)uni.showToast({title:e.message,icon:"none"});else{for(var r=e.data.records,a=0;a<r.length;a++){var o=new Date,n=new Date(r[a].createDate),i=o.getTime()/1e3,s=(n.getTime()+18e5)/1e3,d=s-i;r[a].timestamp=d}t.orderList=t.orderList.concat(r||[]),t.isLoadAll=t.orderParams.page>=e.data.pages,t.loadStatus="nomore"}}))},onClickItem:function(t,e){this.tabIndex=e,this.orderParams.orderStatus=t.status,this.orderParams.page=1,this.orderList=[],this.getOrderData()},cancelOrderEvent:function(t){var e=this;o.request(a.cancelOrderUrl+t.orderNo,{},"POST").then((function(t){console.log(t),0!==t.code?uni.showToast({title:t.message,icon:"none"}):(uni.showToast({title:"取消订单成功",icon:"none"}),e.orderList=[],e.orderParams.page=1,e.getOrderData())}))},onCopy:function(t){uni.setClipboardData({data:t,success:function(){uni.showToast({title:"复制成功",icon:"success"})}})},enterOrderDetailPage:function(t){uni.navigateTo({url:"/pages/order/orderDetail?orderNo="+t.orderNo})},toPayMoney:function(t,e){}}};e.default=n},e254:function(t,e,r){var a=r("3f71");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var o=r("4f06").default;o("c54bca20",a,!0,{sourceMap:!1,shadowMode:!1})},f410:function(t,e,r){"use strict";r.r(e);var a=r("14d96"),o=r("2f8d");for(var n in o)["default"].indexOf(n)<0&&function(t){r.d(e,t,(function(){return o[t]}))}(n);r("726c"),r("1ea1");var i=r("f0c5"),s=Object(i["a"])(o["default"],a["b"],a["c"],!1,null,"bac61a4e",null,!1,a["a"],void 0);e["default"]=s.exports}}]);