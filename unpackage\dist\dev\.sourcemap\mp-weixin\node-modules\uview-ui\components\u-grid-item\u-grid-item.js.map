{"version": 3, "sources": ["webpack:///D:/work/IdeaProjects/test/pos2_hhlm/node_modules/uview-ui/components/u-grid-item/u-grid-item.vue?6cb8", "webpack:///D:/work/IdeaProjects/test/pos2_hhlm/node_modules/uview-ui/components/u-grid-item/u-grid-item.vue?80a4", "webpack:///D:/work/IdeaProjects/test/pos2_hhlm/node_modules/uview-ui/components/u-grid-item/u-grid-item.vue?8221", "webpack:///D:/work/IdeaProjects/test/pos2_hhlm/node_modules/uview-ui/components/u-grid-item/u-grid-item.vue?5ac0", "uni-app:///node_modules/uview-ui/components/u-grid-item/u-grid-item.vue", "webpack:///D:/work/IdeaProjects/test/pos2_hhlm/node_modules/uview-ui/components/u-grid-item/u-grid-item.vue?7d09", "webpack:///D:/work/IdeaProjects/test/pos2_hhlm/node_modules/uview-ui/components/u-grid-item/u-grid-item.vue?10dd"], "names": ["name", "props", "bgColor", "type", "default", "index", "customStyle", "padding", "data", "parentData", "hoverClass", "col", "border", "created", "computed", "width", "methods", "updateParentData", "click"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAoI;AACpI;AAC+D;AACL;AACsC;;;AAGhG;AACwL;AACxL,gBAAgB,sLAAU;AAC1B,EAAE,iFAAM;AACR,EAAE,kGAAM;AACR,EAAE,2GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,sGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjBA;AAAA;AAAA;AAAA;AAAitB,CAAgB,kqBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACaruB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AATA,gBAUA;EACAA;EACAC;IACA;IACAC;MACAC;MACAC;IACA;IACA;IACAC;MACAF;MACAC;IACA;IACA;IACAE;MACAH;MACAC;QACA;UACAG;QACA;MACA;IACA;EACA;EACAC;IACA;MACAC;QACAC;QAAA;QACAC;QAAA;QACAC;MACA;IACA;EACA;EACAC;IACA;IACA;IACA;IACA;EACA;EACAC;IACA;IACAC;MACA;IACA;EACA;EACAC;IACA;IACAC;MACA;MACA;IACA;IACAC;MACA;MACA;IACA;EACA;AACA;AAAA,4B;;;;;;;;;;;;AC9EA;AAAA;AAAA;AAAA;AAAw2C,CAAgB,6sCAAG,EAAC,C;;;;;;;;;;;ACA53C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "node-modules/uview-ui/components/u-grid-item/u-grid-item.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./u-grid-item.vue?vue&type=template&id=99a45d26&scoped=true&\"\nvar renderjs\nimport script from \"./u-grid-item.vue?vue&type=script&lang=js&\"\nexport * from \"./u-grid-item.vue?vue&type=script&lang=js&\"\nimport style0 from \"./u-grid-item.vue?vue&type=style&index=0&id=99a45d26&scoped=true&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../software/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"99a45d26\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"node_modules/uview-ui/components/u-grid-item/u-grid-item.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../software/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../software/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../software/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../software/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../software/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../software/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-grid-item.vue?vue&type=template&id=99a45d26&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var s0 = _vm.__get_style([_vm.customStyle])\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        s0: s0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../software/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../software/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../software/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../software/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../software/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-grid-item.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../software/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../software/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../software/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../software/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../software/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-grid-item.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"u-grid-item\" :hover-class=\"parentData.hoverClass\"\r\n\t :hover-stay-time=\"200\" @tap=\"click\" :style=\"{\r\n\t\t\tbackground: bgColor,\r\n\t\t\twidth: width,\r\n\t\t}\">\r\n\t\t<view class=\"u-grid-item-box\" :style=\"[customStyle]\" :class=\"[parentData.border ? 'u-border-right u-border-bottom' : '']\">\r\n\t\t\t<slot />\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\t/**\r\n\t * gridItem 提示\r\n\t * @description 宫格组件一般用于同时展示多个同类项目的场景，可以给宫格的项目设置徽标组件(badge)，或者图标等，也可以扩展为左右滑动的轮播形式。搭配u-grid使用\r\n\t * @tutorial https://www.uviewui.com/components/grid.html\r\n\t * @property {String} bg-color 宫格的背景颜色（默认#ffffff）\r\n\t * @property {String Number} index 点击宫格时，返回的值\r\n\t * @property {Object} custom-style 自定义样式，对象形式\r\n\t * @event {Function} click 点击宫格触发\r\n\t * @example <u-grid-item></u-grid-item>\r\n\t */\r\n\texport default {\r\n\t\tname: \"u-grid-item\",\r\n\t\tprops: {\r\n\t\t\t// 背景颜色\r\n\t\t\tbgColor: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: '#ffffff'\r\n\t\t\t},\r\n\t\t\t// 点击时返回的index\r\n\t\t\tindex: {\r\n\t\t\t\ttype: [Number, String],\r\n\t\t\t\tdefault: ''\r\n\t\t\t},\r\n\t\t\t// 自定义样式，对象形式\r\n\t\t\tcustomStyle: {\r\n\t\t\t\ttype: Object,\r\n\t\t\t\tdefault() {\r\n\t\t\t\t\treturn {\r\n\t\t\t\t\t\tpadding: '30rpx 0'\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tparentData: {\r\n\t\t\t\t\thoverClass: '', // 按下去的时候，是否显示背景灰色\r\n\t\t\t\t\tcol: 3, // 父组件划分的宫格数\r\n\t\t\t\t\tborder: true, // 是否显示边框，根据父组件决定\r\n\t\t\t\t}\r\n\t\t\t};\r\n\t\t},\r\n\t\tcreated() {\r\n\t\t\t// 父组件的实例\r\n\t\t\tthis.updateParentData();\r\n\t\t\t// this.parent在updateParentData()中定义\r\n\t\t\tthis.parent.children.push(this);\r\n\t\t},\r\n\t\tcomputed: {\r\n\t\t\t// 每个grid-item的宽度\r\n\t\t\twidth() {\r\n\t\t\t\treturn 100 / Number(this.parentData.col) + '%';\r\n\t\t\t},\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\t// 获取父组件的参数\r\n\t\t\tupdateParentData() {\r\n\t\t\t\t// 此方法写在mixin中\r\n\t\t\t\tthis.getParentData('u-grid');\r\n\t\t\t},\r\n\t\t\tclick() {\r\n\t\t\t\tthis.$emit('click', this.index);\r\n\t\t\t\tthis.parent && this.parent.click(this.index);\r\n\t\t\t}\r\n\t\t}\r\n\t};\r\n</script>\r\n\r\n<style scoped lang=\"scss\">\r\n\t@import \"../../libs/css/style.components.scss\";\r\n\t\r\n\t.u-grid-item {\r\n\t\tbox-sizing: border-box;\r\n\t\tbackground: #fff;\r\n\t\t@include vue-flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\tposition: relative;\r\n\t\tflex-direction: column;\r\n\t\t\r\n\t\t/* #ifdef MP */\r\n\t\tposition: relative;\r\n\t\tfloat: left;\r\n\t\t/* #endif */\r\n\t}\r\n\r\n\t.u-grid-item-hover {\r\n\t\tbackground: #f7f7f7 !important;\r\n\t}\r\n\r\n\t.u-grid-marker-box {\r\n\t\tposition: absolute;\r\n\t\t/* #ifndef APP-NVUE */\r\n\t\tdisplay: inline-flex;\t\t\r\n\t\t/* #endif */\r\n\t\tline-height: 0;\r\n\t}\r\n\r\n\t.u-grid-marker-wrap {\r\n\t\tposition: absolute;\r\n\t}\r\n\r\n\t.u-grid-item-box {\r\n\t\tpadding: 30rpx 0;\r\n\t\t@include vue-flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\tflex-direction: column;\r\n\t\tflex: 1;\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t}\r\n</style>\r\n", "import mod from \"-!../../../../../../../../software/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../software/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../software/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../software/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../software/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../software/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../software/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../software/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../software/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-grid-item.vue?vue&type=style&index=0&id=99a45d26&scoped=true&lang=scss&\"; export default mod; export * from \"-!../../../../../../../../software/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../software/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../software/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../software/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../software/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../software/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../software/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../software/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../software/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./u-grid-item.vue?vue&type=style&index=0&id=99a45d26&scoped=true&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1752369745384\n      var cssReload = require(\"D:/software/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}