(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-notice-noticeDetail"],{"4f3c":function(i,t,s){"use strict";s.d(t,"b",(function(){return a})),s.d(t,"c",(function(){return o})),s.d(t,"a",(function(){}));var a=function(){var i=this,t=i.$createElement,s=i._self._c||t;return s("v-uni-view",{staticClass:"notice-detail"},[s("v-uni-view",{staticClass:"title"},[i._v(i._s(i.data.title))]),s("v-uni-view",{staticClass:"date"},[i._v(i._s(i.data.createDate))]),s("v-uni-view",{staticClass:"content",domProps:{innerHTML:i._s(i.data.content)}})],1)},o=[]},"69f5":function(i,t,s){"use strict";s.r(t);var a=s("4f3c"),o=s("cb48");for(var n in o)["default"].indexOf(n)<0&&function(i){s.d(t,i,(function(){return o[i]}))}(n);s("9ac3");var e=s("f0c5"),c=Object(e["a"])(o["default"],a["b"],a["c"],!1,null,"77fff496",null,!1,a["a"],void 0);t["default"]=c.exports},"9ac3":function(i,t,s){"use strict";var a=s("d8eb"),o=s.n(a);o.a},b5ae:function(i,t,s){"use strict";s("7a82"),Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var a={name:"noticeDetail",data:function(){return{data:'<div class="post-content_main-post-info__qCbZu"><div class="index_bbs-thread-comp__PC7_r bbs-thread-comp main-thread"><div class="thread-content-detail">    <p></p><div data-hupu-node="image"><p><p class="image-wrapper"><span class="img-wrapper-embedded" id="img-wrapper-embedded-0" style="width: auto; max-width: 100%;"><div class="lazyload-wrapper "><image src="https://i1.hoopchina.com.cn/news-editor/2025-3-20/15-31-12/57ad0a38-2319-43bb-a838-7384649a8061.png?x-oss-process=image/resize,w_800/format,webp" alt="" class="thread-img" style="width: auto; max-width: 100%; height: auto;"></div></span></p></p></div><p>虎扑03月20日讯 今日NBA常规赛，国王主场123-119战胜骑士。</p><p>赛后，国王主教练道格-克里斯蒂接受了记者采访，谈到了这场赢下东部第一的胜利。</p><p>记者提问：“在第一节球队只得到了15分，但之后球队拿出了上佳的表现，这中间发生了什么？”</p><p>克里斯蒂表示：“我知道骑士有非常多优秀的球员，像多诺万（米切尔），埃文（莫布利），但我们后三节打出了我们自己的篮球，萨克拉门托式的篮球。但我们穿上这身球员，这就是我们希望做到的。我给队员传递的信息是，团结在彼此周围，为彼此战斗。”</p><p>今日获胜后，国王战绩来到35胜33负，位列西部第9。</p> <p></p><br><div class="article-source">  来源： X</div><br><br><br></div><div class="seo-dom">    <p></p><div data-hupu-node="image"><p><image src="https://i1.hoopchina.com.cn/news-editor/2025-3-20/15-31-12/57ad0a38-2319-43bb-a838-7384649a8061.png?x-oss-process=image/resize,w_800/format,webp"></p></div><p>虎扑03月20日讯 今日NBA常规赛，国王主场123-119战胜骑士。</p><p>赛后，国王主教练道格-克里斯蒂接受了记者采访，谈到了这场赢下东部第一的胜利。</p><p>记者提问：“在第一节球队只得到了15分，但之后球队拿出了上佳的表现，这中间发生了什么？”</p><p>克里斯蒂表示：“我知道骑士有非常多优秀的球员，像多诺万（米切尔），埃文（莫布利），但我们后三节打出了我们自己的篮球，萨克拉门托式的篮球。但我们穿上这身球员，这就是我们希望做到的。我给队员传递的信息是，团结在彼此周围，为彼此战斗。”</p><p>今日获胜后，国王战绩来到35胜33负，位列西部第9。</p> <p></p><br><div class="article-source">  来源： X</div><br><br><br></div></div><div class="post-operate_post-operate-comp-wrapper___odBI"><div class="post-operate-comp main-operate"><div class="post-operate-comp-main"><div class="post-operate-comp-main-recommend hove-deep todo-list "><i class="iconfont icontuijian todo-list-icon"></i><span class="todo-list-text">推荐\x3c!-- --\x3e (2)</span></div><div class="post-operate-comp-main-reply todo-list"><i class="iconfont iconpinglun todo-list-icon"></i><span class="todo-list-text">评论\x3c!-- --\x3e (10)</span></div><div class="post-operate-comp-main-collect  todo-list"><i class="iconfont iconshoucang todo-list-icon"></i><span class="todo-list-text">收藏</span></div><div class="post-operate-comp-main-share todo-list"><i class="iconfont iconfenxiang todo-list-icon"></i><span class="todo-list-text">分享</span><div class="share-modal"><div class="prefix"></div><div class="ct"><div class="left-share"><div class="icons"><div class="icon-list"><i class="iconfont iconQQ icon-list-img"></i><p class="icon-list-name">QQ</p></div><div class="icon-list"><i class="iconfont iconQQkongjian icon-list-img"></i><p class="icon-list-name">QQ空间</p></div><div class="icon-list"><i class="iconfont iconxinlangweibo icon-list-img"></i><p class="icon-list-name">微博</p></div></div><div class="copy-board"><div class="copy-value">https://bbs.hupu.com/631253845.html?is_reflow=pc</div><div title="点击复制分享地址" class="copy-btn">复制</div></div></div><div class="right-qrcode"><p class="qr-tip">微信扫一扫分享</p><div class="qr-img"></div></div></div></div></div></div><div class="post-operate-comp-other"><div class="post-operate-comp-other-report todo-list"><i class="iconfont iconjubao1 todo-list-icon"></i><span class="todo-list-text">举报</span></div><div class="post-operate-comp-other-only-main todo-list"><i class="iconfont iconzhikanlouzhu todo-list-icon"></i><span class="todo-list-text">只看楼主</span></div></div></div></div></div>'}},onLoad:function(){this.data=uni.getStorageSync("noticeData")||{}},onUnload:function(){uni.removeStorageSync("noticeData")}};t.default=a},cb48:function(i,t,s){"use strict";s.r(t);var a=s("b5ae"),o=s.n(a);for(var n in a)["default"].indexOf(n)<0&&function(i){s.d(t,i,(function(){return a[i]}))}(n);t["default"]=o.a},d6a0:function(i,t,s){var a=s("24fb");t=a(!1),t.push([i.i,'@charset "UTF-8";\r\n/* uni.scss */\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 字体变量 */\r\n/* 颜色变量 */\r\n/* 行为相关颜色 */\r\n/* 文字基本颜色 */\r\n/* 背景颜色 */\r\n/* 边框颜色 */\r\n/* 尺寸变量 */\r\n/* 文字尺寸 */\r\n/* 图片尺寸 */\r\n/* Border Radius */\r\n/* 水平间距 */\r\n/* 垂直间距 */\r\n/* 透明度 */\r\n/* 文章场景相关 */.notice-detail[data-v-77fff496]{padding:%?20?%;background-color:#fff}.notice-detail .title[data-v-77fff496]{font-size:%?30?%;font-weight:700;margin-bottom:%?20?%}.notice-detail .date[data-v-77fff496]{font-size:%?24?%;color:#999;margin-bottom:%?20?%}',""]),i.exports=t},d8eb:function(i,t,s){var a=s("d6a0");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[i.i,a,""]]),a.locals&&(i.exports=a.locals);var o=s("4f06").default;o("90e72466",a,!0,{sourceMap:!1,shadowMode:!1})}}]);