{"version": 3, "sources": ["uni-app:///main.js", "webpack:///E:/pos/黄金/gold_client/pages/indexChild/payment/payment.vue?f36d", "webpack:///E:/pos/黄金/gold_client/pages/indexChild/payment/payment.vue?955f", "webpack:///E:/pos/黄金/gold_client/pages/indexChild/payment/payment.vue?dcfc", "webpack:///E:/pos/黄金/gold_client/pages/indexChild/payment/payment.vue?ddcc", "uni-app:///pages/indexChild/payment/payment.vue", "webpack:///E:/pos/黄金/gold_client/pages/indexChild/payment/payment.vue?7a6d", "webpack:///E:/pos/黄金/gold_client/pages/indexChild/payment/payment.vue?584b", "webpack:///E:/pos/黄金/gold_client/pages/indexChild/payment/payment.vue?2546", "webpack:///E:/pos/黄金/gold_client/pages/indexChild/payment/payment.vue?29d8"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "number", "goodsId", "orderNo", "ordeForm", "orderPayForm", "goodsInfo", "storeInfo", "yqrInfo", "webCode", "onLoad", "mounted", "onShow", "uni", "key", "success", "that", "methods", "getUser", "location", "url", "onUser", "title", "icon", "getGoodsdetail", "util", "api", "res", "console", "settleOrder", "goodsNum", "type", "pickStore", "reference", "yqr", "addressId", "code", "payTyle", "result", "fail", "onBridgeReady", "WeixinJSBridge", "duration", "setTimeout", "paymentRequest", "timeStamp", "nonceStr", "package", "signType", "paySign", "getCurrentDateTime"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,gBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAgI;AAChI;AAC2D;AACL;AACa;AACyB;;;AAG5F;AACgL;AAChL,gBAAgB,yLAAU;AAC1B,EAAE,6EAAM;AACR,EAAE,8FAAM;AACR,EAAE,uGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,kGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACxBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAwpB,CAAgB,6qBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;ACgE5qB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACA;AACA;AAAA,eACA;EACAC;IACA;MACAC;MAAA;MACAC;MACAC;MACAC;QACA;QACA;QACA;QACA;QACA;QACA;MACA;MACAC;QACA;QACA;QACA;QACA;MACA;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACA;IACA;IACA;EACA;EACAC,6BAQA;EACAC;IACA;IACAC;MACAC;MACAC;QACAC;MACA;IACA;EACA;EACAC;IACAC;MAAA;MACA;QACA;MACA;IACA;IACAC;MACAN;QACAO;MACA;MACA;IACA;IACAC;MACA;QACAR;UACAS;UACAC;QACA;QACA;MACA;MACAV;QACAO;MACA;IACA;IACAI;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,OACAC,aACAC,wDACA,OACA;cAAA;gBAHAC;gBAIAC;gBACA;kBACAf;oBACAS;oBACAC;kBACA;gBAEA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACA;IACAM;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAD;gBACAf;kBACAS;gBACA;gBACAN;gBAAA;gBAAA,OACAS,aACAC;kBACAxB;kBACA4B;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;gBACA,GACA,OACA;cAAA;gBAXAR;gBAYAC;gBACA;kBACAf;oBACAS;oBACAC;kBACA;gBAEA;kBACAP;kBAEAH;oBACA;oBACA;oBAAA;oBACAE;sBAAA;wBAAA;wBAAA;0BAAA;4BAAA;8BAAA;gCACAqB;gCAAA;gCAAA,OACAX,aACAC;kCACAvB;kCACAkC;kCACAD;gCACA,GACA,OACA;8BAAA;gCAPAE;gCAQA;kCACAzB;oCACAS;oCACAC;kCACA;gCAEA;kCACAP;gCACA;8BAAA;8BAAA;gCAAA;4BAAA;0BAAA;wBAAA;sBAAA,CACA;sBAAA;wBAAA;sBAAA;sBAAA;oBAAA;oBACAuB;sBACA;sBACA;oBAAA;kBAEA;gBA8BA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACA;IACAC;MACA;MACAC,qDACA;QACA;UACAb;UACA;UACAf;YACAS;YACAC;YACAmB;UACA;UACA;UACAC;YACA;YACA9B;cACAO;YACA;UACA;QACA;UACA;UACA;UACA;UACA;UACAP;UACA8B;YACA;YACA9B;cACAO;YACA;UACA;QACA;MACA;IACA;IACA;IACAwB;MACA;MACA;MACA/B;QACAgC;QACAC;QACAC;QACAC;QACAC;QACAlC;UACAF;YACAS;YACAC;YACAmB;UACA;UACAC;YACA;YACA9B;cACAO;YACA;UACA;UACAQ;QACA;QACAW;UACA;UACA;UACA;UACA;UACA1B;UACA8B;YACA;YACA9B;cACAO;YACA;UACA;QACA;MACA;IACA;IACA8B;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC3UA;AAAA;AAAA;AAAA;AAAg8B,CAAgB,07BAAG,EAAC,C;;;;;;;;;;;ACAp9B;AACA,OAAO,KAAU,EAAE,kBAKd;;;;;;;;;;;;;ACNL;AAAA;AAAA;AAAA;AAAuwC,CAAgB,ouCAAG,EAAC,C;;;;;;;;;;;ACA3xC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/indexChild/payment/payment.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/indexChild/payment/payment.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./payment.vue?vue&type=template&id=299f44de&scoped=true&\"\nvar renderjs\nimport script from \"./payment.vue?vue&type=script&lang=js&\"\nexport * from \"./payment.vue?vue&type=script&lang=js&\"\nimport style0 from \"./payment.vue?vue&type=style&index=0&lang=css&\"\nimport style1 from \"./payment.vue?vue&type=style&index=1&id=299f44de&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"299f44de\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/indexChild/payment/payment.vue\"\nexport default component.exports", "export * from \"-!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./payment.vue?vue&type=template&id=299f44de&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./payment.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./payment.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"page\">\r\n\t\t<form @submit=\"settleOrder\">\r\n\t\t<view class=\"address\">\r\n\t\t\t<view v-if=\"storeInfo.id\">\r\n\t\t\t\t<view style=\"display: flex;justify-content: space-between;align-items: center;\">\r\n\t\t\t\t\t<view class=\"address_top\">\r\n\t\t\t\t\t\t<view style=\"display: flex;\"><image src=\"/static/img/shop/shop_store.png\" mode=\"widthFix\" style=\"width: 40rpx;height: 40rpx;\"></image></view>\r\n\t\t\t\t\t\t<view style=\"margin-left: 10rpx;ffont-size: 32rpx;font-weight: 600;\">{{storeInfo.name}}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view @click=\"location('/pages/promotion/store')\" style=\"display: flex; align-items: center;font-size: 24rpx;\">切换门店 <view class=\"right-icon\"></view></view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view style=\"margin-top: 20rpx;color: #61687C;font-size: 24rpx;\">{{storeInfo.address}}</view>\r\n\t\t\t</view>\r\n\t\t\t<view @click=\"location('/pages/promotion/store')\" class=\"address_right\" v-else style=\"width: 100%;display: flex;align-items: center;justify-content: space-between;\">\r\n\t\t\t\t<view style=\"display: flex;align-items: center;\">\r\n\t\t\t\t\t<image src=\"/static/img/shop/shop_store.png\" mode=\"widthFix\" style=\"width: 40rpx;height: 40rpx;\">\r\n\t\t\t\t\t<text style=\"margin-left: 10rpx;\">请选择门店</text>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"right-icon\"></view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view class=\"product_info\">\r\n\t\t\t<view class=\"prodct_left\">\r\n\t\t\t\t<image style=\"width:164rpx;height: 164rpx;\" :src=\"goodsInfo.goodsImg\"></image>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"product_center\">\r\n\t\t\t\t<view style=\"font-size: 28rpx;line-height: 44rpx;\" class=\"text-ellipsis_2\">{{goodsInfo.goodsName}}</view>\r\n\t\t\t\t<view style=\"display: flex;margin-top: 20rpx;\">\r\n\t\t\t\t\t<view style=\"color: #61687C;font-size: 24rpx;background-color: #F2F4F7;text-align: center;padding: 6rpx 20rpx;border-radius: 6rpx;\">{{goodsInfo.specification}}g</view>\r\n\t\t\t\t\t<view></view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"product_right\">\r\n\t\t\t\t<view><text class=\"font_small\">¥{{goodsInfo.price}}</text></view>\r\n\t\t\t\t<view style=\"font-size: 24rpx;color: #9FA3B0;margin-top: 8rpx;text-align: right;\">x1</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view class=\"inviter\" @click=\"onUser('/pages/indexChild/userList/userList')\">\r\n\t\t\t<view>邀请人</view>\r\n\t\t\t<view style=\"display: flex;align-items: center;\">\r\n\t\t\t\t<view style=\"display: flex;\">\r\n\t\t\t\t\t<input class=\"uni-input\" name=\"yqr\" :value=\"yqrInfo.name\" disabled=\"true\" readOnly=\"true\" placeholder=\"请选择邀请人\" />\r\n\t\t\t\t\t</view>\r\n\t\t\t\t<view class=\"right-icon\" style=\"margin-left: 20rpx;\"></view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view class=\"pay_type\">\r\n\t\t\t<view>\r\n\t\t\t\t<view style=\"display: flex;\"><image style=\"width: 56rpx;height: 56rpx;\" src=\"/static/img/shop/shop_wx.png\" alt=\"\" /></view>\r\n\t\t\t</view>\r\n\t\t\t<view style=\"display: flex;justify-content: space-between;align-items: center;flex: 1;margin-left: 24rpx;\">\r\n\t\t\t\t<view style=\"font-size: 28rpx;\">微信</view>\r\n\t\t\t\t<view><image style=\"width: 48rpx;height: 48rpx;\" src=\"/static/img/shop/shop_round_a.png\" alt=\"\" /></view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view class=\"goods_detail_footer\">\r\n\t\t\t<button class=\"btn\" form-type=\"submit\">确认支付</button>\r\n\t\t</view>\r\n\t\t</form>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport number from \"../../../utils/number.js\";\r\n\tconst api = require('../../../config/api');\r\n\tconst util = require('../../../utils/util');\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tnumber: number, //声明number属性并赋值为引入的number模块\r\n\t\t\t\tgoodsId: '',\r\n\t\t\t\torderNo: '',\r\n\t\t\t\tordeForm: {\r\n\t\t\t\t\t\"addressId\": 0,\r\n\t\t\t\t\t\"goodsId\": 0,\r\n\t\t\t\t\t\"goodsNum\": 0,\r\n\t\t\t\t\t\"pickStore\": \"\",\r\n\t\t\t\t\t\"reference\": \"\",\r\n\t\t\t\t\t\"type\": \"\"\r\n\t\t\t\t},\r\n\t\t\t\torderPayForm: {\r\n\t\t\t\t\t\"code\": \"\",\r\n\t\t\t\t\t\"img\": \"\",\r\n\t\t\t\t\t\"orderNo\": '',\r\n\t\t\t\t\t\"payTyle\": 3\r\n\t\t\t\t},\r\n\t\t\t\tgoodsInfo: {},\r\n\t\t\t\tstoreInfo: {},\r\n\t\t\t\tyqrInfo: {},\r\n\t\t\t\twebCode: ''\r\n\t\t\t}\r\n\t\t},\r\n\t\tonLoad(o) {\r\n\t\t\tthis.goodsId = o.goodsId\r\n\t\t\tthis.goodsNum = parseInt(o.goodsNum)\r\n\t\t\tthis.getGoodsdetail();\r\n\t\t\tthis.checked = true\r\n\t\t},\r\n\t\tmounted(){\r\n\t\t\t\t// #ifdef WEB\r\n\t\t\t\tvar currentUrl = window.location.href.split('?');\r\n\t\t\t\tlet code = currentUrl[1].split('&')\r\n\t\t\t\tconsole.log(currentUrl)\r\n\t\t\t\tconsole.log(code)\r\n\t\t\t\tthis.webCode = code[0].split('=')[1]\r\n\t\t\t\t// #endif\r\n\t\t},\r\n\t\tonShow() {\r\n\t\t\tlet that = this\r\n\t\t\tuni.getStorage({\r\n\t\t\t\tkey: 'store_info',\r\n\t\t\t\tsuccess: function (res) {\r\n\t\t\t\t\tthat.storeInfo = res.data\r\n\t\t\t\t}\r\n\t\t\t});\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tgetUser(data){\r\n\t\t\t\tthis.$nextTick(()=>{\r\n\t\t\t\t\tthis.yqrInfo = data\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tlocation(url){\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: url\r\n\t\t\t\t})\r\n\t\t\t\tthis.yqrInfo = {}\r\n\t\t\t},\r\n\t\t\tonUser(url){\r\n\t\t\t\tif(!this.storeInfo.id){\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '请选择门店',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t})\r\n\t\t\t\t\treturn\r\n\t\t\t\t}\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: url+'?storeId='+this.storeInfo.id\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tasync getGoodsdetail() {\r\n\t\t\t\tconst res = await util.request(\r\n\t\t\t\t\tapi.goodsDetailsUrl + '?goodsId=' + this.goodsId, {},\r\n\t\t\t\t\t'POST'\r\n\t\t\t\t);\r\n\t\t\t\tconsole.log(res);\r\n\t\t\t\tif (res.code !== 0) {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: res.msg,\r\n\t\t\t\t\t\ticon: \"none\"\r\n\t\t\t\t\t})\r\n\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.goodsInfo = res.data.result\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t// 先获取订单号、传入订单号获取支付数据、传入支付数据调用微信支付\r\n\t\t\tasync settleOrder(e) {\r\n\t\t\t\tconsole.log(e)\r\n\t\t\t\tuni.showLoading({\r\n\t\t\t\t\ttitle: '支付中'\r\n\t\t\t\t});\r\n\t\t\t\tlet that = this\r\n\t\t\t\tconst res = await util.request(\r\n\t\t\t\t\tapi.settleOrderUrl, {\r\n\t\t\t\t\t\tgoodsId: that.goodsId,\r\n\t\t\t\t\t\tgoodsNum: 1,\r\n\t\t\t\t\t\ttype: 2,\r\n\t\t\t\t\t\tpickStore: \"\",\r\n\t\t\t\t\t\treference: \"\",\r\n\t\t\t\t\t\tyqr: e.detail.value.yqr,\r\n\t\t\t\t\t\taddressId: that.storeInfo.id\r\n\t\t\t\t\t},\r\n\t\t\t\t\t'POST'\r\n\t\t\t\t);\r\n\t\t\t\tconsole.log(res);\r\n\t\t\t\tif (res.code !== 0) {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: res.msg,\r\n\t\t\t\t\t\ticon: \"none\"\r\n\t\t\t\t\t})\r\n\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthat.orderNo = res.data\r\n\t\t\t\t\t// #ifdef MP-WEIXIN\t\r\n\t\t\t\t\tuni.login({\r\n\t\t\t\t\t\t\"provider\": \"weixin\",\r\n\t\t\t\t\t\t\"onlyAuthorize\": true, // 微信登录仅请求授权认证\r\n\t\t\t\t\t\tsuccess: async function(event){\r\n\t\t\t\t\t\t\tconst {code} = event\r\n\t\t\t\t\t\t\tconst result = await util.request(\r\n\t\t\t\t\t\t\t\tapi.orderPayUrl, {\r\n\t\t\t\t\t\t\t\t\torderNo: res.data,\r\n\t\t\t\t\t\t\t\t\tpayTyle: '8',\r\n\t\t\t\t\t\t\t\t\tcode: event.code\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t'POST'\r\n\t\t\t\t\t\t\t);\r\n\t\t\t\t\t\t\tif (result.code !== 0) {\r\n\t\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\t\ttitle: result.msg,\r\n\t\t\t\t\t\t\t\t\ticon: \"none\"\r\n\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\tthat.paymentRequest(result.data)\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t\tfail: function (err) {\r\n\t\t\t\t\t        // 登录授权失败\r\n\t\t\t\t\t        // err.code是错误码\r\n\t\t\t\t\t    }\r\n\t\t\t\t\t})\r\n\t\t\t\t\t// #endif\r\n\t\t\t\t\t// #ifdef WEB\r\n\t\t\t\t\tconst result = await util.request(\r\n\t\t\t\t\t\tapi.orderPayUrl, {\r\n\t\t\t\t\t\t\torderNo: res.data,\r\n\t\t\t\t\t\t\tpayTyle: '7',\r\n\t\t\t\t\t\t\tcode: that.webCode\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t\t'POST'\r\n\t\t\t\t\t);\r\n\t\t\t\t\tif (result.code !== 0) {\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: result.msg,\r\n\t\t\t\t\t\t\ticon: \"none\"\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tconst payWeb = JSON.parse(result.data.jspay_info)\r\n\t\t\t\t\t\tlet param = {\r\n\t\t\t\t\t\t\t\"appId\": \"wxa56aa346588ae42f\", //公众号ID，由你传入     \r\n\t\t\t\t\t\t\t\"timeStamp\": payWeb.timeStamp, //时间戳，自1970年以来的秒数     \r\n\t\t\t\t\t\t\t\"nonceStr\": payWeb.nonceStr, //随机串     \r\n\t\t\t\t\t\t\t\"package\": payWeb.package,\r\n\t\t\t\t\t\t\t\"signType\": payWeb.signType, //微信签名方式：     \r\n\t\t\t\t\t\t\t\"paySign\": payWeb.paySign, //微信签名 \r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tthat.onBridgeReady(param)\r\n\t\t\t\t\t}\r\n\t\t\t\t\t// #endif\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t// 微信公众号支付\r\n\t\t\tonBridgeReady(param) {\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tWeixinJSBridge.invoke('getBrandWCPayRequest', param,\r\n\t\t\t\t\tfunction(res) {\r\n\t\t\t\t\t\tif (res.err_msg == \"get_brand_wcpay_request:ok\") {\r\n\t\t\t\t\t\t\tconsole.log(\"微信支付成功了！！！\")\r\n\t\t\t\t\t\t\t// 支付成功的回调中\r\n\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\ttitle: '支付成功',\r\n\t\t\t\t\t\t\t\ticon: 'success',\r\n\t\t\t\t\t\t\t\tduration: 1500\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t//支付成功后重新回到订单详情界面并刷新\r\n\t\t\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\t\t\t// 订单详情页面\r\n\t\t\t\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\t\t\t\turl: '/pages/order/orderDetail?orderNo='+that.orderNo\r\n\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t}, 2000);\r\n\t\t\t\t\t\t}else{\r\n\t\t\t\t\t\t\t// uni.showToast({\r\n\t\t\t\t\t\t\t// \ttitle: '支付失败',\r\n\t\t\t\t\t\t\t// \ticon:'none'\r\n\t\t\t\t\t\t\t// })\r\n\t\t\t\t\t\t\tuni.hideLoading()\r\n\t\t\t\t\t\t\tsetTimeout(()=>{\r\n\t\t\t\t\t\t\t\t// 订单详情页面\r\n\t\t\t\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\t\t\t\turl: '/pages/order/orderDetail?orderNo='+that.orderNo\r\n\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t},1000)\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t// 微信小程序支付\r\n\t\t\tpaymentRequest(params) {\r\n\t\t\t\tlet that = this\r\n\t\t\t\tconst payInfo = JSON.parse(params.jspay_info)\r\n\t\t\t\tuni.requestPayment({\r\n\t\t\t\t\ttimeStamp: payInfo.timeStamp,\r\n\t\t\t\t\tnonceStr: payInfo.nonceStr,\r\n\t\t\t\t\tpackage: payInfo.package,\r\n\t\t\t\t\tsignType: payInfo.signType,\r\n\t\t\t\t\tpaySign: payInfo.paySign,\r\n\t\t\t\t    success: function (res) {\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: '支付成功',\r\n\t\t\t\t\t\t\ticon: 'success',\r\n\t\t\t\t\t\t\tduration: 1500\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t\tsetTimeout(()=>{\r\n\t\t\t\t\t\t\t// 订单详情页面\r\n\t\t\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\t\t\turl: '/pages/order/orderDetail?orderNo='+that.orderNo\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t},1000)\r\n\t\t\t\t        console.log('success:' + JSON.stringify(res));\r\n\t\t\t\t    },\r\n\t\t\t\t    fail: function (err) {\r\n\t\t\t\t\t\t// uni.showToast({\r\n\t\t\t\t\t\t// \ttitle:'支付失败',\r\n\t\t\t\t\t\t// \ticon:'none'\r\n\t\t\t\t\t\t// })\r\n\t\t\t\t\t\tuni.hideLoading()\r\n\t\t\t\t\t\tsetTimeout(()=>{\r\n\t\t\t\t\t\t\t// 订单详情页面\r\n\t\t\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\t\t\turl: '/pages/order/orderDetail?orderNo='+that.orderNo\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t},1000)\r\n\t\t\t\t    }\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tgetCurrentDateTime() {\r\n\t\t\t\tconst now = new Date();\r\n\t\t\t\tconst year = now.getFullYear();\r\n\t\t\t\tconst month = String(now.getMonth() + 1).padStart(2, '0'); // 月份从0开始，需要加1\r\n\t\t\t\tconst day = String(now.getDate()).padStart(2, '0');\r\n\t\t\t\tconst hours = String(now.getHours()).padStart(2, '0');\r\n\t\t\t\tconst minutes = String(now.getMinutes()).padStart(2, '0');\r\n\t\t\t\tconst seconds = String(now.getSeconds()).padStart(2, '0');\r\n\t\t\t\tthis.dateTime = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;\r\n\t\t\t\treturn `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n<style>\r\n\tpage{\r\n\t\tbackground-color: #F5F5F5;\r\n\t}\r\n</style>\r\n<style lang=\"scss\" scoped>\r\n\t.page{\r\n\t\tpadding: 24rpx 24rpx 0 24rpx;\r\n\t}\r\n\t.address {\r\n\t\t// margin-top: 24rpx;\r\n\t\tpadding: 30rpx 24rpx;\r\n\t\tbackground-color: white;\r\n\t\tborder-radius: 10rpx;\r\n\t}\r\n\r\n\t.address_top {\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: space-between;\r\n\t\talign-items: center;\r\n\t}\r\n\r\n\t.product_info {\r\n\t\tmargin-top: 24rpx;\r\n\t\tdisplay: flex;\r\n\t\tpadding: 24rpx;\r\n\t\tbackground-color: #FFFFFF;\r\n\t\tborder-radius: 10rpx;\r\n\t\t.prodct_left {\r\n\t\t\tdisplay: flex;\r\n\t\t\tborder-radius: 10rpx;\r\n\t\t\toverflow: hidden;\r\n\t\t}\r\n\r\n\t\t.product_center {\r\n\t\t\tflex: 1;\r\n\t\t\tmargin-left: 20rpx;\r\n\t\t}\r\n\r\n\t\t.product_right {\r\n\t\t\tmargin-left: 32rpx;\r\n\t\t\t.font_small{\r\n\t\t\t\tfont-size: 36rpx;\r\n\t\t\t\tcolor: #171B25;\r\n\t\t\t\tfont-weight: 600;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\t.inviter {\r\n\t\tmargin-top: 24rpx;\r\n\t\tpadding: 24rpx 32rpx;\r\n\t\t// width: 100%;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: space-between;\r\n\t\tbackground-color: #fff;\r\n\t\tborder-radius: 10rpx;\r\n\t\t.uni-input{\r\n\t\t\tfont-size: 28rpx;\r\n\t\t\ttext-align: right;\r\n\t\t}\r\n\t}\r\n\t.pay_type{\r\n\t\tmargin-top: 24rpx;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\t// justify-content: space-between;\r\n\t\tpadding: 24rpx 32rpx;\r\n\t\tbackground-color: #fff;\r\n\t\tborder-radius: 10rpx;\r\n\t}\r\n\r\n\t.goods_detail_footer {\r\n\t\tposition: fixed;\r\n\t\tbottom: 0;\r\n\t\tleft: 0;\r\n\t\tright: 0;\r\n\t\twidth: 100%;\r\n\t\theight: 170rpx;\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: center;\r\n\t\tbackground-color: #FFFFFF;\r\n\t\tpadding-top: 6rpx;\r\n\t\t.btn {\r\n\t\t\theight: 84rpx;\r\n\t\t\twidth: 93%;\r\n\t\t\tborder-radius: 9999px;\r\n\t\t\tbackground-color: #BBA186;\r\n\t\t\tcolor: #FFFFFF;\r\n\t\t\tfont-size: 28rpx;\r\n\t\t\ttext-align: center;\r\n\t\t\tline-height: 84rpx;\r\n\t\t}\r\n\t}\r\n</style>", "import mod from \"-!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./payment.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./payment.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754188037847\n      var cssReload = require(\"D:/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  ", "import mod from \"-!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./payment.vue?vue&type=style&index=1&id=299f44de&lang=scss&scoped=true&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./payment.vue?vue&type=style&index=1&id=299f44de&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754188040021\n      var cssReload = require(\"D:/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}