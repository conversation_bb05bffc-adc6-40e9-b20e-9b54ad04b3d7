(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-indexChild-payment-payment"],{"1b04":function(t,e,i){var n=i("557a");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var o=i("4f06").default;o("be12a406",n,!0,{sourceMap:!1,shadowMode:!1})},"38cf":function(t,e,i){var n=i("23e7"),o=i("1148");n({target:"String",proto:!0},{repeat:o})},"3a2b":function(t,e,i){"use strict";i("7a82");var n=i("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var o=n(i("c7eb")),a=n(i("1da1"));i("e25e"),i("e9c4"),i("4d90"),i("99af");var r=n(i("c8ed")),s=i("30ea"),c=i("9e56"),d={data:function(){return{number:r.default,goodsId:"",orderNo:"",ordeForm:{addressId:0,goodsId:0,goodsNum:0,pickStore:"",reference:"",type:""},orderPayForm:{code:"",img:"",orderNo:"",payTyle:3},goodsInfo:{},storeInfo:{},yqrInfo:{},webCode:""}},onLoad:function(t){this.goodsId=t.goodsId,this.goodsNum=parseInt(t.goodsNum),this.getGoodsdetail(),this.checked=!0},mounted:function(){var t=window.location.href.split("?"),e=t[1].split("&");console.log(t),console.log(e),this.webCode=e[0].split("=")[1]},onShow:function(){var t=this;uni.getStorage({key:"store_info",success:function(e){t.storeInfo=e.data}})},methods:{getUser:function(t){var e=this;this.$nextTick((function(){e.yqrInfo=t}))},location:function(t){uni.navigateTo({url:t}),this.yqrInfo={}},onUser:function(t){this.storeInfo.id?uni.navigateTo({url:t+"?storeId="+this.storeInfo.id}):uni.showToast({title:"请选择门店",icon:"none"})},getGoodsdetail:function(){var t=this;return(0,a.default)((0,o.default)().mark((function e(){var i;return(0,o.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,c.request(s.goodsDetailsUrl+"?goodsId="+t.goodsId,{},"POST");case 2:i=e.sent,console.log(i),0!==i.code?uni.showToast({title:i.msg,icon:"none"}):t.goodsInfo=i.data.result;case 5:case"end":return e.stop()}}),e)})))()},settleOrder:function(t){var e=this;return(0,a.default)((0,o.default)().mark((function i(){var n,a,r,d,u;return(0,o.default)().wrap((function(i){while(1)switch(i.prev=i.next){case 0:return console.log(t),uni.showLoading({title:"支付中"}),n=e,i.next=5,c.request(s.settleOrderUrl,{goodsId:n.goodsId,goodsNum:1,type:2,pickStore:"",reference:"",yqr:t.detail.value.yqr,addressId:n.storeInfo.id},"POST");case 5:if(a=i.sent,console.log(a),0===a.code){i.next=11;break}uni.showToast({title:a.msg,icon:"none"}),i.next=16;break;case 11:return n.orderNo=a.data,i.next=14,c.request(s.orderPayUrl,{orderNo:a.data,payTyle:"7",code:n.webCode},"POST");case 14:r=i.sent,0!==r.code?uni.showToast({title:r.msg,icon:"none"}):(d=JSON.parse(r.data.jspay_info),u={appId:"wxa56aa346588ae42f",timeStamp:d.timeStamp,nonceStr:d.nonceStr,package:d.package,signType:d.signType,paySign:d.paySign},n.onBridgeReady(u));case 16:case"end":return i.stop()}}),i)})))()},onBridgeReady:function(t){var e=this;WeixinJSBridge.invoke("getBrandWCPayRequest",t,(function(t){"get_brand_wcpay_request:ok"==t.err_msg?(console.log("微信支付成功了！！！"),uni.showToast({title:"支付成功",icon:"success",duration:1500}),setTimeout((function(){uni.navigateTo({url:"/pages/order/orderDetail?orderNo="+e.orderNo})}),2e3)):(uni.hideLoading(),setTimeout((function(){uni.navigateTo({url:"/pages/order/orderDetail?orderNo="+e.orderNo})}),1e3))}))},paymentRequest:function(t){var e=this,i=JSON.parse(t.jspay_info);uni.requestPayment({timeStamp:i.timeStamp,nonceStr:i.nonceStr,package:i.package,signType:i.signType,paySign:i.paySign,success:function(t){uni.showToast({title:"支付成功",icon:"success",duration:1500}),setTimeout((function(){uni.navigateTo({url:"/pages/order/orderDetail?orderNo="+e.orderNo})}),1e3),console.log("success:"+JSON.stringify(t))},fail:function(t){uni.hideLoading(),setTimeout((function(){uni.navigateTo({url:"/pages/order/orderDetail?orderNo="+e.orderNo})}),1e3)}})},getCurrentDateTime:function(){var t=new Date,e=t.getFullYear(),i=String(t.getMonth()+1).padStart(2,"0"),n=String(t.getDate()).padStart(2,"0"),o=String(t.getHours()).padStart(2,"0"),a=String(t.getMinutes()).padStart(2,"0"),r=String(t.getSeconds()).padStart(2,"0");return this.dateTime="".concat(e,"-").concat(i,"-").concat(n," ").concat(o,":").concat(a,":").concat(r),"".concat(e,"-").concat(i,"-").concat(n," ").concat(o,":").concat(a,":").concat(r)}}};e.default=d},"557a":function(t,e,i){var n=i("24fb");e=n(!1),e.push([t.i,'@charset "UTF-8";\r\n/* uni.scss */\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 字体变量 */\r\n/* 颜色变量 */\r\n/* 行为相关颜色 */\r\n/* 文字基本颜色 */\r\n/* 背景颜色 */\r\n/* 边框颜色 */\r\n/* 尺寸变量 */\r\n/* 文字尺寸 */\r\n/* 图片尺寸 */\r\n/* Border Radius */\r\n/* 水平间距 */\r\n/* 垂直间距 */\r\n/* 透明度 */\r\n/* 文章场景相关 */.page[data-v-ffbc672c]{padding:%?24?% %?24?% 0 %?24?%}.address[data-v-ffbc672c]{padding:%?30?% %?24?%;background-color:#fff;border-radius:%?10?%}.address_top[data-v-ffbc672c]{display:flex;justify-content:space-between;align-items:center}.product_info[data-v-ffbc672c]{margin-top:%?24?%;display:flex;padding:%?24?%;background-color:#fff;border-radius:%?10?%}.product_info .prodct_left[data-v-ffbc672c]{display:flex;border-radius:%?10?%;overflow:hidden}.product_info .product_center[data-v-ffbc672c]{flex:1;margin-left:%?20?%}.product_info .product_right[data-v-ffbc672c]{margin-left:%?32?%}.product_info .product_right .font_small[data-v-ffbc672c]{font-size:%?36?%;color:#171b25;font-weight:600}.inviter[data-v-ffbc672c]{margin-top:%?24?%;padding:%?24?% %?32?%;display:flex;align-items:center;justify-content:space-between;background-color:#fff;border-radius:%?10?%}.inviter .uni-input[data-v-ffbc672c]{font-size:%?28?%;text-align:right}.pay_type[data-v-ffbc672c]{margin-top:%?24?%;display:flex;align-items:center;padding:%?24?% %?32?%;background-color:#fff;border-radius:%?10?%}.goods_detail_footer[data-v-ffbc672c]{position:fixed;bottom:0;left:0;right:0;width:100%;height:%?170?%;display:flex;justify-content:center;background-color:#fff;padding-top:%?6?%}.goods_detail_footer .btn[data-v-ffbc672c]{height:%?84?%;width:93%;border-radius:9999px;background-color:#bba186;color:#fff;font-size:%?28?%;text-align:center;line-height:%?84?%}',""]),t.exports=e},"7b5a":function(t,e,i){var n=i("24fb");e=n(!1),e.push([t.i,"uni-page-body[data-v-ffbc672c]{background-color:#f5f5f5}body.?%PAGE?%[data-v-ffbc672c]{background-color:#f5f5f5}",""]),t.exports=e},8821:function(t,e,i){var n=i("7b5a");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var o=i("4f06").default;o("e81c2aa4",n,!0,{sourceMap:!1,shadowMode:!1})},bea0:function(t,e,i){"use strict";i.d(e,"b",(function(){return n})),i.d(e,"c",(function(){return o})),i.d(e,"a",(function(){}));var n=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",{staticClass:"page"},[i("v-uni-form",{on:{submit:function(e){arguments[0]=e=t.$handleEvent(e),t.settleOrder.apply(void 0,arguments)}}},[i("v-uni-view",{staticClass:"address"},[t.storeInfo.id?i("v-uni-view",[i("v-uni-view",{staticStyle:{display:"flex","justify-content":"space-between","align-items":"center"}},[i("v-uni-view",{staticClass:"address_top"},[i("v-uni-view",{staticStyle:{display:"flex"}},[i("v-uni-image",{staticStyle:{width:"40rpx",height:"40rpx"},attrs:{src:"/static/img/shop/shop_store.png",mode:"widthFix"}})],1),i("v-uni-view",{staticStyle:{"margin-left":"10rpx","ffont-size":"32rpx","font-weight":"600"}},[t._v(t._s(t.storeInfo.name))])],1),i("v-uni-view",{staticStyle:{display:"flex","align-items":"center","font-size":"24rpx"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.location("/pages/promotion/store")}}},[t._v("切换门店"),i("v-uni-view",{staticClass:"right-icon"})],1)],1),i("v-uni-view",{staticStyle:{"margin-top":"20rpx",color:"#61687C","font-size":"24rpx"}},[t._v(t._s(t.storeInfo.address))])],1):i("v-uni-view",{staticClass:"address_right",staticStyle:{width:"100%",display:"flex","align-items":"center","justify-content":"space-between"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.location("/pages/promotion/store")}}},[i("v-uni-view",{staticStyle:{display:"flex","align-items":"center"}},[i("v-uni-image",{staticStyle:{width:"40rpx",height:"40rpx"},attrs:{src:"/static/img/shop/shop_store.png",mode:"widthFix"}}),i("v-uni-text",{staticStyle:{"margin-left":"10rpx"}},[t._v("请选择门店")])],1),i("v-uni-view",{staticClass:"right-icon"})],1)],1),i("v-uni-view",{staticClass:"product_info"},[i("v-uni-view",{staticClass:"prodct_left"},[i("v-uni-image",{staticStyle:{width:"164rpx",height:"164rpx"},attrs:{src:t.goodsInfo.goodsImg}})],1),i("v-uni-view",{staticClass:"product_center"},[i("v-uni-view",{staticClass:"text-ellipsis_2",staticStyle:{"font-size":"28rpx","line-height":"44rpx"}},[t._v(t._s(t.goodsInfo.goodsName))]),i("v-uni-view",{staticStyle:{display:"flex","margin-top":"20rpx"}},[i("v-uni-view",{staticStyle:{color:"#61687C","font-size":"24rpx","background-color":"#F2F4F7","text-align":"center",padding:"6rpx 20rpx","border-radius":"6rpx"}},[t._v(t._s(t.goodsInfo.specification)+"g")]),i("v-uni-view")],1)],1),i("v-uni-view",{staticClass:"product_right"},[i("v-uni-view",[i("v-uni-text",{staticClass:"font_small"},[t._v("¥"+t._s(t.goodsInfo.price))])],1),i("v-uni-view",{staticStyle:{"font-size":"24rpx",color:"#9FA3B0","margin-top":"8rpx","text-align":"right"}},[t._v("x1")])],1)],1),i("v-uni-view",{staticClass:"inviter",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.onUser("/pages/indexChild/userList/userList")}}},[i("v-uni-view",[t._v("邀请人")]),i("v-uni-view",{staticStyle:{display:"flex","align-items":"center"}},[i("v-uni-view",{staticStyle:{display:"flex"}},[i("v-uni-input",{staticClass:"uni-input",attrs:{name:"yqr",value:t.yqrInfo.name,disabled:"true",readOnly:"true",placeholder:"请选择邀请人"}})],1),i("v-uni-view",{staticClass:"right-icon",staticStyle:{"margin-left":"20rpx"}})],1)],1),i("v-uni-view",{staticClass:"pay_type"},[i("v-uni-view",[i("v-uni-view",{staticStyle:{display:"flex"}},[i("v-uni-image",{staticStyle:{width:"56rpx",height:"56rpx"},attrs:{src:"/static/img/shop/shop_wx.png",alt:""}})],1)],1),i("v-uni-view",{staticStyle:{display:"flex","justify-content":"space-between","align-items":"center",flex:"1","margin-left":"24rpx"}},[i("v-uni-view",{staticStyle:{"font-size":"28rpx"}},[t._v("微信")]),i("v-uni-view",[i("v-uni-image",{staticStyle:{width:"48rpx",height:"48rpx"},attrs:{src:"/static/img/shop/shop_round_a.png",alt:""}})],1)],1)],1),i("v-uni-view",{staticClass:"goods_detail_footer"},[i("v-uni-button",{staticClass:"btn",attrs:{"form-type":"submit"}},[t._v("确认支付")])],1)],1)],1)},o=[]},c8ed:function(t,e,i){"use strict";i("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("d401"),i("d3b7"),i("25f0"),i("c975"),i("fb6a"),i("38cf"),i("99af");var n={parsePrice:function(t){if(t||0===t){var e=t.toString(),i=e.indexOf(".");return-1!==i&&e.length-i===3?e:-1!==i?e.length-i===2?e+"0":e.slice(0,i+3):e+".00"}return""},hideMiddleDigits:function(t){if(!t||"string"!==typeof t)return"";var e=t.slice(0,3)+"*".repeat(4),i=t.slice(7);return"".concat(e).concat(i)},oneparsePrice:function(t){if(null!=t&&""!==t){var e=t.toString(),i=e.indexOf(".");if(-1!==i){var n=e.length-i-1;return 1===n?e:n>1?e.slice(0,i+2):e+"0"}return e+".0"}return""}};e.default=n},e886:function(t,e,i){"use strict";var n=i("8821"),o=i.n(n);o.a},e9c9:function(t,e,i){"use strict";i.r(e);var n=i("3a2b"),o=i.n(n);for(var a in n)["default"].indexOf(a)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(a);e["default"]=o.a},ed8f:function(t,e,i){"use strict";i.r(e);var n=i("bea0"),o=i("e9c9");for(var a in o)["default"].indexOf(a)<0&&function(t){i.d(e,t,(function(){return o[t]}))}(a);i("e886"),i("f707");var r=i("f0c5"),s=Object(r["a"])(o["default"],n["b"],n["c"],!1,null,"ffbc672c",null,!1,n["a"],void 0);e["default"]=s.exports},f707:function(t,e,i){"use strict";var n=i("1b04"),o=i.n(n);o.a}}]);