{"version": 3, "sources": ["uni-app:///main.js", null, "webpack:///E:/pos/黄金/gold_client/App.vue?62b1", "uni-app:///App.vue", "webpack:///E:/pos/黄金/gold_client/App.vue?f7ec", "webpack:///E:/pos/黄金/gold_client/App.vue?53d2", "webpack:///E:/pos/黄金/gold_client/App.vue?4fec", "webpack:///E:/pos/黄金/gold_client/App.vue?0205"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "<PERSON><PERSON>", "use", "uView", "config", "productionTip", "App", "mpType", "app", "$mount", "onLaunch", "onShow", "setTimeout", "onHide"], "mappings": ";;;;;;;;;;;;;;AAAA;AAE2D;AAG3D;AACA;AAGA;AAEA;AAA6B;AAAA;AAV7B;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAc1DC,YAAG,CAACC,GAAG,CAACC,gBAAK,CAAC;AAEdF,YAAG,CAACG,MAAM,CAACC,aAAa,GAAG,KAAK;AAChCC,YAAG,CAACC,MAAM,GAAG,KAAK;AAClB,IAAMC,GAAG,GAAG,IAAIP,YAAG,mBACdK,YAAG,EACN;AACF,UAAAE,GAAG,EAACC,MAAM,EAAE,C;;;;;;;;;;;;;ACvBZ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACuD;AACL;AACc;AACD;;;AAG/D;AACgL;AAChL,gBAAgB,yLAAU;AAC1B,EAAE,yEAAM;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACxBf;AAAA;AAAA;AAAA;AAAopB,CAAgB,yqBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eC6BxqB;EACAC;IACA;IACA;MACA;MACA;MACA;MACA;IAAA,CAeA;MACA;IAAA;EAOA;EACAC;IACAC,wBAIA;IACA;EACA;;EACAC;IACA;EAAA;AAEA;AAAA,2B;;;;;;;;;;;;;ACvEA;AAAA;AAAA;AAAA;AAA2uC,CAAgB,wsCAAG,EAAC,C;;;;;;;;;;;ACA/vC;AACA,OAAO,KAAU,EAAE,kBAKd;;;;;;;;;;;;;ACNL;AAAA;AAAA;AAAA;AAA47B,CAAgB,s7BAAG,EAAC,C;;;;;;;;;;;ACAh9B;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "common/main.js", "sourcesContent": ["import 'uni-pages';\r\n// @ts-ignore\r\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import App from './App'\r\n\r\n\r\nimport Vue from 'vue'\r\nimport './uni.promisify.adaptor'\r\n\r\n// 先引入自定义主题\r\nimport './static/style/theme.scss'\r\n// 再引入uView\r\nimport uView from \"uview-ui\";\r\n\r\n\r\n\r\n\r\nVue.use(uView);\r\n\r\nVue.config.productionTip = false\r\nApp.mpType = 'app'\r\nconst app = new Vue({\r\n  ...App\r\n})\r\napp.$mount()", "var render, staticRenderFns, recyclableRender, components\nvar renderjs\nimport script from \"./App.vue?vue&type=script&lang=js&\"\nexport * from \"./App.vue?vue&type=script&lang=js&\"\nimport style0 from \"./App.vue?vue&type=style&index=0&lang=scss&\"\nimport style1 from \"./App.vue?vue&type=style&index=1&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"App.vue\"\nexport default component.exports", "import mod from \"-!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./App.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./App.vue?vue&type=script&lang=js&\"", "<style lang=\"scss\">\r\n\t/* 注意要写在第一行，同时给style标签加入lang=\"scss\"属性 */\r\n\t@import \"uview-ui/index.scss\";\r\n\t.right-icon{\r\n\t\twidth: 14rpx;\r\n\t\theight: 14rpx;\r\n\t\tborder-top: 1px solid #666666;\r\n\t\tborder-right: 1px solid #666666;\r\n\t\ttransform: rotate(45deg);\r\n\t}\r\n\t.text-ellipsis_2{\r\n\t   overflow : hidden;\r\n\t   text-overflow: ellipsis;\r\n\t   display: -webkit-box;\r\n\t   -webkit-line-clamp: 2;\r\n\t   -webkit-box-orient: vertical;\r\n\t   word-break: break-all;\r\n\t}\r\n\t/* #ifdef WEB */\r\n\t.page_bottom {\r\n\t  height: 55px; /*暂时发现安卓小屏手机安全区为0 && 不带单位的话padding-bottom会失效，写一个兜底, 并写在最上面；f12发现css样式如果都给height设置样式，会依次往下，用最底下的一个样式，如果最下面的没失效，依次往上查找，所以兜底的写在最上面 */\r\n\t  height: calc(55px + constant(safe-area-inset-bottom)); /* 直接扩展高度，因为padding-bottom是内边距 */\r\n\t  height: calc(55px + env(safe-area-inset-bottom)); /* 直接扩展高度 */\r\n\t  padding-bottom: constant(safe-area-inset-bottom); /*兼容 iOS<11.2 */\r\n\t  padding-bottom: env(safe-area-inset-bottom); /* 兼容iOS>= 11.2*/\r\n\t}\r\n\t/* #endif */\r\n</style>\r\n<script>\r\n\texport default {\r\n\tonLaunch() {\r\n\t    const token = uni.getStorageSync('token');  \r\n\t    if (!token) {  \r\n\t        // 如果没有token，则跳转到登录页面  \r\n\t        // 注意：uni.reLaunch() 会关闭所有非 tabBar 页面  \r\n\t        // 如果你的登录页面是tabBar页面，可能需要使用uni.navigateTo()或其他方法  \r\n\t        // console.log('没有token，跳转到登录页面');  \r\n\t          //#ifdef APP-PLUS  \r\n\t        uni.reLaunch({  \r\n\t            url: \"pages/register/register\",  \r\n\t            success: () => {  \r\n\t                // 跳转完页面后再关闭启动页（仅在App平台有效）  \r\n\t                plus.navigator.closeSplashscreen();  \r\n\t            }  \r\n\t        });  \r\n\t          //#else  \r\n\t        // 在非App平台，直接使用uni.navigateTo或uni.redirectTo跳转到登录页面  \r\n\t        uni.navigateTo({  \r\n\t            url: \"pages/register/register\"  \r\n\t        });  \r\n\t          //#endif  \r\n\t    } else {  \r\n\t        // 如果有token，则关闭启动页（仅在App平台有效）  \r\n\t         // #ifdef APP-PLUS  \r\n\t        plus.navigator.closeSplashscreen();  \r\n\t        // 这里可以添加其他逻辑，比如跳转到首页或检查商户信息等  \r\n\t        // 例如：this.getmerDetail(); 但注意，this在onLaunch中可能不是你期望的上下文  \r\n\t          //#endif  \r\n\t    }  \r\n\t},\r\n\tonShow: function() {\r\n\t\tsetTimeout(() => {\r\n\t\t\t// #ifdef APP-PLUS \r\n\t\t\tplus.navigator.closeSplashscreen();\r\n\t\t\t// #endif\r\n\t\t}, 2000)\r\n\t\t// console.log('App Show')\r\n\t},\r\n\t\tonHide: function() {\r\n\t\t\t// console.log('App Hide')\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style>\r\n\t/*每个页面公共css */\r\n\tpage{\r\n\t\theight: 100%;\r\n\t\tfont-family: \"黑体\", SimHei, \"Heiti SC\", \"Heiti TC\", sans-serif;\r\n\t}\r\n\t\r\n\t/* 全局应用黑体字体 */\r\n\tuni-page-body,\r\n\tuni-page-refresh,\r\n\tuni-app,\r\n\tuni-page,\r\n\tview,\r\n\tscroll-view,\r\n\tswiper,\r\n\tswiper-item,\r\n\tcover-view,\r\n\tcover-image,\r\n\ttext,\r\n\trich-text,\r\n\tinput,\r\n\ttextarea,\r\n\tbutton,\r\n\tlabel,\r\n\tnavigator,\r\n\timage {\r\n\t\tfont-family: \"黑体\", SimHei, \"Heiti SC\", \"Heiti TC\", sans-serif;\r\n\t}\r\n</style>\r\n", "import mod from \"-!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./App.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./App.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754188041170\n      var cssReload = require(\"D:/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  ", "import mod from \"-!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./App.vue?vue&type=style&index=1&lang=css&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./App.vue?vue&type=style&index=1&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754188037861\n      var cssReload = require(\"D:/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}