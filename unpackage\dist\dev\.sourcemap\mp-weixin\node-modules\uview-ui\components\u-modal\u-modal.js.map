{"version": 3, "sources": ["webpack:///E:/pos/黄金/gold_client/node_modules/uview-ui/components/u-modal/u-modal.vue?f25e", "webpack:///E:/pos/黄金/gold_client/node_modules/uview-ui/components/u-modal/u-modal.vue?4a00", "webpack:///E:/pos/黄金/gold_client/node_modules/uview-ui/components/u-modal/u-modal.vue?2ccd", "webpack:///E:/pos/黄金/gold_client/node_modules/uview-ui/components/u-modal/u-modal.vue?8bbc", "uni-app:///node_modules/uview-ui/components/u-modal/u-modal.vue", "webpack:///E:/pos/黄金/gold_client/node_modules/uview-ui/components/u-modal/u-modal.vue?318f", "webpack:///E:/pos/黄金/gold_client/node_modules/uview-ui/components/u-modal/u-modal.vue?524b"], "names": ["name", "props", "value", "type", "default", "zIndex", "title", "width", "content", "showTitle", "showConfirmButton", "showCancelButton", "confirmText", "cancelText", "confirmColor", "cancelColor", "borderRadius", "titleStyle", "contentStyle", "cancelStyle", "confirmStyle", "zoom", "asyncClose", "maskCloseAble", "negativeTop", "data", "loading", "computed", "cancelBtnStyle", "color", "confirmBtnStyle", "uZIndex", "watch", "methods", "confirm", "cancel", "setTimeout", "popupClose", "clearLoading"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAgI;AAChI;AAC2D;AACL;AACsC;;;AAG5F;AACgL;AAChL,gBAAgB,yLAAU;AAC1B,EAAE,6EAAM;AACR,EAAE,8FAAM;AACR,EAAE,uGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,kGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,qOAEN;AACP,KAAK;AACL;AACA,aAAa,iPAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACnEA;AAAA;AAAA;AAAA;AAAwpB,CAAgB,6qBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACkC5qB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AA5BA,gBA6BA;EACAA;EACAC;IACA;IACAC;MACAC;MACAC;IACA;IACA;IACAC;MACAF;MACAC;IACA;IACA;IACAE;MACAH;MACAC;IACA;IACA;IACAG;MACAJ;MACAC;IACA;IACA;IACAI;MACAL;MACAC;IACA;IACA;IACAK;MACAN;MACAC;IACA;IACA;IACAM;MACAP;MACAC;IACA;IACA;IACAO;MACAR;MACAC;IACA;IACA;IACAQ;MACAT;MACAC;IACA;IACA;IACAS;MACAV;MACAC;IACA;IACA;IACAU;MACAX;MACAC;IACA;IACA;IACAW;MACAZ;MACAC;IACA;IACA;IACAY;MACAb;MACAC;IACA;IACA;IACAa;MACAd;MACAC;QACA;MACA;IACA;IACA;IACAc;MACAf;MACAC;QACA;MACA;IACA;IACA;IACAe;MACAhB;MACAC;QACA;MACA;IACA;IACA;IACAgB;MACAjB;MACAC;QACA;MACA;IACA;IACA;IACAiB;MACAlB;MACAC;IACA;IACA;IACAkB;MACAnB;MACAC;IACA;IACA;IACAmB;MACApB;MACAC;IACA;IACA;IACAoB;MACArB;MACAC;IACA;EACA;EACAqB;IACA;MACAC;IACA;EACA;;EACAC;IACAC;MACA;QACAC;MACA;IACA;IACAC;MACA;QACAD;MACA;IACA;IACAE;MACA;IACA;EACA;EACAC;IACA;IACA;IACA9B;MACA;IACA;EACA;EACA+B;IACAC;MACA;MACA;QACA;MACA;QACA;MACA;MACA;IACA;IACAC;MAAA;MACA;MACA;MACA;MACA;MACAC;QACA;MACA;IACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;IACA;EACA;AACA;AAAA,4B;;;;;;;;;;;;AC3OA;AAAA;AAAA;AAAA;AAAuwC,CAAgB,ouCAAG,EAAC,C;;;;;;;;;;;ACA3xC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "node-modules/uview-ui/components/u-modal/u-modal.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./u-modal.vue?vue&type=template&id=713d0fd3&scoped=true&\"\nvar renderjs\nimport script from \"./u-modal.vue?vue&type=script&lang=js&\"\nexport * from \"./u-modal.vue?vue&type=script&lang=js&\"\nimport style0 from \"./u-modal.vue?vue&type=style&index=0&id=713d0fd3&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"713d0fd3\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"node_modules/uview-ui/components/u-modal/u-modal.vue\"\nexport default component.exports", "export * from \"-!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-modal.vue?vue&type=template&id=713d0fd3&scoped=true&\"", "var components\ntry {\n  components = {\n    uPopup: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-popup/u-popup\" */ \"uview-ui/components/u-popup/u-popup.vue\"\n      )\n    },\n    uLoading: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-loading/u-loading\" */ \"uview-ui/components/u-loading/u-loading.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var s0 = _vm.showTitle ? _vm.__get_style([_vm.titleStyle]) : null\n  var s1 =\n    _vm.$slots.default || _vm.$slots.$default\n      ? _vm.__get_style([_vm.contentStyle])\n      : null\n  var s2 = !(_vm.$slots.default || _vm.$slots.$default)\n    ? _vm.__get_style([_vm.contentStyle])\n    : null\n  var s3 =\n    (_vm.showCancelButton || _vm.showConfirmButton) && _vm.showCancelButton\n      ? _vm.__get_style([_vm.cancelBtnStyle])\n      : null\n  var s4 =\n    (_vm.showCancelButton || _vm.showConfirmButton) &&\n    (_vm.showConfirmButton || _vm.$slots[\"confirm-button\"])\n      ? _vm.__get_style([_vm.confirmBtnStyle])\n      : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        s0: s0,\n        s1: s1,\n        s2: s2,\n        s3: s3,\n        s4: s4,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-modal.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-modal.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view>\r\n\t\t<u-popup :zoom=\"zoom\" mode=\"center\" :popup=\"false\" :z-index=\"uZIndex\" v-model=\"value\" :length=\"width\"\r\n\t\t :mask-close-able=\"maskCloseAble\" :border-radius=\"borderRadius\" @close=\"popupClose\" :negative-top=\"negativeTop\">\r\n\t\t\t<view class=\"u-model\">\r\n\t\t\t\t<view v-if=\"showTitle\" class=\"u-model__title u-line-1\" :style=\"[titleStyle]\">{{ title }}</view>\r\n\t\t\t\t<view class=\"u-model__content\">\r\n\t\t\t\t\t<view :style=\"[contentStyle]\" v-if=\"$slots.default  || $slots.$default\">\r\n\t\t\t\t\t\t<slot />\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view v-else class=\"u-model__content__message\" :style=\"[contentStyle]\">{{ content }}</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"u-model__footer u-border-top\" v-if=\"showCancelButton || showConfirmButton\">\r\n\t\t\t\t\t<view v-if=\"showCancelButton\" :hover-stay-time=\"100\" hover-class=\"u-model__btn--hover\" class=\"u-model__footer__button\"\r\n\t\t\t\t\t :style=\"[cancelBtnStyle]\" @tap=\"cancel\">\r\n\t\t\t\t\t\t{{cancelText}}\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view v-if=\"showConfirmButton || $slots['confirm-button']\" :hover-stay-time=\"100\" :hover-class=\"asyncClose ? 'none' : 'u-model__btn--hover'\"\r\n\t\t\t\t\t class=\"u-model__footer__button hairline-left\" :style=\"[confirmBtnStyle]\" @tap=\"confirm\">\r\n\t\t\t\t\t\t<slot v-if=\"$slots['confirm-button']\" name=\"confirm-button\"></slot>\r\n\t\t\t\t\t\t<block v-else>\r\n\t\t\t\t\t\t\t<u-loading mode=\"circle\" :color=\"confirmColor\" v-if=\"loading\"></u-loading>\r\n\t\t\t\t\t\t\t<block v-else>\r\n\t\t\t\t\t\t\t\t{{confirmText}}\r\n\t\t\t\t\t\t\t</block>\r\n\t\t\t\t\t\t</block>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</u-popup>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\t/**\r\n\t * modal 模态框\r\n\t * @description 弹出模态框，常用于消息提示、消息确认、在当前页面内完成特定的交互操作\r\n\t * @tutorial https://www.uviewui.com/components/modal.html\r\n\t * @property {Boolean} value 是否显示模态框\r\n\t * @property {String | Number} z-index 层级\r\n\t * @property {String} title 模态框标题（默认\"提示\"）\r\n\t * @property {String | Number} width 模态框宽度（默认600）\r\n\t * @property {String} content 模态框内容（默认\"内容\"）\r\n\t * @property {Boolean} show-title 是否显示标题（默认true）\r\n\t * @property {Boolean} async-close 是否异步关闭，只对确定按钮有效（默认false）\r\n\t * @property {Boolean} show-confirm-button 是否显示确认按钮（默认true）\r\n\t * @property {String | Number} negative-top modal往上偏移的值\r\n\t * @property {Boolean} show-cancel-button 是否显示取消按钮（默认false）\r\n\t * @property {Boolean} mask-close-able 是否允许点击遮罩关闭modal（默认false）\r\n\t * @property {String} confirm-text 确认按钮的文字内容（默认\"确认\"）\r\n\t * @property {String} cancel-text 取消按钮的文字内容（默认\"取消\"）\r\n\t * @property {String} cancel-color 取消按钮的颜色（默认\"#606266\"）\r\n\t * @property {String} confirm-color 确认按钮的文字内容（默认\"#2979ff\"）\r\n\t * @property {String | Number} border-radius 模态框圆角值，单位rpx（默认16）\r\n\t * @property {Object} title-style 自定义标题样式，对象形式\r\n\t * @property {Object} content-style 自定义内容样式，对象形式\r\n\t * @property {Object} cancel-style 自定义取消按钮样式，对象形式\r\n\t * @property {Object} confirm-style 自定义确认按钮样式，对象形式\r\n\t * @property {Boolean} zoom 是否开启缩放模式（默认true）\r\n\t * @event {Function} confirm 确认按钮被点击\r\n\t * @event {Function} cancel 取消按钮被点击\r\n\t * @example <u-modal :src=\"title\" :content=\"content\"></u-modal>\r\n\t */\r\n\texport default {\r\n\t\tname: 'u-modal',\r\n\t\tprops: {\r\n\t\t\t// 是否显示Modal\r\n\t\t\tvalue: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: false\r\n\t\t\t},\r\n\t\t\t// 层级z-index\r\n\t\t\tzIndex: {\r\n\t\t\t\ttype: [Number, String],\r\n\t\t\t\tdefault: ''\r\n\t\t\t},\r\n\t\t\t// 标题\r\n\t\t\ttitle: {\r\n\t\t\t\ttype: [String],\r\n\t\t\t\tdefault: '提示'\r\n\t\t\t},\r\n\t\t\t// 弹窗宽度，可以是数值(rpx)，百分比，auto等\r\n\t\t\twidth: {\r\n\t\t\t\ttype: [Number, String],\r\n\t\t\t\tdefault: 600\r\n\t\t\t},\r\n\t\t\t// 弹窗内容\r\n\t\t\tcontent: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: '内容'\r\n\t\t\t},\r\n\t\t\t// 是否显示标题\r\n\t\t\tshowTitle: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: true\r\n\t\t\t},\r\n\t\t\t// 是否显示确认按钮\r\n\t\t\tshowConfirmButton: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: true\r\n\t\t\t},\r\n\t\t\t// 是否显示取消按钮\r\n\t\t\tshowCancelButton: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: false\r\n\t\t\t},\r\n\t\t\t// 确认文案\r\n\t\t\tconfirmText: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: '确认'\r\n\t\t\t},\r\n\t\t\t// 取消文案\r\n\t\t\tcancelText: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: '取消'\r\n\t\t\t},\r\n\t\t\t// 确认按钮颜色\r\n\t\t\tconfirmColor: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: '#2979ff'\r\n\t\t\t},\r\n\t\t\t// 取消文字颜色\r\n\t\t\tcancelColor: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: '#606266'\r\n\t\t\t},\r\n\t\t\t// 圆角值\r\n\t\t\tborderRadius: {\r\n\t\t\t\ttype: [Number, String],\r\n\t\t\t\tdefault: 16\r\n\t\t\t},\r\n\t\t\t// 标题的样式\r\n\t\t\ttitleStyle: {\r\n\t\t\t\ttype: Object,\r\n\t\t\t\tdefault () {\r\n\t\t\t\t\treturn {}\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t// 内容的样式\r\n\t\t\tcontentStyle: {\r\n\t\t\t\ttype: Object,\r\n\t\t\t\tdefault () {\r\n\t\t\t\t\treturn {}\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t// 取消按钮的样式\r\n\t\t\tcancelStyle: {\r\n\t\t\t\ttype: Object,\r\n\t\t\t\tdefault () {\r\n\t\t\t\t\treturn {}\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t// 确定按钮的样式\r\n\t\t\tconfirmStyle: {\r\n\t\t\t\ttype: Object,\r\n\t\t\t\tdefault () {\r\n\t\t\t\t\treturn {}\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t// 是否开启缩放效果\r\n\t\t\tzoom: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: true\r\n\t\t\t},\r\n\t\t\t// 是否异步关闭，只对确定按钮有效\r\n\t\t\tasyncClose: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: false\r\n\t\t\t},\r\n\t\t\t// 是否允许点击遮罩关闭modal\r\n\t\t\tmaskCloseAble: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: false\r\n\t\t\t},\r\n\t\t\t// 给一个负的margin-top，往上偏移，避免和键盘重合的情况\r\n\t\t\tnegativeTop: {\r\n\t\t\t\ttype: [String, Number],\r\n\t\t\t\tdefault: 0\r\n\t\t\t}\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tloading: false, // 确认按钮是否正在加载中\r\n\t\t\t}\r\n\t\t},\r\n\t\tcomputed: {\r\n\t\t\tcancelBtnStyle() {\r\n\t\t\t\treturn Object.assign({\r\n\t\t\t\t\tcolor: this.cancelColor\r\n\t\t\t\t}, this.cancelStyle);\r\n\t\t\t},\r\n\t\t\tconfirmBtnStyle() {\r\n\t\t\t\treturn Object.assign({\r\n\t\t\t\t\tcolor: this.confirmColor\r\n\t\t\t\t}, this.confirmStyle);\r\n\t\t\t},\r\n\t\t\tuZIndex() {\r\n\t\t\t\treturn this.zIndex ? this.zIndex : this.$u.zIndex.popup;\r\n\t\t\t}\r\n\t\t},\r\n\t\twatch: {\r\n\t\t\t// 如果是异步关闭时，外部修改v-model的值为false时，重置内部的loading状态\r\n\t\t\t// 避免下次打开的时候，状态混乱\r\n\t\t\tvalue(n) {\r\n\t\t\t\tif (n === true) this.loading = false;\r\n\t\t\t}\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tconfirm() {\r\n\t\t\t\t// 异步关闭\r\n\t\t\t\tif (this.asyncClose) {\r\n\t\t\t\t\tthis.loading = true;\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.$emit('input', false);\r\n\t\t\t\t}\r\n\t\t\t\tthis.$emit('confirm');\r\n\t\t\t},\r\n\t\t\tcancel() {\r\n\t\t\t\tthis.$emit('cancel');\r\n\t\t\t\tthis.$emit('input', false);\r\n\t\t\t\t// 目前popup弹窗关闭有一个延时操作，此处做一个延时\r\n\t\t\t\t// 避免确认按钮文字变成了\"确定\"字样，modal还没消失，造成视觉不好的效果\r\n\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\tthis.loading = false;\r\n\t\t\t\t}, 300);\r\n\t\t\t},\r\n\t\t\t// 点击遮罩关闭modal，设置v-model的值为false，否则无法第二次弹起modal\r\n\t\t\tpopupClose() {\r\n\t\t\t\tthis.$emit('input', false);\r\n\t\t\t},\r\n\t\t\t// 清除加载中的状态\r\n\t\t\tclearLoading() {\r\n\t\t\t\tthis.loading = false;\r\n\t\t\t}\r\n\t\t}\r\n\t};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n\t@import \"../../libs/css/style.components.scss\";\r\n\r\n\t.u-model {\r\n\t\theight: auto;\r\n\t\toverflow: hidden;\r\n\t\tfont-size: 32rpx;\r\n\t\tbackground-color: #fff;\r\n\r\n\t\t&__btn--hover {\r\n\t\t\tbackground-color: rgb(230, 230, 230);\r\n\t\t}\r\n\r\n\t\t&__title {\r\n\t\t\tpadding-top: 48rpx;\r\n\t\t\tfont-weight: 500;\r\n\t\t\ttext-align: center;\r\n\t\t\tcolor: $u-main-color;\r\n\t\t}\r\n\r\n\t\t&__content {\r\n\t\t\t&__message {\r\n\t\t\t\tpadding: 48rpx;\r\n\t\t\t\tfont-size: 30rpx;\r\n\t\t\t\ttext-align: center;\r\n\t\t\t\tcolor: $u-content-color;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t&__footer {\r\n\t\t\t@include vue-flex;\r\n\r\n\t\t\t&__button {\r\n\t\t\t\tflex: 1;\r\n\t\t\t\theight: 100rpx;\r\n\t\t\t\tline-height: 100rpx;\r\n\t\t\t\tfont-size: 32rpx;\r\n\t\t\t\tbox-sizing: border-box;\r\n\t\t\t\tcursor: pointer;\r\n\t\t\t\ttext-align: center;\r\n\t\t\t\tborder-radius: 4rpx;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</style>\r\n", "import mod from \"-!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-modal.vue?vue&type=style&index=0&id=713d0fd3&lang=scss&scoped=true&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-modal.vue?vue&type=style&index=0&id=713d0fd3&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754273099964\n      var cssReload = require(\"D:/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}