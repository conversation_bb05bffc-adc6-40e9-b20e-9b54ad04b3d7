{"version": 3, "sources": ["webpack:///E:/Home/ma-Yi/gold/uni_modules/Sansnn-uQRCode/components/uqrcode/uqrcode.vue?1ba3", "webpack:///E:/Home/ma-Yi/gold/uni_modules/Sansnn-uQRCode/components/uqrcode/uqrcode.vue?8b68", "webpack:///E:/Home/ma-Yi/gold/uni_modules/Sansnn-uQRCode/components/uqrcode/uqrcode.vue?07d2", "webpack:///E:/Home/ma-Yi/gold/uni_modules/Sansnn-uQRCode/components/uqrcode/uqrcode.vue?cf78", "uni-app:///uni_modules/Sansnn-uQRCode/components/uqrcode/uqrcode.vue", "webpack:///E:/Home/ma-Yi/gold/uni_modules/Sansnn-uQRCode/components/uqrcode/uqrcode.vue?b7b7", "webpack:///E:/Home/ma-Yi/gold/uni_modules/Sansnn-uQRCode/components/uqrcode/uqrcode.vue?4a86"], "names": ["name", "props", "canvasId", "type", "required", "value", "options", "default", "size", "sizeUnit", "fileType", "start", "auto", "hide", "queue", "isQueueLoadImage", "loading", "h5SaveIsDownload", "h5DownloadName", "data", "canvas", "canvasType", "canvasContext", "makeDelegate", "drawDelegate", "toTempFilePathDelegate", "makeExecuted", "makeing", "drawing", "isError", "error", "isH5Save", "tempFile<PERSON>ath", "templateOptions", "width", "height", "canvasWidth", "canvasHeight", "canvasTransform", "canvasDisplay", "uqrcodeOptions", "plugins", "makeingPattern", "watch", "handler", "immediate", "deep", "mounted", "methods", "getTemplateOptions", "getUqrcodeOptions", "resetCanvas", "callback", "draw", "isDrawDelegate", "console", "errMsg", "qr", "uni", "createSelectorQuery", "in", "select", "fields", "node", "exec", "resolve", "dpr", "img", "reject", "src", "success", "fail", "setTimeout", "draw<PERSON><PERSON>vas", "then", "delegate", "catch", "finally", "make", "clearTimeout", "complete", "remake", "toTempFile<PERSON>ath", "dataURL", "destWidth", "destHeight", "save", "Math", "toString", "split", "fs", "filePath", "encoding", "onClick", "getInstance", "registerStyle", "plugin", "getLoadImage", "loadImage", "cacheImageList", "obj", "o"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAgI;AAChI;AAC2D;AACL;AACqC;;;AAG3F;AACqM;AACrM,gBAAgB,8MAAU;AAC1B,EAAE,6EAAM;AACR,EAAE,8FAAM;AACR,EAAE,uGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,kGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACfA;AAAA;AAAA;AAAA;AAAiwB,CAAgB,sxBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;AC6DrxB;AAYA;AAKA;AAEA;AAAA;AAEA;AAAA,gBAEA;EACAA;EACAC;IACA;AACA;AACA;IACAC;MACAC;MACAC;IACA;;IACA;AACA;AACA;IACAC;MACAF;IACA;IACA;AACA;AACA;IACAG;MACAH;MACAI;QACA;MACA;IACA;IACA;AACA;AACA;IACAC;MACAL;MACAI;IACA;IACA;AACA;AACA;IACAE;MACAN;MACAI;IACA;IACA;AACA;AACA;IACAG;MACAP;MACAI;IACA;IACA;AACA;AACA;IACAI;MACAR;MACAI;IACA;IACA;AACA;AACA;IACAK;MACAT;MACAI;IACA;IACA;AACA;AACA;IACAM;MACAV;MACAI;IACA;IACA;AACA;AACA;AACA;IACAJ;MACAA;MACAI;QAEA;MAKA;IACA;IACA;AACA;AACA;IACAO;MACAX;MACAI;IACA;IACA;AACA;AACA;IACAQ;MACAZ;MACAI;IACA;IACA;AACA;AACA;IACAS;MACAb;MACAI;IACA;IACA;AACA;AACA;IACAU;MACAd;MACAI;IACA;IACA;AACA;AACA;IACAW;MACAf;MACAI;IACA;EACA;EACAY;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;QACAzB;QACA0B;QAAA;QACAC;QACAC;QAAA;QACAC;QACAC;QACAC;MACA;MACAC;QACArB;MACA;MACAsB;MACAC,iBACA,CACA,kEACA,kEACA,kEACA,kEACA,kEACA,kEACA,kEACA,8DACA,8DACA,6DACA,EACA,CACA,8DACA,8DACA,8DACA,kEACA,kEACA,kEACA,qEACA,+DACA,+DACA,8DACA,EACA,CACA,8DACA,8DACA,8DACA,kEACA,kEACA,iEACA,iEACA,iEACA,kEACA,iEACA,EACA,CACA,8DACA,8DACA,8DACA,qEACA,qEACA,qEACA,qEACA,8DACA,8DACA,6DACA;IAEA;EACA;EACAC;IACAxC;MACAyC;QACA;QACA;UACA;QACA;UACA;QACA;MACA;MACAC;IACA;IACAxC;MACAuC;QACA;UACA;QACA;MACA;IACA;IACApC;MACAoC;QACA;UACA;QACA;MACA;IACA;IACAtC;MACAsC;QACA;UACA;QACA;MACA;MACAE;IACA;IACAnB;MACAiB;QACA;UACA;YACA;UACA;QACA;MACA;IACA;EACA;EACAG;IACA;IACA;IACA;IACA;IACA;IACA,8BAKA;MACA,8JACA;IACA;IACA;MACA;IACA;EACA;EACAC;IACA;AACA;AACA;IACAC;MACA;MACA;QACAzC;QACA0B;QACAC;MACA;IACA;IACA;AACA;AACA;IACAe;MACA;QACA/B;QACAX;MACA;IACA;IACA;AACA;AACA;IACA2C;MAAA;MACA;MACA;QACA;QACA;UACAC;QACA;MACA;IACA;IACA;AACA;AACA;IACAC;MAAA;QAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAAD;gBAAAE;gBACA;kBACAF;gBACA;gBACA;kBACAA;gBACA;gBACA;kBACAA;gBACA;gBAAA,KAEA;kBAAA;kBAAA;gBAAA;gBAAA,IACAE;kBAAA;kBAAA;gBAAA;gBACA;kBACA;gBACA;gBAAA;cAAA;gBAAA;gBAAA;cAAA;gBAIA;cAAA;gBAAA,IAGA;kBAAA;kBAAA;gBAAA;gBACAC;gBACA;gBACA;gBACAH;kBACAI;gBACA;gBACAJ;kBACAI;gBACA;gBAAA;cAAA;gBAAA,IAGA;kBAAA;kBAAA;gBAAA;gBACAD;gBACA;gBACA;gBACAH;kBACAI;gBACA;gBACAJ;kBACAI;gBACA;gBAAA;cAAA;gBAIA;gBACA;gBACA;gBACA;gBACA;gBACA;kBACA;gBACA;gBACA;;gBAEA;kBACA;gBACA;;gBAWA;gBACAC;gBACA;gBACA;kBAAA;gBAAA;gBACA;gBACAA;gBACA;gBACAA;;gBAEA;gBACAnC;gBAAA,MAEA;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OAGA;kBACAoC,IACAC,sBACAC;kBAAA,CACAC,oCACAC;oBACAC;oBACAvD;kBACA,GACAwD;oBACAC;kBACA;gBACA;cAAA;gBAZA7C;gBAaAE;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA4C;gBACA9C;gBACAA;gBACAE;gBACA;gBACAmC;kBACA;kBACA;oBACA;oBACAU;oBACAA;sBACAF;oBACA;oBACAE;sBACAC;oBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAmCA;gBACA9C;gBACA;gBACA;gBACA;gBACA,sKACA;gBACA;gBACAmC;kBACA;oBACA;oBACA;sBACAC;wBACAW;wBACAC;0BACAL;wBACA;wBACAM;0BACAH;wBACA;sBACA;oBACA;sBACA;wBACAb;wBACA;sBACA;wBACAU;sBACA;oBACA;kBACA;gBACA;cAAA;gBAgCA;gBACAR;gBACA;gBACAe;kBACA;kBACA;oBAAA;kBAAA;kBACA;kBACA;kBACA;kBACA;oBACAC;sBAAA;wBAAA;sBAAA;oBAAA;oBACA;oBACA;oBACA;oBACA;oBACA;kBACA;oBACAA;sBAAA;oBAAA;kBACA;kBACA;kBACAA,aACAC;oBACA;sBACA;sBACA;sBACA;sBACAC;oBACA;sBACA;sBACAvB;oBACA;kBACA,GACAwB;oBACArB;oBACA;sBACA;sBACA;sBACA;sBACAoB;oBACA;sBACA;sBACA;sBACAvB;oBACA;kBACA,GACAyB;oBACAzB;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACA;AACA;AACA;IACA0B;MAAA;MAAA;MACA;MACA;MACA;MAEA;QACA1B;MACA;MACA;QACAA;MACA;MACA;QACAA;MACA;MAEA;QACA2B;QACA;UACA;YACAT;cACAE;gBACApB;gBACA;cACA;YACA;YACAmB;cACAnB;cACA;cACA;YACA;YACA4B;cACA5B;cACA;YACA;UACA;QACA;MACA;IACA;IACA;AACA;AACA;IACA6B;MACA;MACA;IACA;IACA;AACA;AACA;IACAD;MAAA;MAAA;MACA;QACA;UACAV;QACA;MACA;QACA;UACAA;UACAd;QACA;MACA;IACA;IACA;AACA;AACA;IACA0B;MAAA;MAAA;MACA;QACA9B;MACA;MACA;QACAA;MACA;MACA;QACAA;MACA;MAEA;QACAG;QACA;UACAC;QACA;QACAJ;QACAA;QACA;MACA;MAEA;QACAA;QACAA;QACA;MACA;MAEA;QACA;QACA;UACA;QACA;QACA;MACA;QACA;MACA;MAGA;QAEA;UACA;UAMA+B;UAEA/B;YACApB;UACA;UACAoB;YACApB;UACA;QACA;UACAoB;UACAA;QACA;MAEA;QACAM;UACAxD;UACAQ;UACAwB;UACAC;UACAiD;UACAC;UACAf;YACAlB;UACA;UACAmB;YACAnB;UACA;UACA4B;YACA5B;UACA;QACA,GACA,KACA;MACA;IAmBA;IACA;AACA;AACA;IACAkC;MAAA;MAAA;MACA;QACAlC;MACA;MACA;QACAA;MACA;MACA;QACAA;MACA;MAEA;QACAkB;UAEA;YAEA;YACA;YACA;YACA;YACA,6FACAiB,cACAC,WACAC,sBACA;YACAC;cACAC;cAAA;cACAxE;cAAA;cACAyE;cACAtB;gBACAZ;kBACAiC;kBACArB;oBACAlB;kBACA;kBACAmB;oBACAnB;kBACA;kBACA4B;oBACA5B;kBACA;gBACA;cACA;cACAmB;gBACAnB;cACA;cACA4B;gBACA5B;cACA;YACA;UAEA;YACAM;cACAiC;cACArB;gBACAlB;cACA;cACAmB;gBACAnB;cACA;cACA4B;gBACA5B;cACA;YACA;UACA;QAsBA;QACAmB;UACAnB;UACAA;QACA;MACA;IACA;IACA;AACA;AACA;IACAyC;MACA;IACA;IACA;AACA;AACA;IACAC;MACA;IACA;IACA;AACA;AACA;AACA;IACAC;MACA;QACAxC;QACA;UACAC;QACA;MACA;MACA;QACA;UACAwC;UACAhG;UACAyE;QACA;MACA;IACA;IACAwB;MACA;MACA;QACA;UACA;UACA;YACA;YACA;cACA;gBACAzB;kBACA;oBAAA;kBAAA;kBACA;oBACAP;kBACA;oBACAiC,eACAxB;sBACAyB;wBACA9B;wBACAF;sBACA;sBACAF;oBACA,GACAW;sBACAR;oBACA;kBACA;gBACA;cACA;YACA;UACA;YACA;UACA;QACA;MACA;QACA;UACA;QACA;MACA;IACA;EACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AALA;AAMA;EAAA;EAAA;EAAA;EACA;EACA;IACA;IACAgC;EACA;IACA;IACAA,wBACAC,EACA;EACA;EACA;IACA;IACA;MACA;QACAD;MACA;QACAA;MACA;QACAA;MACA;IACA;EACA;EACA;AACA,C;;;;;;;;;;;;;ACt/BA;AAAA;AAAA;AAAA;AAA2mC,CAAgB,qmCAAG,EAAC,C;;;;;;;;;;;ACA/nC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "uni_modules/Sansnn-uQRCode/components/uqrcode/uqrcode.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./uqrcode.vue?vue&type=template&id=b7042062&scoped=true&\"\nvar renderjs\nimport script from \"./uqrcode.vue?vue&type=script&lang=js&\"\nexport * from \"./uqrcode.vue?vue&type=script&lang=js&\"\nimport style0 from \"./uqrcode.vue?vue&type=style&index=0&id=b7042062&scoped=true&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"b7042062\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"uni_modules/Sansnn-uQRCode/components/uqrcode/uqrcode.vue\"\nexport default component.exports", "export * from \"-!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./uqrcode.vue?vue&type=template&id=b7042062&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  _vm.$initSSP()\n  if (_vm.$scope.data.scopedSlotsCompiler === \"augmented\") {\n    _vm.$setSSP(\"error\", {\n      error: _vm.error,\n    })\n  }\n  _vm.$callSSP()\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./uqrcode.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./uqrcode.vue?vue&type=script&lang=js&\"", "<template>\r\n  <view class=\"uqrcode\" :class=\"{ 'uqrcode-hide': hide }\" :style=\"{ width: `${templateOptions.width}px`, height: `${templateOptions.height}px` }\">\r\n    <view class=\"uqrcode-canvas-wrapper\">\r\n      <!-- 画布 -->\r\n      <!-- #ifndef APP-NVUE -->\r\n      <canvas class=\"uqrcode-canvas\" :id=\"canvasId\" :canvas-id=\"canvasId\" :type=\"canvasType\" :style=\"{\r\n          width: `${templateOptions.canvasWidth}px`,\r\n          height: `${templateOptions.canvasHeight}px`,\r\n          transform: templateOptions.canvasTransform\r\n        }\" v-if=\"templateOptions.canvasDisplay\" @click=\"onClick\"></canvas>\r\n      <!-- #endif -->\r\n\r\n      <!-- nvue用gcanvas -->\r\n      <!-- #ifdef APP-NVUE -->\r\n      <gcanvas class=\"uqrcode-canvas\" ref=\"gcanvas\" :style=\"{\r\n          width: `${templateOptions.canvasWidth}px`,\r\n          height: `${templateOptions.canvasHeight}px`\r\n        }\" v-if=\"templateOptions.canvasDisplay\" @click=\"onClick\"></gcanvas>\r\n      <!-- #endif -->\r\n    </view>\r\n\r\n    <!-- 加载效果 -->\r\n    <view class=\"uqrcode-makeing\" v-if=\"loading === undefined ? makeing : loading\">\r\n      <slot name=\"loading\">\r\n        <image class=\"uqrcode-makeing-image\" :style=\"{ width: `${templateOptions.size / 4}px`, height: `${templateOptions.size / 4}px` }\"\r\n          src=\"data:image/gif;base64,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\">\r\n        </image>\r\n      </slot>\r\n    </view>\r\n\r\n    <!-- 错误处理 -->\r\n    <view class=\"uqrcode-error\" v-if=\"isError\" @click=\"onClick\">\r\n      <slot name=\"error\" :error=\"error\">\r\n        <text class=\"uqrcode-error-message\">{{ error.errMsg }}</text>\r\n      </slot>\r\n    </view>\r\n\r\n    <!-- H5保存提示 -->\r\n    <!-- #ifdef H5 -->\r\n    <view class=\"uqrcode-h5-save\" v-if=\"isH5Save\">\r\n      <slot name=\"h5save\" :tempFilePath=\"tempFilePath\">\r\n        <image class=\"uqrcode-h5-save-image\" :src=\"tempFilePath\"></image>\r\n        <text class=\"uqrcode-h5-save-text\">{{ h5SaveIsDownload ? '若保存失败，' : '' }}请长按二维码进行保存</text>\r\n      </slot>\r\n      <view class=\"uqrcode-h5-save-close\" @click.stop=\"isH5Save = false\">\r\n        <view class=\"uqrcode-h5-save-close-before\"></view>\r\n        <view class=\"uqrcode-h5-save-close-after\"></view>\r\n      </view>\r\n    </view>\r\n    <!-- #endif -->\r\n  </view>\r\n</template>\r\n\r\n<script>\r\n  // #ifdef VUE3\r\n  import {\r\n    toRaw\r\n  } from 'vue';\r\n  // #endif\r\n\r\n  /* 引入uQRCode核心js */\r\n  import UQRCode from '../../js_sdk/uqrcode/uqrcode';\r\n\r\n  /* 引入nvue所需模块 */\r\n  // #ifdef APP-NVUE\r\n  import {\r\n    enable,\r\n    WeexBridge\r\n  } from '../../js_sdk/gcanvas';\r\n  const modal = weex.requireModule('modal');\r\n  // #endif\r\n\r\n  /* 引入队列 */\r\n  import {\r\n    queueDraw,\r\n    queueLoadImage\r\n  } from '../../common/queue';\r\n  /* 引入缓存图片 */\r\n  import {\r\n    cacheImageList\r\n  } from '../../common/cache';\r\n\r\n  let instance = null;\r\n\r\n  export default {\r\n    name: 'uqrcode',\r\n    props: {\r\n      /**\r\n       * canvas组件id\r\n       */\r\n      canvasId: {\r\n        type: String,\r\n        required: true // canvasId在微信小程序初始值不能为空，created中赋值也不行，必须给一个值，否则挂载组件后无法绘制。不考虑用随机id，uuid\r\n      },\r\n      /**\r\n       * 二维码内容\r\n       */\r\n      value: {\r\n        type: [String, Number]\r\n      },\r\n      /**\r\n       * 选项\r\n       */\r\n      options: {\r\n        type: Object,\r\n        default: () => {\r\n          return {};\r\n        }\r\n      },\r\n      /**\r\n       * 二维码大小\r\n       */\r\n      size: {\r\n        type: [String, Number],\r\n        default: 200\r\n      },\r\n      /**\r\n       * 二维码尺寸单位\r\n       */\r\n      sizeUnit: {\r\n        type: String,\r\n        default: 'px'\r\n      },\r\n      /**\r\n       * 导出的文件类型\r\n       */\r\n      fileType: {\r\n        type: String,\r\n        default: 'png'\r\n      },\r\n      /**\r\n       * 是否初始化组件后就开始生成\r\n       */\r\n      start: {\r\n        type: Boolean,\r\n        default: true\r\n      },\r\n      /**\r\n       * 是否数据发生改变自动重绘\r\n       */\r\n      auto: {\r\n        type: Boolean,\r\n        default: true\r\n      },\r\n      /**\r\n       * 隐藏组件\r\n       */\r\n      hide: {\r\n        type: Boolean,\r\n        default: false\r\n      },\r\n      /**\r\n       * canvas 类型，微信小程序默认使用2d，非2d微信官方已放弃维护，问题比较多\r\n       * 注意：微信小程序type2d手机上正常，PC上微信内打开小程序toDataURL报错，看后期微信官方团队会不会做兼容，不兼容的话只能在自行判断在PC使用非2d，或者直接提示用户请在手机上操作，微信团队的海报中心小程序就是这么做的\r\n       */\r\n      type: {\r\n        type: String,\r\n        default: () => {\r\n          // #ifdef MP-WEIXIN\r\n          return '2d';\r\n          // #endif\r\n          // #ifndef MP-WEIXIN\r\n          return 'normal';\r\n          // #endif\r\n        }\r\n      },\r\n      /**\r\n       * 队列绘制，主要针对NVue端\r\n       */\r\n      queue: {\r\n        type: Boolean,\r\n        default: false\r\n      },\r\n      /**\r\n       * 是否队列加载图片，可减少canvas发起的网络资源请求，节省服务器资源\r\n       */\r\n      isQueueLoadImage: {\r\n        type: Boolean,\r\n        default: false\r\n      },\r\n      /**\r\n       * loading态\r\n       */\r\n      loading: {\r\n        type: Boolean,\r\n        default: undefined\r\n      },\r\n      /**\r\n       * H5保存即自动下载（在支持的环境下），默认false为仅弹层提示用户需要长按图片保存，不会自动下载\r\n       */\r\n      h5SaveIsDownload: {\r\n        type: Boolean,\r\n        default: false\r\n      },\r\n      /**\r\n       * H5下载名称\r\n       */\r\n      h5DownloadName: {\r\n        type: String,\r\n        default: 'uQRCode'\r\n      }\r\n    },\r\n    data() {\r\n      return {\r\n        canvas: undefined,\r\n        canvasType: undefined,\r\n        canvasContext: undefined,\r\n        makeDelegate: undefined,\r\n        drawDelegate: undefined,\r\n        toTempFilePathDelegate: undefined,\r\n        makeExecuted: false,\r\n        makeing: false,\r\n        drawing: false,\r\n        isError: false,\r\n        error: undefined,\r\n        isH5Save: false,\r\n        tempFilePath: '',\r\n        templateOptions: {\r\n          size: 0,\r\n          width: 0, // 组件宽度\r\n          height: 0,\r\n          canvasWidth: 0, // canvas宽度\r\n          canvasHeight: 0,\r\n          canvasTransform: '',\r\n          canvasDisplay: false\r\n        },\r\n        uqrcodeOptions: {\r\n          data: ''\r\n        },\r\n        plugins: [],\r\n        makeingPattern: [\r\n          [\r\n            [true, true, true, false, false, false, false, true, true, true],\r\n            [true, true, true, false, false, false, false, true, true, true],\r\n            [true, true, true, false, false, false, false, true, true, true],\r\n            [true, true, true, false, false, false, false, true, true, true],\r\n            [true, true, true, false, false, false, false, true, true, true],\r\n            [true, true, true, false, false, false, false, true, true, true],\r\n            [true, true, true, false, false, false, false, true, true, true],\r\n            [true, true, true, true, true, true, true, true, true, true],\r\n            [true, true, true, true, true, true, true, true, true, true],\r\n            [true, true, true, true, true, true, true, true, true, true]\r\n          ],\r\n          [\r\n            [true, true, true, true, true, true, true, true, true, true],\r\n            [true, true, true, true, true, true, true, true, true, true],\r\n            [true, true, true, true, true, true, true, true, true, true],\r\n            [true, true, true, false, false, false, false, true, true, true],\r\n            [true, true, true, false, false, false, false, true, true, true],\r\n            [true, true, true, false, false, false, false, true, true, true],\r\n            [true, true, true, false, false, false, false, false, false, false],\r\n            [true, true, true, true, true, true, false, true, true, true],\r\n            [true, true, true, true, true, true, false, true, true, true],\r\n            [true, true, true, true, true, true, false, true, true, true]\r\n          ],\r\n          [\r\n            [true, true, true, true, true, true, true, true, true, true],\r\n            [true, true, true, true, true, true, true, true, true, true],\r\n            [true, true, true, true, true, true, true, true, true, true],\r\n            [true, true, true, false, false, false, false, true, true, true],\r\n            [true, true, true, false, false, false, false, true, true, true],\r\n            [true, true, true, true, true, true, true, false, false, false],\r\n            [true, true, true, true, true, true, true, false, false, false],\r\n            [true, true, true, true, true, true, true, false, false, false],\r\n            [true, true, true, false, false, false, false, true, true, true],\r\n            [true, true, true, false, false, false, false, true, true, true]\r\n          ],\r\n          [\r\n            [true, true, true, true, true, true, true, true, true, true],\r\n            [true, true, true, true, true, true, true, true, true, true],\r\n            [true, true, true, true, true, true, true, true, true, true],\r\n            [true, true, true, false, false, false, false, false, false, false],\r\n            [true, true, true, false, false, false, false, false, false, false],\r\n            [true, true, true, false, false, false, false, false, false, false],\r\n            [true, true, true, false, false, false, false, false, false, false],\r\n            [true, true, true, true, true, true, true, true, true, true],\r\n            [true, true, true, true, true, true, true, true, true, true],\r\n            [true, true, true, true, true, true, true, true, true, true]\r\n          ]\r\n        ]\r\n      };\r\n    },\r\n    watch: {\r\n      type: {\r\n        handler(val) {\r\n          const types = ['2d'];\r\n          if (types.includes(val)) {\r\n            this.canvasType = val;\r\n          } else {\r\n            this.canvasType = undefined;\r\n          }\r\n        },\r\n        immediate: true\r\n      },\r\n      value: {\r\n        handler() {\r\n          if (this.auto) {\r\n            this.remake();\r\n          }\r\n        }\r\n      },\r\n      size: {\r\n        handler() {\r\n          if (this.auto) {\r\n            this.remake();\r\n          }\r\n        }\r\n      },\r\n      options: {\r\n        handler() {\r\n          if (this.auto) {\r\n            this.remake();\r\n          }\r\n        },\r\n        deep: true\r\n      },\r\n      makeing: {\r\n        handler(val) {\r\n          if (!val) {\r\n            if (typeof this.toTempFilePathDelegate === 'function') {\r\n              this.toTempFilePathDelegate();\r\n            }\r\n          }\r\n        }\r\n      }\r\n    },\r\n    mounted() {\r\n      this.templateOptions.size = this.sizeUnit == 'rpx' ? uni.upx2px(this.size) : this.size;\r\n      this.templateOptions.width = this.templateOptions.size;\r\n      this.templateOptions.height = this.templateOptions.size;\r\n      this.templateOptions.canvasWidth = this.templateOptions.size;\r\n      this.templateOptions.canvasHeight = this.templateOptions.size;\r\n      if (this.canvasType == '2d') {\r\n        // #ifndef MP-WEIXIN\r\n        this.templateOptions.canvasTransform = `scale(${this.templateOptions.size / this.templateOptions.canvasWidth}, ${this.templateOptions.size /\r\n        this.templateOptions.canvasHeight})`;\r\n        // #endif\r\n      } else {\r\n        this.templateOptions.canvasTransform = `scale(${this.templateOptions.size / this.templateOptions.canvasWidth}, ${this.templateOptions.size /\r\n        this.templateOptions.canvasHeight})`;\r\n      }\r\n      if (this.start) {\r\n        this.make();\r\n      }\r\n    },\r\n    methods: {\r\n      /**\r\n       * 获取模板选项\r\n       */\r\n      getTemplateOptions() {\r\n        var size = this.sizeUnit == 'rpx' ? uni.upx2px(this.size) : this.size;\r\n        return deepReplace(this.templateOptions, {\r\n          size,\r\n          width: size,\r\n          height: size\r\n        });\r\n      },\r\n      /**\r\n       * 获取插件选项\r\n       */\r\n      getUqrcodeOptions() {\r\n        return deepReplace(this.options, {\r\n          data: String(this.value),\r\n          size: Number(this.templateOptions.size)\r\n        });\r\n      },\r\n      /**\r\n       * 重置画布\r\n       */\r\n      resetCanvas(callback) {\r\n        this.templateOptions.canvasDisplay = false;\r\n        this.$nextTick(() => {\r\n          this.templateOptions.canvasDisplay = true;\r\n          this.$nextTick(() => {\r\n            callback && callback();\r\n          });\r\n        });\r\n      },\r\n      /**\r\n       * 绘制二维码\r\n       */\r\n      async draw(callback = {}, isDrawDelegate = false) {\r\n        if (typeof callback.success != 'function') {\r\n          callback.success = () => {};\r\n        }\r\n        if (typeof callback.fail != 'function') {\r\n          callback.fail = () => {};\r\n        }\r\n        if (typeof callback.complete != 'function') {\r\n          callback.complete = () => {};\r\n        }\r\n\r\n        if (this.drawing) {\r\n          if (!isDrawDelegate) {\r\n            this.drawDelegate = () => {\r\n              this.draw(callback, true);\r\n            };\r\n            return;\r\n          }\r\n        } else {\r\n          this.drawing = true;\r\n        }\r\n\r\n        if (!this.canvasId) {\r\n          console.error('[uQRCode]: canvasId must be set!');\r\n          this.isError = true;\r\n          this.drawing = false;\r\n          callback.fail({\r\n            errMsg: '[uQRCode]: canvasId must be set!'\r\n          });\r\n          callback.complete({\r\n            errMsg: '[uQRCode]: canvasId must be set!'\r\n          });\r\n          return;\r\n        }\r\n        if (!this.value) {\r\n          console.error('[uQRCode]: value must be set!');\r\n          this.isError = true;\r\n          this.drawing = false;\r\n          callback.fail({\r\n            errMsg: '[uQRCode]: value must be set!'\r\n          });\r\n          callback.complete({\r\n            errMsg: '[uQRCode]: value must be set!'\r\n          });\r\n          return;\r\n        }\r\n\r\n        /* 组件数据 */\r\n        this.templateOptions = this.getTemplateOptions();\r\n        /* uQRCode选项 */\r\n        this.uqrcodeOptions = this.getUqrcodeOptions();\r\n        /* 纠错等级兼容字母写法 */\r\n        if (typeof this.uqrcodeOptions.errorCorrectLevel === 'string') {\r\n          this.uqrcodeOptions.errorCorrectLevel = UQRCode.errorCorrectLevel[this.uqrcodeOptions.errorCorrectLevel];\r\n        }\r\n        /* nvue不支持动态修改gcanvas尺寸，除nvue外，默认使用useDynamicSize */\r\n        // #ifndef APP-NVUE\r\n        if (typeof this.options.useDynamicSize === 'undefined') {\r\n          this.uqrcodeOptions.useDynamicSize = true;\r\n        }\r\n        // #endif\r\n        // #ifdef APP-NVUE\r\n        if (typeof this.options.useDynamicSize === 'undefined') {\r\n          this.uqrcodeOptions.useDynamicSize = false;\r\n        }\r\n        // if (typeof this.options.drawReserve === 'undefined') {\r\n        //   this.uqrcodeOptions.drawReserve = true;\r\n        // }\r\n        // #endif\r\n\r\n        /* 获取uQRCode实例 */\r\n        const qr = instance = new UQRCode();\r\n        /* 注册扩展 */\r\n        this.plugins.forEach(p => qr.register(p.plugin));\r\n        /* 设置uQRCode选项 */\r\n        qr.setOptions(this.uqrcodeOptions);\r\n        /* 调用制作二维码方法 */\r\n        qr.make();\r\n\r\n        /* 获取canvas上下文 */\r\n        let canvasContext = null;\r\n        // #ifndef APP-NVUE\r\n        if (this.canvasType === '2d') {\r\n          // #ifdef MP-WEIXIN\r\n          /* 微信小程序获取canvas2d上下文方式 */\r\n          const canvas = (this.canvas = await new Promise(resolve => {\r\n            uni\r\n              .createSelectorQuery()\r\n              .in(this) // 在组件内使用需要\r\n              .select(`#${this.canvasId}`)\r\n              .fields({\r\n                node: true,\r\n                size: true\r\n              })\r\n              .exec(res => {\r\n                resolve(res[0].node);\r\n              });\r\n          }));\r\n          canvasContext = this.canvasContext = canvas.getContext('2d');\r\n          /* 2d的组件设置宽高与实际canvas绘制宽高不是一个，打个比方，组件size=200，canvas.width设置为100，那么绘制出来就是100=200，组件size=400，canvas.width设置为800，绘制大小还是800=400，所以无需理会下方返回的dynamicSize是多少，按dpr重新赋值给canvas即可 */\r\n          this.templateOptions.canvasWidth = qr.size;\r\n          this.templateOptions.canvasHeight = qr.size;\r\n          this.templateOptions.canvasTransform = '';\r\n          /* 使用dynamicSize+scale，可以解决小块间出现白线问题，dpr可以解决模糊问题 */\r\n          const dpr = uni.getSystemInfoSync().pixelRatio;\r\n          canvas.width = qr.dynamicSize * dpr;\r\n          canvas.height = qr.dynamicSize * dpr;\r\n          canvasContext.scale(dpr, dpr);\r\n          /* 微信小程序获取图像方式 */\r\n          qr.loadImage = this.getLoadImage(function(src) {\r\n            /* 小程序下获取网络图片信息需先配置download域名白名单才能生效 */\r\n            return new Promise((resolve, reject) => {\r\n              const img = canvas.createImage();\r\n              img.src = src;\r\n              img.onload = () => {\r\n                resolve(img);\r\n              };\r\n              img.onerror = err => {\r\n                reject(err);\r\n              };\r\n            });\r\n          });\r\n          // #endif\r\n          // #ifndef MP-WEIXIN\r\n          /* 非微信小程序不支持2d，切换回uniapp获取canvas上下文方式 */\r\n          canvasContext = this.canvasContext = uni.createCanvasContext(this.canvasId, this);\r\n          /* 使用dynamicSize，可以解决小块间出现白线问题，再通过scale缩放至size，使其达到所设尺寸 */\r\n          this.templateOptions.canvasWidth = qr.dynamicSize;\r\n          this.templateOptions.canvasHeight = qr.dynamicSize;\r\n          this.templateOptions.canvasTransform = `scale(${this.templateOptions.size / this.templateOptions.canvasWidth}, ${this.templateOptions.size /\r\n          this.templateOptions.canvasHeight})`;\r\n          /* uniapp获取图像方式 */\r\n          qr.loadImage = this.getLoadImage(function(src) {\r\n            return new Promise((resolve, reject) => {\r\n              if (src.startsWith('http')) {\r\n                uni.getImageInfo({\r\n                  src,\r\n                  success: res => {\r\n                    resolve(res.path);\r\n                  },\r\n                  fail: err => {\r\n                    reject(err);\r\n                  }\r\n                });\r\n              } else {\r\n                if (src.startsWith('.')) {\r\n                  console.error('[uQRCode]: 本地图片路径仅支持绝对路径！');\r\n                  throw new Error('[uQRCode]: local image path only supports absolute path!');\r\n                } else {\r\n                  resolve(src);\r\n                }\r\n              }\r\n            });\r\n          });\r\n          // #endif\r\n        } else {\r\n          /* uniapp获取canvas上下文方式 */\r\n          canvasContext = this.canvasContext = uni.createCanvasContext(this.canvasId, this);\r\n          /* 使用dynamicSize，可以解决小块间出现白线问题，再通过scale缩放至size，使其达到所设尺寸 */\r\n          this.templateOptions.canvasWidth = qr.dynamicSize;\r\n          this.templateOptions.canvasHeight = qr.dynamicSize;\r\n          this.templateOptions.canvasTransform = `scale(${this.templateOptions.size / this.templateOptions.canvasWidth}, ${this.templateOptions.size /\r\n          this.templateOptions.canvasHeight})`;\r\n          /* uniapp获取图像方式 */\r\n          qr.loadImage = this.getLoadImage(function(src) {\r\n            return new Promise((resolve, reject) => {\r\n              /* getImageInfo在微信小程序的bug：本地路径返回路径会把开头的/或../移除，导致路径错误，解决方法：限制只能使用绝对路径 */\r\n              if (src.startsWith('http')) {\r\n                uni.getImageInfo({\r\n                  src,\r\n                  success: res => {\r\n                    resolve(res.path);\r\n                  },\r\n                  fail: err => {\r\n                    reject(err);\r\n                  }\r\n                });\r\n              } else {\r\n                if (src.startsWith('.')) {\r\n                  console.error('[uQRCode]: 本地图片路径仅支持绝对路径！');\r\n                  throw new Error('[uQRCode]: local image path only supports absolute path!');\r\n                } else {\r\n                  resolve(src);\r\n                }\r\n              }\r\n            });\r\n          });\r\n        }\r\n        // #endif\r\n        // #ifdef APP-NVUE\r\n        /* NVue获取canvas上下文方式 */\r\n        const gcanvas = this.$refs['gcanvas'];\r\n        const canvas = enable(gcanvas, {\r\n          bridge: WeexBridge\r\n        });\r\n        canvasContext = this.canvasContext = canvas.getContext('2d');\r\n        /* NVue获取图像方式 */\r\n        qr.loadImage = this.getLoadImage(function(src) {\r\n          return new Promise((resolve, reject) => {\r\n            /* getImageInfo在nvue的bug：获取同一个路径的图片信息，同一时间第一次获取成功，后续失败，猜测是写入本地时产生文件写入冲突，所以没有返回，特别是对于网络资源 --- 已实现队列绘制，已解决此问题 */\r\n            if (src.startsWith('.')) {\r\n              console.error('[uQRCode]: 本地图片路径仅支持绝对路径！');\r\n              throw new Error('[uQRCode]: local image path only supports absolute path!');\r\n            } else {\r\n              uni.getImageInfo({\r\n                src,\r\n                success: res => {\r\n                  resolve(res.path);\r\n                },\r\n                fail: err => {\r\n                  reject(err);\r\n                }\r\n              });\r\n            }\r\n          });\r\n        });\r\n        // #endif\r\n\r\n        /* 设置uQRCode实例的canvas上下文 */\r\n        qr.canvasContext = canvasContext;\r\n        /* 延时等待页面重新绘制完毕 */\r\n        setTimeout(() => {\r\n          /* 从插件获取具体要调用哪一个扩展函数 */\r\n          var plugin = this.plugins.find(p => p.name == qr.style);\r\n          var drawCanvasName = plugin ? plugin.drawCanvas : 'drawCanvas';\r\n          /* 虽然qr[drawCanvasName]是直接返回Promise的，但由于js内部this指向问题，故不能直接exec(qr[drawCanvasName])此方式执行，需要改成exec(() => qr[drawCanvasName]())才能正确获取this */\r\n          var drawCanvas;\r\n          if (this.queue) {\r\n            drawCanvas = () => queueDraw.exec(() => qr[drawCanvasName]());\r\n            // drawCanvas = () => queueDraw.exec(() => new Promise((resolve, reject) => {\r\n            //   setTimeout(() => {\r\n            //     qr[drawCanvasName]().then(resolve).catch(reject);\r\n            //   }, 1000);\r\n            // }));\r\n          } else {\r\n            drawCanvas = () => qr[drawCanvasName]();\r\n          }\r\n          /* 调用绘制方法将二维码图案绘制到canvas上 */\r\n          drawCanvas()\r\n            .then(() => {\r\n              if (this.drawDelegate) {\r\n                /* 高频重绘纠正 */\r\n                let delegate = this.drawDelegate;\r\n                this.drawDelegate = undefined;\r\n                delegate();\r\n              } else {\r\n                this.drawing = false;\r\n                callback.success();\r\n              }\r\n            })\r\n            .catch(err => {\r\n              console.log(err);\r\n              if (this.drawDelegate) {\r\n                /* 高频重绘纠正 */\r\n                let delegate = this.drawDelegate;\r\n                this.drawDelegate = undefined;\r\n                delegate();\r\n              } else {\r\n                this.drawing = false;\r\n                this.isError = true;\r\n                callback.fail(err);\r\n              }\r\n            })\r\n            .finally(() => {\r\n              callback.complete();\r\n            });\r\n        }, 300);\r\n      },\r\n      /**\r\n       * 生成二维码\r\n       */\r\n      make(callback = {}) {\r\n        this.makeExecuted = true;\r\n        this.makeing = true;\r\n        this.isError = false;\r\n\r\n        if (typeof callback.success != 'function') {\r\n          callback.success = () => {};\r\n        }\r\n        if (typeof callback.fail != 'function') {\r\n          callback.fail = () => {};\r\n        }\r\n        if (typeof callback.complete != 'function') {\r\n          callback.complete = () => {};\r\n        }\r\n\r\n        this.resetCanvas(() => {\r\n          clearTimeout(this.makeDelegate);\r\n          this.makeDelegate = setTimeout(() => {\r\n            this.draw({\r\n              success: () => {\r\n                setTimeout(() => {\r\n                  callback.success();\r\n                  this.complete(true);\r\n                }, 300);\r\n              },\r\n              fail: err => {\r\n                callback.fail(err);\r\n                this.error = err;\r\n                this.complete(false, err.errMsg);\r\n              },\r\n              complete: () => {\r\n                callback.complete();\r\n                this.makeing = false;\r\n              }\r\n            });\r\n          }, 300);\r\n        });\r\n      },\r\n      /**\r\n       * 重新生成\r\n       */\r\n      remake(callback) {\r\n        this.$emit('change');\r\n        this.make(callback);\r\n      },\r\n      /**\r\n       * 生成完成\r\n       */\r\n      complete(success = true, errMsg = '') {\r\n        if (success) {\r\n          this.$emit('complete', {\r\n            success\r\n          });\r\n        } else {\r\n          this.$emit('complete', {\r\n            success,\r\n            errMsg\r\n          });\r\n        }\r\n      },\r\n      /**\r\n       * 导出临时路径\r\n       */\r\n      toTempFilePath(callback = {}) {\r\n        if (typeof callback.success != 'function') {\r\n          callback.success = () => {};\r\n        }\r\n        if (typeof callback.fail != 'function') {\r\n          callback.fail = () => {};\r\n        }\r\n        if (typeof callback.complete != 'function') {\r\n          callback.complete = () => {};\r\n        }\r\n\r\n        if (!this.makeExecuted) {\r\n          console.error('[uQRCode]: make() 方法从未调用！请先成功调用 make() 后再进行操作。');\r\n          var err = {\r\n            errMsg: '[uQRCode]: make() method has never been executed! please execute make() successfully before operating.'\r\n          };\r\n          callback.fail(err);\r\n          callback.complete(err);\r\n          return;\r\n        }\r\n\r\n        if (this.isError) {\r\n          callback.fail(this.error);\r\n          callback.complete(this.error);\r\n          return;\r\n        }\r\n\r\n        if (this.makeing) {\r\n          /* 如果还在生成状态，那当前操作将托管到委托，监听生成完成后再通过委托复调当前方法 */\r\n          this.toTempFilePathDelegate = () => {\r\n            this.toTempFilePath(callback);\r\n          };\r\n          return;\r\n        } else {\r\n          this.toTempFilePathDelegate = null;\r\n        }\r\n\r\n        // #ifndef APP-NVUE\r\n        if (this.canvasType === '2d') {\r\n          // #ifdef MP-WEIXIN\r\n          try {\r\n            let dataURL = null;\r\n            // #ifdef VUE3\r\n            dataURL = toRaw(this.canvas)\r\n              .toDataURL();\r\n            // #endif\r\n            // #ifndef VUE3\r\n            dataURL = this.canvas.toDataURL();\r\n            // #endif\r\n            callback.success({\r\n              tempFilePath: dataURL\r\n            });\r\n            callback.complete({\r\n              tempFilePath: dataURL\r\n            });\r\n          } catch (e) {\r\n            callback.fail(e);\r\n            callback.complete(e);\r\n          }\r\n          // #endif\r\n        } else {\r\n          uni.canvasToTempFilePath({\r\n              canvasId: this.canvasId,\r\n              fileType: this.fileType,\r\n              width: Number(this.templateOptions.canvasWidth),\r\n              height: Number(this.templateOptions.canvasHeight),\r\n              destWidth: Number(this.templateOptions.size),\r\n              destHeight: Number(this.templateOptions.size),\r\n              success: res => {\r\n                callback.success(res);\r\n              },\r\n              fail: err => {\r\n                callback.fail(err);\r\n              },\r\n              complete: () => {\r\n                callback.complete();\r\n              }\r\n            },\r\n            this\r\n          );\r\n        }\r\n        // #endif\r\n        // #ifdef APP-NVUE\r\n        const dpr = uni.getSystemInfoSync().pixelRatio;\r\n        this.canvasContext.toTempFilePath(\r\n          0,\r\n          0,\r\n          this.templateOptions.canvasWidth * dpr,\r\n          this.templateOptions.canvasHeight * dpr,\r\n          this.templateOptions.size * dpr,\r\n          this.templateOptions.size * dpr,\r\n          '',\r\n          1,\r\n          res => {\r\n            callback.success(res);\r\n            callback.complete(res);\r\n          }\r\n        );\r\n        // #endif\r\n      },\r\n      /**\r\n       * 保存\r\n       */\r\n      save(callback = {}) {\r\n        if (typeof callback.success != 'function') {\r\n          callback.success = () => {};\r\n        }\r\n        if (typeof callback.fail != 'function') {\r\n          callback.fail = () => {};\r\n        }\r\n        if (typeof callback.complete != 'function') {\r\n          callback.complete = () => {};\r\n        }\r\n\r\n        this.toTempFilePath({\r\n          success: res => {\r\n            // #ifndef H5\r\n            if (this.canvasType === '2d') {\r\n              // #ifdef MP-WEIXIN\r\n              /* 需要将 data:image/png;base64, 这段去除 writeFile 才能正常打开文件，否则是损坏文件，无法打开 */\r\n              const reg = new RegExp('^data:image/png;base64,', 'g');\r\n              const dataURL = res.tempFilePath.replace(reg, '');\r\n              const fs = wx.getFileSystemManager();\r\n              const tempFilePath = `${wx.env.USER_DATA_PATH}/${new Date().getTime()}${\r\n                Math.random()\r\n                  .toString()\r\n                  .split('.')[1]\r\n              }.png`;\r\n              fs.writeFile({\r\n                filePath: tempFilePath, // 要写入的文件路径 (本地路径)\r\n                data: dataURL, // base64图片\r\n                encoding: 'base64',\r\n                success: res1 => {\r\n                  uni.saveImageToPhotosAlbum({\r\n                    filePath: tempFilePath,\r\n                    success: res2 => {\r\n                      callback.success(res2);\r\n                    },\r\n                    fail: err2 => {\r\n                      callback.fail(err2);\r\n                    },\r\n                    complete: () => {\r\n                      callback.complete();\r\n                    }\r\n                  });\r\n                },\r\n                fail: err => {\r\n                  callback.fail(err);\r\n                },\r\n                complete: () => {\r\n                  callback.complete();\r\n                }\r\n              });\r\n              // #endif\r\n            } else {\r\n              uni.saveImageToPhotosAlbum({\r\n                filePath: res.tempFilePath,\r\n                success: res1 => {\r\n                  callback.success(res1);\r\n                },\r\n                fail: err1 => {\r\n                  callback.fail(err1);\r\n                },\r\n                complete: () => {\r\n                  callback.complete();\r\n                }\r\n              });\r\n            }\r\n            // #endif\r\n\r\n            // #ifdef H5\r\n            /* 可以在电脑浏览器下载，移动端iOS不行，安卓微信浏览器不行，安卓外部浏览器可以 */\r\n            this.isH5Save = true;\r\n            this.tempFilePath = res.tempFilePath;\r\n            if (this.h5SaveIsDownload) {\r\n              const aEle = document.createElement('a');\r\n              aEle.download = this.h5DownloadName; // 设置下载的文件名，默认是'下载'\r\n              aEle.href = res.tempFilePath;\r\n              document.body.appendChild(aEle);\r\n              aEle.click();\r\n              aEle.remove(); // 下载之后把创建的元素删除\r\n            }\r\n            callback.success({\r\n              errMsg: 'ok'\r\n            });\r\n            callback.complete({\r\n              errMsg: 'ok'\r\n            });\r\n            // #endif\r\n          },\r\n          fail: err => {\r\n            callback.fail(err);\r\n            callback.complete(err);\r\n          }\r\n        });\r\n      },\r\n      /**\r\n       * 注册click事件\r\n       */\r\n      onClick(e) {\r\n        this.$emit('click', e);\r\n      },\r\n      /**\r\n       * 获取实例\r\n       */\r\n      getInstance() {\r\n        return instance;\r\n      },\r\n      /**\r\n       * 注册扩展，组件仅支持注册type为style的drawCanvas扩展\r\n       * @param {Object} plugin\r\n       */\r\n      registerStyle(plugin) {\r\n        if (plugin.Type != 'style') {\r\n          console.warn('[uQRCode]: registerStyle 仅支持注册 style 类型的扩展！');\r\n          return {\r\n            errMsg: 'registerStyle 仅支持注册 style 类型的扩展！'\r\n          };\r\n        }\r\n        if (typeof plugin === 'function') {\r\n          this.plugins.push({\r\n            plugin,\r\n            name: plugin.Name,\r\n            drawCanvas: plugin.DrawCanvas\r\n          });\r\n        }\r\n      },\r\n      getLoadImage(loadImage) {\r\n        var that = this;\r\n        if (typeof loadImage == 'function') {\r\n          return function(src) {\r\n            /* 判断是否是队列加载图片的 */\r\n            if (that.isQueueLoadImage) {\r\n              /* 解决iOS APP||NVUE同时绘制多个二维码导致图片丢失需使用队列 */\r\n              return queueLoadImage.exec(() => {\r\n                return new Promise((resolve, reject) => {\r\n                  setTimeout(() => {\r\n                    const cache = cacheImageList.find(x => x.src == src);\r\n                    if (cache) {\r\n                      resolve(cache.img);\r\n                    } else {\r\n                      loadImage(src)\r\n                        .then(img => {\r\n                          cacheImageList.push({\r\n                            src,\r\n                            img\r\n                          });\r\n                          resolve(img);\r\n                        })\r\n                        .catch(err => {\r\n                          reject(err);\r\n                        });\r\n                    }\r\n                  }, 10);\r\n                });\r\n              });\r\n            } else {\r\n              return loadImage(src);\r\n            }\r\n          };\r\n        } else {\r\n          return function(src) {\r\n            return Promise.resolve(src);\r\n          };\r\n        }\r\n      }\r\n    }\r\n  };\r\n\r\n  /**\r\n   * 对象属性深度替换\r\n   * @param {Object} o 原始对象/默认对象/被替换的对象\r\n   * @param {Object} r 从这个对象里取值替换到o对象里\r\n   * @return {Object} 替换后的新对象\r\n   */\r\n  function deepReplace(o = {}, r = {}, c = false) {\r\n    let obj;\r\n    if (c) {\r\n      // 从源替换\r\n      obj = o;\r\n    } else {\r\n      // 不替换源，copy一份备份来替换\r\n      obj = {\r\n        ...o\r\n      };\r\n    }\r\n    for (let k in r) {\r\n      var vr = r[k];\r\n      if (vr != undefined) {\r\n        if (vr.constructor == Object) {\r\n          obj[k] = this.deepReplace(obj[k], vr);\r\n        } else if (vr.constructor == String && !vr) {\r\n          obj[k] = obj[k];\r\n        } else {\r\n          obj[k] = vr;\r\n        }\r\n      }\r\n    }\r\n    return obj;\r\n  }\r\n</script>\r\n\r\n<style scoped>\r\n  .uqrcode {\r\n    position: relative;\r\n  }\r\n\r\n  .uqrcode-hide {\r\n    position: fixed;\r\n    left: 7500rpx;\r\n  }\r\n\r\n  .uqrcode-canvas {\r\n    transform-origin: top left;\r\n  }\r\n\r\n  .uqrcode-makeing {\r\n    position: absolute;\r\n    top: 0;\r\n    right: 0;\r\n    bottom: 0;\r\n    left: 0;\r\n    z-index: 10;\r\n    /* #ifndef APP-NVUE */\r\n    display: flex;\r\n    /* #endif */\r\n    justify-content: center;\r\n    align-items: center;\r\n  }\r\n\r\n  .uqrcode-makeing-image {\r\n    /* #ifndef APP-NVUE */\r\n    display: block;\r\n    max-width: 120px;\r\n    max-height: 120px;\r\n    /* #endif */\r\n  }\r\n\r\n  .uqrcode-error {\r\n    position: absolute;\r\n    top: 0;\r\n    right: 0;\r\n    bottom: 0;\r\n    left: 0;\r\n    /* #ifndef APP-NVUE */\r\n    display: flex;\r\n    /* #endif */\r\n    justify-content: center;\r\n    align-items: center;\r\n  }\r\n\r\n  .uqrcode-error-message {\r\n    font-size: 12px;\r\n    color: #939291;\r\n  }\r\n\r\n  /* #ifdef H5 */\r\n  .uqrcode-h5-save {\r\n    position: fixed;\r\n    top: 0;\r\n    right: 0;\r\n    bottom: 0;\r\n    left: 0;\r\n    z-index: 100;\r\n    background-color: rgba(0, 0, 0, 0.68);\r\n    display: flex;\r\n    flex-direction: column;\r\n    justify-content: center;\r\n    align-items: center;\r\n  }\r\n\r\n  .uqrcode-h5-save-image {\r\n    width: 512rpx;\r\n    height: 512rpx;\r\n    padding: 32rpx;\r\n  }\r\n\r\n  .uqrcode-h5-save-text {\r\n    margin-top: 20rpx;\r\n    font-size: 32rpx;\r\n    font-weight: 700;\r\n    color: #ffffff;\r\n  }\r\n\r\n  .uqrcode-h5-save-close {\r\n    position: relative;\r\n    margin-top: 72rpx;\r\n    width: 60rpx;\r\n    height: 60rpx;\r\n    border: 2rpx solid #ffffff;\r\n    border-radius: 60rpx;\r\n    padding: 10rpx;\r\n  }\r\n\r\n  .uqrcode-h5-save-close-before {\r\n    position: absolute;\r\n    top: 50%;\r\n    left: 50%;\r\n    transform: translate(-50%, -50%) rotate(45deg);\r\n    width: 40rpx;\r\n    height: 4rpx;\r\n    background: #ffffff;\r\n  }\r\n\r\n  .uqrcode-h5-save-close-after {\r\n    position: absolute;\r\n    top: 50%;\r\n    left: 50%;\r\n    transform: translate(-50%, -50%) rotate(-45deg);\r\n    width: 40rpx;\r\n    height: 4rpx;\r\n    background: #ffffff;\r\n  }\r\n\r\n  /* #endif */\r\n</style>\r\n", "import mod from \"-!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./uqrcode.vue?vue&type=style&index=0&id=b7042062&scoped=true&lang=css&\"; export default mod; export * from \"-!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./uqrcode.vue?vue&type=style&index=0&id=b7042062&scoped=true&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1752722991712\n      var cssReload = require(\"D:/HBuilderX.3.3.13.20220314.full/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"../../\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}