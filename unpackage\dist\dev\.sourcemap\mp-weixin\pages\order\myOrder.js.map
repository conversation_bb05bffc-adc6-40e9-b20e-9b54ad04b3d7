{"version": 3, "sources": ["uni-app:///main.js", "webpack:///E:/pos/黄金/gold_client/pages/order/myOrder.vue?e4c6", "webpack:///E:/pos/黄金/gold_client/pages/order/myOrder.vue?ecd1", "webpack:///E:/pos/黄金/gold_client/pages/order/myOrder.vue?4d6e", "webpack:///E:/pos/黄金/gold_client/pages/order/myOrder.vue?2dde", "uni-app:///pages/order/myOrder.vue", "webpack:///E:/pos/黄金/gold_client/pages/order/myOrder.vue?ae6d", "webpack:///E:/pos/黄金/gold_client/pages/order/myOrder.vue?7e5c", "webpack:///E:/pos/黄金/gold_client/pages/order/myOrder.vue?62e7", "webpack:///E:/pos/黄金/gold_client/pages/order/myOrder.vue?9fa6"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "name", "data", "orderTypes", "status", "tabIndex", "orderParams", "orderStatus", "page", "limit", "loadStatus", "loadText", "loadmore", "loading", "nomore", "activeColor", "orderList", "isLoadAll", "filters", "formatState", "onPullDownRefresh", "onReachBottom", "console", "onShow", "methods", "getOrderData", "util", "uni", "title", "icon", "records", "onClickItem", "cancelOrderEvent", "onCopy", "success", "enterOrderDetailPage", "url", "to<PERSON>ayMoney"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,gBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAgI;AAChI;AAC2D;AACL;AACc;AACwB;;;AAG5F;AACgL;AAChL,gBAAgB,yLAAU;AAC1B,EAAE,6EAAM;AACR,EAAE,8FAAM;AACR,EAAE,uGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,kGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACxBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,mQAEN;AACP,KAAK;AACL;AACA,aAAa,uPAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACxDA;AAAA;AAAA;AAAA;AAAwpB,CAAgB,6qBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACkF5qB;AACA;AAAA,eACA;EACAC;EACAC;IACA;MACAC;QACAC;QACAH;MACA,GACA;QACAG;QACAH;MACA,GACA;QACAG;QACAH;MACA,GACA;QACAG;QACAH;MACA,EACA;MACAI;MACAC;QACAC;QACAC;QACAC;MACA;MACAC;MACAC;QACAC;QACAC;QACAC;MACA;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACAC;MACA;QACA;MACA;QACA;MACA;QACA;MACA;QACA;MACA;QACA;MACA;IACA;EACA;EACAC;IACA;IACA;IACA;EACA;EACAC;IACAC;IACA;MACA;MACA;IACA;EACA;EACAC;IACA;IACA;IACA;EACA;EACAC;IACA;IACAC;MAAA;MACA;MACAC;QACAJ;QACA;UACAK;YACAC;YACAC;UACA;QACA;UACA;UACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACAC;UACA;UACA;UACA;UACA;QACA;MACA;IACA;IACA;IACAC;MACA;MACA;MACA;MACA;MACA;IACA;IACA;IACAC;MAAA;MACAN;QACAJ;QACA;UACAK;YACAC;YACAC;UACA;QACA;UACAF;YACAC;YACAC;UACA;UACA;UACA;UACA;QACA;MACA;IACA;IACAI;MACAN;QACAzB;QACAgC;UACAP;YACAC;YACAC;UACA;QACA;MACA;IACA;IACA;IACAM;MACAR;QACAS;MACA;IACA;IACA;IACAC;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACzOA;AAAA;AAAA;AAAA;AAA+uC,CAAgB,4sCAAG,EAAC,C;;;;;;;;;;;ACAnwC;AACA,OAAO,KAAU,EAAE,kBAKd;;;;;;;;;;;;;ACNL;AAAA;AAAA;AAAA;AAAuwC,CAAgB,ouCAAG,EAAC,C;;;;;;;;;;;ACA3xC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/order/myOrder.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/order/myOrder.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./myOrder.vue?vue&type=template&id=67aadf18&scoped=true&\"\nvar renderjs\nimport script from \"./myOrder.vue?vue&type=script&lang=js&\"\nexport * from \"./myOrder.vue?vue&type=script&lang=js&\"\nimport style0 from \"./myOrder.vue?vue&type=style&index=0&lang=scss&\"\nimport style1 from \"./myOrder.vue?vue&type=style&index=1&id=67aadf18&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"67aadf18\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/order/myOrder.vue\"\nexport default component.exports", "export * from \"-!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./myOrder.vue?vue&type=template&id=67aadf18&scoped=true&\"", "var components\ntry {\n  components = {\n    uCountDown: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-count-down/u-count-down\" */ \"uview-ui/components/u-count-down/u-count-down.vue\"\n      )\n    },\n    uLoadmore: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-loadmore/u-loadmore\" */ \"uview-ui/components/u-loadmore/u-loadmore.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l0 = _vm.__map(_vm.orderList, function (item, index) {\n    var $orig = _vm.__get_orig(item)\n    var f0 = _vm._f(\"formatState\")(item.orderStatus)\n    var g0 = [\"3\", \"4\", \"7\"].includes(item.orderStatus)\n    return {\n      $orig: $orig,\n      f0: f0,\n      g0: g0,\n    }\n  })\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l0: l0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./myOrder.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./myOrder.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"order-list-page\">\r\n\t\t<!-- 订单状态 -->\r\n\t\t<view class=\"order-list-header\">\r\n\t\t\t<view class=\"one-status\" v-for=\"(item, index) in orderTypes\" :key=\"index\"\r\n\t\t\t\t:class=\"index == tabIndex ? 'active' : ''\" @click=\"onClickItem(item, index)\">{{ item.name }}</view>\r\n\t\t</view>\r\n\t\t<!-- 订单列表 -->\r\n\t\t<view class=\"order-list\">\r\n\t\t\t<view class=\"each-order\" v-for=\"(item, index) of orderList\" :key=\"index\">\r\n\t\t\t\t<view class=\"header\">\r\n\t\t\t\t\t<view class=\"order-no-status\">\r\n\t\t\t\t\t\t<view class=\"no\">\r\n\t\t\t\t\t\t\t<view>订单编：{{item.orderNo}}</view>\r\n\t\t\t\t\t\t\t<view @click=\"onCopy(item.orderNo)\" style=\"display: flex;margin-left: 20rpx;\"><image\r\n\t\t\t\t\t\t\t\t\tstyle=\"width: 22rpx;height: 22rpx;\" src=\"/static/img/shop/shop-copy.png\" alt=\"\" />\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"status\">{{item.orderStatus | formatState}}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"good-detail\">\r\n\t\t\t\t\t<view class=\"product_info\">\r\n\t\t\t\t\t\t<view class=\"prodct_left\">\r\n\t\t\t\t\t\t\t<image style=\"width:164rpx;height: 164rpx;\" :src=\"item.goodsImg\"></image>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"product_center\">\r\n\t\t\t\t\t\t\t<view style=\"font-size: 28rpx;line-height: 44rpx;\" class=\"text-ellipsis_2\">\r\n\t\t\t\t\t\t\t\t{{item.goodsName}}</view>\r\n\t\t\t\t\t\t\t<view style=\"display: flex;margin-top: 20rpx;\">\r\n\t\t\t\t\t\t\t\t<view\r\n\t\t\t\t\t\t\t\t\tstyle=\"color: #61687C;font-size: 24rpx;background-color: #F2F4F7;text-align: center;padding: 6rpx 20rpx;border-radius: 6rpx;\">\r\n\t\t\t\t\t\t\t\t\t{{item.specification}}g</view>\r\n\t\t\t\t\t\t\t\t<view></view>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view class=\"product_right\">\r\n\t\t\t\t\t\t\t<view><text class=\"font_small\">¥{{item.price}}</text></view>\r\n\t\t\t\t\t\t\t<view style=\"font-size: 24rpx;color: #9FA3B0;margin-top: 8rpx;text-align: right;\">x1</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"opera-btns\">\r\n\t\t\t\t\t<view style=\"display: flex;justify-content: space-between;align-items: center;\" v-if=\"item.orderStatus == '0'\">\r\n\t\t\t\t\t\t<view style=\"display: flex;background-color: rgba(255, 0, 70, 0.05);align-items: center;padding: 4rpx 8rpx;border-radius: 8rpx;\">\r\n\t\t\t\t\t\t\t<!-- 倒计时、创建时间 -->\r\n\t\t\t\t\t\t\t<view style=\"font-size: 24rpx;color: #FF0046;\">支付剩余：</view>\r\n\t\t\t\t\t\t\t<u-count-down :timestamp=\"item.timestamp\" font-size=\"24\" bg-color=\"none\" color=\"#FF0046\" separator=\"zh\" separator-size=\"24\" separator-color=\"#FF0046\"></u-count-down>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view style=\"display: flex;justify-content: space-between;align-items: center;\">\r\n\t\t\t\t\t\t\t<view class=\"each-btn cancel\" @click=\"cancelOrderEvent(item)\">取消订单</view>\r\n\t\t\t\t\t\t\t<view class=\"each-btn pay\" @click=\"enterOrderDetailPage(item)\">立即付款</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view style=\"display: flex;justify-content: space-between;align-items: center;\" v-if=\"item.orderStatus == '1'\">\r\n\t\t\t\t\t\t<view style=\"color: #9FA3B0;font-size: 24rpx;\">\r\n\t\t\t\t\t\t\t<!-- 倒计时、创建时间 -->\r\n\t\t\t\t\t\t\t下单时间：{{item.createDate}}\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view style=\"display: flex;justify-content: space-between;align-items: center;\">\r\n\t\t\t\t\t\t\t<view class=\"each-btn pay\" @click=\"enterOrderDetailPage(item)\">去核销</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view style=\"display: flex;justify-content: space-between;align-items: center;\" v-if=\"['3','4','7'].includes(item.orderStatus)\">\r\n\t\t\t\t\t\t<view style=\"color: #9FA3B0;font-size: 24rpx;\">\r\n\t\t\t\t\t\t\t<!-- 下单时间：{{item.createDate}} -->\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<view style=\"display: flex;justify-content: space-between;align-items: center;\">\r\n\t\t\t\t\t\t\t<view class=\"each-btn pay\" @click=\"enterOrderDetailPage(item)\">查看订单</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<u-loadmore :status=\"loadStatus\" :load-text=\"loadText\"></u-loadmore>\r\n\t\t\t<!-- #ifdef WEB -->\r\n\t\t\t<view class=\"page_bottom\"></view>\r\n\t\t\t<!-- #endif -->\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\tconst api = require('../../config/api')\r\n\tconst util = require('../../utils/util')\r\n\texport default {\r\n\t\tname: 'orderList',\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\torderTypes: [{\r\n\t\t\t\t\t\tstatus: 5,\r\n\t\t\t\t\t\tname: '全部'\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tstatus: 0,\r\n\t\t\t\t\t\tname: '待付款'\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tstatus: 1,\r\n\t\t\t\t\t\tname: '待核销'\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tstatus: 3,\r\n\t\t\t\t\t\tname: '已完成'\r\n\t\t\t\t\t}\r\n\t\t\t\t],\r\n\t\t\t\ttabIndex: '0',\r\n\t\t\t\torderParams: {\r\n\t\t\t\t\torderStatus: 5,\r\n\t\t\t\t\tpage: 1,\r\n\t\t\t\t\tlimit: 10\r\n\t\t\t\t},\r\n\t\t\t\tloadStatus: 'loading',\r\n\t\t\t\tloadText: {\r\n\t\t\t\t\tloadmore: '加载更多',\r\n\t\t\t\t\tloading: '努力加载中',\r\n\t\t\t\t\tnomore: '已经到底了'\r\n\t\t\t\t},\r\n\t\t\t\tactiveColor: '#007aff',\r\n\t\t\t\torderList: [],\r\n\t\t\t\tisLoadAll: false\r\n\t\t\t}\r\n\t\t},\r\n\t\tfilters: {\r\n\t\t\tformatState: function(_state) {\r\n\t\t\t\tif (_state === '0') {\r\n\t\t\t\t\treturn '待付款'\r\n\t\t\t\t} else if (_state === '1') {\r\n\t\t\t\t\treturn '待核销'\r\n\t\t\t\t} else if (_state === '3') {\r\n\t\t\t\t\treturn '已完成'\r\n\t\t\t\t} else if (_state === '4') {\r\n\t\t\t\t\treturn '已取消'\r\n\t\t\t\t} else if (_state === '7') {\r\n\t\t\t\t\treturn '已退款'\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\t\tonPullDownRefresh() {\r\n\t\t\tthis.isLoadAll = false\r\n\t\t\tthis.orderParams.page = 1\r\n\t\t\tthis.getOrderData()\r\n\t\t},\r\n\t\tonReachBottom () {\r\n\t\t\tconsole.log('加载中')\r\n\t\t\tif (!this.isLoadAll) {\r\n\t\t\t\tthis.orderParams.page++\r\n\t\t\t\tthis.getOrderData()\r\n\t\t\t}\r\n\t\t},\r\n\t\tonShow: function() {\r\n\t\t\tthis.orderList=[]\r\n\t\t\tthis.orderParams.page = 1\r\n\t\t\tthis.getOrderData()\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\t// 获取订单列表\r\n\t\t\tgetOrderData() {\r\n\t\t\t\tthis.loadStatus = 'loading'\r\n\t\t\t\tutil.request(api.myOrderUrl, this.orderParams, 'POST').then((res) => {\r\n\t\t\t\t\tconsole.log(res)\r\n\t\t\t\t\tif (res.code !== 0) {\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: res.message,\r\n\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tlet records = res.data.records\r\n\t\t\t\t\t\tfor (let i = 0; i < records.length; i++) {\r\n\t\t\t\t\t\t\t// records[i].createDate = records[i].createDate.replace(/-/g,'/')\r\n\t\t\t\t\t\t\t// console.log(records[i].createDate)\r\n\t\t\t\t\t\t\tlet nowTime = new Date()\r\n\t\t\t\t\t\t\t// let startTime = new Date(records[i].createDate);\r\n\t\t\t\t\t\t\tlet endTime = new Date(records[i].createDate)\r\n\t\t\t\t\t\t\tlet nowSeconds = ( nowTime.getTime()) / 1000 ; // 开始时间转换为秒\r\n\t\t\t\t\t\t\t// let startSeconds = startTime.getTime() / 1000; // 开始时间转换为秒\r\n\t\t\t\t\t\t\tlet endSeconds = (endTime.getTime()+ 30 * 60 * 1000) / 1000; // 结束时间转换为秒\r\n\t\t\t\t\t\t\tlet timestamp = endSeconds - nowSeconds; // 持续时间（秒）\r\n\t\t\t\t\t\t\trecords[i].timestamp = timestamp\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tthis.orderList = this.orderList.concat(records || [])\r\n\t\t\t\t\t\tthis.isLoadAll = this.orderParams.page >= res.data.pages //4\r\n\t\t\t\t\t\tthis.loadStatus = 'nomore'\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t// 切换订单状态tab\r\n\t\t\tonClickItem(item, inx) {\r\n\t\t\t\tthis.tabIndex = inx\r\n\t\t\t\tthis.orderParams.orderStatus = item.status\r\n\t\t\t\tthis.orderParams.page = 1\r\n\t\t\t\tthis.orderList = []\r\n\t\t\t\tthis.getOrderData()\r\n\t\t\t},\r\n\t\t\t// 取消订单\r\n\t\t\tcancelOrderEvent(item) {\r\n\t\t\t\tutil.request(api.cancelOrderUrl + item.orderNo, {}, 'POST').then((res) => {\r\n\t\t\t\t\tconsole.log(res)\r\n\t\t\t\t\tif (res.code !== 0) {\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: res.message,\r\n\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: '取消订单成功',\r\n\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t\tthis.orderList=[]\r\n\t\t\t\t\t\tthis.orderParams.page = 1\r\n\t\t\t\t\t\tthis.getOrderData()\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tonCopy(orderNo) {\r\n\t\t\t\tuni.setClipboardData({\r\n\t\t\t\t\tdata: orderNo,\r\n\t\t\t\t\tsuccess: function() {\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: '复制成功',\r\n\t\t\t\t\t\t\ticon: 'success'\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t// 查看订单详情\r\n\t\t\tenterOrderDetailPage(item) {\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: '/pages/order/orderDetail?orderNo=' + item.orderNo\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t// 去付款\r\n\t\t\ttoPayMoney(x, item) {}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n\r\n<style lang=\"scss\">\r\n\tpage{\r\n\t\tbackground-color: #F7F7F7;\r\n\t}\r\n\t.order-list-page {\r\n\t\theight: 100%;\r\n\r\n\t\t.type-tabs {\r\n\t\t\t.segmented-control__text {\r\n\t\t\t\tfont-size: 24rpx;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</style>\r\n<style lang=\"scss\" scoped>\r\n\t.order-list-page {\r\n\t\t.order-list-header {\r\n\t\t\tpadding: 20rpx;\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-items: center;\r\n\t\t\tjustify-content: space-around;\r\n\t\t\tbackground-color: #FFFFFF;\r\n\t\t\t.one-status {\r\n\t\t\t\tpadding: 10rpx 0;\r\n\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\tposition: relative;\r\n\t\t\t\tcolor: #9FA3B0;\r\n\r\n\t\t\t\t&.active {\r\n\t\t\t\t\tcolor: #171B25;\r\n\r\n\t\t\t\t\t&:after {\r\n\t\t\t\t\t\tcontent: '';\r\n\t\t\t\t\t\tdisplay: block;\r\n\t\t\t\t\t\twidth: 100%;\r\n\t\t\t\t\t\theight: 2px;\r\n\t\t\t\t\t\tbackground-color: #BBA186;\r\n\t\t\t\t\t\tposition: absolute;\r\n\t\t\t\t\t\tbottom: 0;\r\n\t\t\t\t\t\tleft: 0;\r\n\t\t\t\t\t\ttransition: all 0.3s;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t.order-list {\r\n\t\t\t// width: 710rpx;\r\n\t\t\t// margin: 0 auto;\r\n\t\t\tpadding: 20rpx;\r\n\t\t\t// background-color: #ececec;\r\n\t\t\t// height: calc(100% - 100rpx);\r\n\t\t\toverflow: auto;\r\n\r\n\t\t\t.each-order {\r\n\t\t\t\tbackground-color: #ffffff;\r\n\t\t\t\tpadding: 20rpx;\r\n\t\t\t\tmargin-bottom: 20rpx;\r\n\t\t\t\tborder-radius: 10rpx;\r\n\r\n\t\t\t\t.header {\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\talign-items: center;\r\n\t\t\t\t\tjustify-content: space-between;\r\n\r\n\t\t\t\t\t.order-no-status {\r\n\t\t\t\t\t\twidth: 100%;\r\n\t\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\t\talign-items: center;\r\n\t\t\t\t\t\tjustify-content: space-between;\r\n\t\t\t\t\t\t// padding-bottom: 20rpx;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t.no {\r\n\t\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\t\talign-items: center;\r\n\t\t\t\t\t\tcolor: #171B25;\r\n\t\t\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t.status {\r\n\t\t\t\t\t\tcolor: #FF0046;\r\n\t\t\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t\t.good-detail {\r\n\t\t\t\t\tborder-bottom: 1rpx solid #F7F7F7;\r\n\t\t\t\t\t.product_info {\r\n\t\t\t\t\t\t// margin-top: 24rpx;\r\n\t\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\t\tpadding: 24rpx;\r\n\t\t\t\t\t\tbackground-color: #FFFFFF;\r\n\t\t\t\t\t\tborder-radius: 10rpx;\r\n\t\t\t\t\t\t.prodct_left {\r\n\t\t\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\t\t\tborder-radius: 10rpx;\r\n\t\t\t\t\t\t\toverflow: hidden;\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t.product_center {\r\n\t\t\t\t\t\t\tflex: 1;\r\n\t\t\t\t\t\t\tmargin-left: 20rpx;\r\n\t\t\t\t\t\t}\r\n\r\n\t\t\t\t\t\t.product_right {\r\n\t\t\t\t\t\t\tmargin-left: 32rpx;\r\n\r\n\t\t\t\t\t\t\t.font_small {\r\n\t\t\t\t\t\t\t\tfont-size: 36rpx;\r\n\t\t\t\t\t\t\t\tcolor: #171B25;\r\n\t\t\t\t\t\t\t\tfont-weight: 600;\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\t.opera-btns {\r\n\t\t\t\t\tmargin-top: 20rpx;\r\n\t\t\t\t\t.each-btn {\r\n\t\t\t\t\t\tpadding: 10rpx 20rpx;\r\n\t\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\t\tborder: 1px solid #ececec;\r\n\t\t\t\t\t\tborder-radius: 200rpx;\r\n\t\t\t\t\t\tmargin-left: 20rpx;\r\n\t\t\t\t\t\t&.pay {\r\n\t\t\t\t\t\t\tcolor: #FFFFFF;\r\n\t\t\t\t\t\t\tbackground-color: #BBA186;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</style>", "import mod from \"-!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./myOrder.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./myOrder.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754273099629\n      var cssReload = require(\"D:/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  ", "import mod from \"-!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./myOrder.vue?vue&type=style&index=1&id=67aadf18&lang=scss&scoped=true&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./myOrder.vue?vue&type=style&index=1&id=67aadf18&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754273099781\n      var cssReload = require(\"D:/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}