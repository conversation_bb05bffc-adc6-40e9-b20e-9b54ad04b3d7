{"version": 3, "sources": ["webpack:///E:/pos/黄金/gold_client/node_modules/uview-ui/components/u-divider/u-divider.vue?a775", "webpack:///E:/pos/黄金/gold_client/node_modules/uview-ui/components/u-divider/u-divider.vue?737b", "webpack:///E:/pos/黄金/gold_client/node_modules/uview-ui/components/u-divider/u-divider.vue?8adb", "webpack:///E:/pos/黄金/gold_client/node_modules/uview-ui/components/u-divider/u-divider.vue?f715", "uni-app:///node_modules/uview-ui/components/u-divider/u-divider.vue", "webpack:///E:/pos/黄金/gold_client/node_modules/uview-ui/components/u-divider/u-divider.vue?f634", "webpack:///E:/pos/黄金/gold_client/node_modules/uview-ui/components/u-divider/u-divider.vue?9989"], "names": ["name", "props", "halfWidth", "type", "default", "borderColor", "color", "fontSize", "bgColor", "height", "marginTop", "marginBottom", "useSlot", "computed", "lineStyle", "style", "methods", "click"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkI;AAClI;AAC6D;AACL;AACsC;;;AAG9F;AACgL;AAChL,gBAAgB,yLAAU;AAC1B,EAAE,+EAAM;AACR,EAAE,gGAAM;AACR,EAAE,yGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACnBA;AAAA;AAAA;AAAA;AAA0pB,CAAgB,+qBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACiB9qB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAhBA,eAiBA;EACAA;EACAC;IACA;IACAC;MACAC;MACAC;IACA;IACA;IACAC;MACAF;MACAC;IACA;IACA;IACAD;MACAA;MACAC;IACA;IACA;IACAE;MACAH;MACAC;IACA;IACA;IACAG;MACAJ;MACAC;IACA;IACA;IACAI;MACAL;MACAC;IACA;IACA;IACAK;MACAN;MACAC;IACA;IACA;IACAM;MACAP;MACAC;IACA;IACA;IACAO;MACAR;MACAC;IACA;IACA;IACAQ;MACAT;MACAC;IACA;EACA;EACAS;IACAC;MACA;MACA,iFACAC;MACA;MACA;MACA;IACA;EACA;EACAC;IACAC;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;ACvGA;AAAA;AAAA;AAAA;AAAywC,CAAgB,suCAAG,EAAC,C;;;;;;;;;;;ACA7xC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "node-modules/uview-ui/components/u-divider/u-divider.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./u-divider.vue?vue&type=template&id=239d3faa&scoped=true&\"\nvar renderjs\nimport script from \"./u-divider.vue?vue&type=script&lang=js&\"\nexport * from \"./u-divider.vue?vue&type=script&lang=js&\"\nimport style0 from \"./u-divider.vue?vue&type=style&index=0&id=239d3faa&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"239d3faa\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"node_modules/uview-ui/components/u-divider/u-divider.vue\"\nexport default component.exports", "export * from \"-!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-divider.vue?vue&type=template&id=239d3faa&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var s0 = _vm.__get_style([_vm.lineStyle])\n  var s1 = _vm.__get_style([_vm.lineStyle])\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        s0: s0,\n        s1: s1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-divider.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-divider.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"u-divider\" :style=\"{\r\n\t\theight: height == 'auto' ? 'auto' : height + 'rpx',\r\n\t\tbackgroundColor: bgColor,\r\n\t\tmarginBottom: marginBottom + 'rpx',\r\n\t\tmarginTop: marginTop + 'rpx'\r\n\t}\" @tap=\"click\">\r\n\t\t<view class=\"u-divider-line\" :class=\"[type ? 'u-divider-line--bordercolor--' + type : '']\" :style=\"[lineStyle]\"></view>\r\n\t\t<view v-if=\"useSlot\" class=\"u-divider-text\" :style=\"{\r\n\t\t\tcolor: color,\r\n\t\t\tfontSize: fontSize + 'rpx'\r\n\t\t}\"><slot /></view>\r\n\t\t<view class=\"u-divider-line\" :class=\"[type ? 'u-divider-line--bordercolor--' + type : '']\" :style=\"[lineStyle]\"></view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n/**\r\n * divider 分割线\r\n * @description 区隔内容的分割线，一般用于页面底部\"没有更多\"的提示。\r\n * @tutorial https://www.uviewui.com/components/divider.html\r\n * @property {String Number} half-width 文字左或右边线条宽度，数值或百分比，数值时单位为rpx\r\n * @property {String} border-color 线条颜色，优先级高于type（默认#dcdfe6）\r\n * @property {String} color 文字颜色（默认#909399）\r\n * @property {String Number} fontSize 字体大小，单位rpx（默认26）\r\n * @property {String} bg-color 整个divider的背景颜色（默认呢#ffffff）\r\n * @property {String Number} height 整个divider的高度，单位rpx（默认40）\r\n * @property {String} type 将线条设置主题色（默认primary）\r\n * @property {Boolean} useSlot 是否使用slot传入内容，如果不传入，中间不会有空隙（默认true）\r\n * @property {String Number} margin-top 与前一个组件的距离，单位rpx（默认0）\r\n * @property {String Number} margin-bottom 与后一个组件的距离，单位rpx（0）\r\n * @event {Function} click divider组件被点击时触发\r\n * @example <u-divider color=\"#fa3534\">长河落日圆</u-divider>\r\n */\r\nexport default {\r\n\tname: 'u-divider',\r\n\tprops: {\r\n\t\t// 单一边divider横线的宽度(数值)，单位rpx。或者百分比\r\n\t\thalfWidth: {\r\n\t\t\ttype: [Number, String],\r\n\t\t\tdefault: 150\r\n\t\t},\r\n\t\t// divider横线的颜色，如设置，\r\n\t\tborderColor: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: '#dcdfe6'\r\n\t\t},\r\n\t\t// 主题色，可以是primary|info|success|warning|error之一值\r\n\t\ttype: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: 'primary'\r\n\t\t},\r\n\t\t// 文字颜色\r\n\t\tcolor: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: '#909399'\r\n\t\t},\r\n\t\t// 文字大小，单位rpx\r\n\t\tfontSize: {\r\n\t\t\ttype: [Number, String],\r\n\t\t\tdefault: 26\r\n\t\t},\r\n\t\t// 整个divider的背景颜色\r\n\t\tbgColor: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: '#ffffff'\r\n\t\t},\r\n\t\t// 整个divider的高度单位rpx\r\n\t\theight: {\r\n\t\t\ttype: [Number, String],\r\n\t\t\tdefault: 'auto'\r\n\t\t},\r\n\t\t// 上边距\r\n\t\tmarginTop: {\r\n\t\t\ttype: [String, Number],\r\n\t\t\tdefault: 0\r\n\t\t},\r\n\t\t// 下边距\r\n\t\tmarginBottom: {\r\n\t\t\ttype: [String, Number],\r\n\t\t\tdefault: 0\r\n\t\t},\r\n\t\t// 是否使用slot传入内容，如果不用slot传入内容，先的中间就不会有空隙\r\n\t\tuseSlot: {\r\n\t\t\ttype: Boolean,\r\n\t\t\tdefault: true\r\n\t\t}\r\n\t},\r\n\tcomputed: {\r\n\t\tlineStyle() {\r\n\t\t\tlet style = {};\r\n\t\t\tif(String(this.halfWidth).indexOf('%') != -1) style.width = this.halfWidth;\r\n\t\t\telse style.width = this.halfWidth + 'rpx';\r\n\t\t\t// borderColor优先级高于type值\r\n\t\t\tif(this.borderColor) style.borderColor = this.borderColor;\r\n\t\t\treturn style;\r\n\t\t}\r\n\t},\r\n\tmethods: {\r\n\t\tclick() {\r\n\t\t\tthis.$emit('click');\r\n\t\t}\r\n\t}\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n@import \"../../libs/css/style.components.scss\";\r\n.u-divider {\r\n\twidth: 100%;\r\n\tposition: relative;\r\n\ttext-align: center;\r\n\t@include vue-flex;\r\n\tjustify-content: center;\r\n\talign-items: center;\r\n\toverflow: hidden;\r\n\tflex-direction: row;\r\n}\r\n\r\n.u-divider-line {\r\n\tborder-bottom: 1px solid $u-border-color;\r\n\ttransform: scale(1, 0.5);\r\n\ttransform-origin: center;\r\n\t\r\n\t&--bordercolor--primary {\r\n\t\tborder-color: $u-type-primary;\r\n\t}\r\n\t\r\n\t&--bordercolor--success {\r\n\t\tborder-color: $u-type-success;\r\n\t}\r\n\t\r\n\t&--bordercolor--error {\r\n\t\tborder-color: $u-type-primary;\r\n\t}\r\n\t\r\n\t&--bordercolor--info {\r\n\t\tborder-color: $u-type-info;\r\n\t}\r\n\t\r\n\t&--bordercolor--warning {\r\n\t\tborder-color: $u-type-warning;\r\n\t}\r\n}\r\n\r\n.u-divider-text {\r\n\twhite-space: nowrap;\r\n\tpadding: 0 16rpx;\r\n\t/* #ifndef APP-NVUE */\r\n\tdisplay: inline-flex;\t\t\r\n\t/* #endif */\r\n}\r\n</style>\r\n", "import mod from \"-!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-divider.vue?vue&type=style&index=0&id=239d3faa&lang=scss&scoped=true&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-divider.vue?vue&type=style&index=0&id=239d3faa&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754188040154\n      var cssReload = require(\"D:/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}