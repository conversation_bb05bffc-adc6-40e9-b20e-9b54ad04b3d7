{"version": 3, "sources": ["uni-app:///main.js", "webpack:///E:/pos/黄金/gold_client/pages/notice/noticeDetail.vue?1ede", "webpack:///E:/pos/黄金/gold_client/pages/notice/noticeDetail.vue?19f1", "webpack:///E:/pos/黄金/gold_client/pages/notice/noticeDetail.vue?affd", "webpack:///E:/pos/黄金/gold_client/pages/notice/noticeDetail.vue?151d", "uni-app:///pages/notice/noticeDetail.vue", "webpack:///E:/pos/黄金/gold_client/pages/notice/noticeDetail.vue?1337", "webpack:///E:/pos/黄金/gold_client/pages/notice/noticeDetail.vue?41f3"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "name", "data", "onLoad", "onUnload", "uni"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,qBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAyH;AACzH;AACgE;AACL;AACc;;;AAGzE;AACgL;AAChL,gBAAgB,yLAAU;AAC1B,EAAE,kFAAM;AACR,EAAE,uFAAM;AACR,EAAE,gGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,2FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAA6pB,CAAgB,krBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;eCSjrB;EACAC;EACAC;IACA;MACAA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACAC;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACtBA;AAAA;AAAA;AAAA;AAAovC,CAAgB,itCAAG,EAAC,C;;;;;;;;;;;ACAxwC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/notice/noticeDetail.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/notice/noticeDetail.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./noticeDetail.vue?vue&type=template&id=694a1437&\"\nvar renderjs\nimport script from \"./noticeDetail.vue?vue&type=script&lang=js&\"\nexport * from \"./noticeDetail.vue?vue&type=script&lang=js&\"\nimport style0 from \"./noticeDetail.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/notice/noticeDetail.vue\"\nexport default component.exports", "export * from \"-!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./noticeDetail.vue?vue&type=template&id=694a1437&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./noticeDetail.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./noticeDetail.vue?vue&type=script&lang=js&\"", "<template>\r\n  <view class=\"notice-detail\">\r\n    <view class=\"title\">{{data.title}}</view>\r\n    <view class=\"date\">{{data.createDate}}</view>\r\n    <view class=\"content\" v-html=\"data.content\"></view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nexport default {\r\n  name: 'noticeDetail',\r\n  data() {\r\n    return {\r\n      data: `<div class=\"post-content_main-post-info__qCbZu\"><div class=\"index_bbs-thread-comp__PC7_r bbs-thread-comp main-thread\"><div class=\"thread-content-detail\">    <p></p><div data-hupu-node=\"image\"><p><p class=\"image-wrapper\"><span class=\"img-wrapper-embedded\" id=\"img-wrapper-embedded-0\" style=\"width: auto; max-width: 100%;\"><div class=\"lazyload-wrapper \"><image src=\"https://i1.hoopchina.com.cn/news-editor/2025-3-20/15-31-12/57ad0a38-2319-43bb-a838-7384649a8061.png?x-oss-process=image/resize,w_800/format,webp\" alt=\"\" class=\"thread-img\" style=\"width: auto; max-width: 100%; height: auto;\"></div></span></p></p></div><p>虎扑03月20日讯 今日NBA常规赛，国王主场123-119战胜骑士。</p><p>赛后，国王主教练道格-克里斯蒂接受了记者采访，谈到了这场赢下东部第一的胜利。</p><p>记者提问：“在第一节球队只得到了15分，但之后球队拿出了上佳的表现，这中间发生了什么？”</p><p>克里斯蒂表示：“我知道骑士有非常多优秀的球员，像多诺万（米切尔），埃文（莫布利），但我们后三节打出了我们自己的篮球，萨克拉门托式的篮球。但我们穿上这身球员，这就是我们希望做到的。我给队员传递的信息是，团结在彼此周围，为彼此战斗。”</p><p>今日获胜后，国王战绩来到35胜33负，位列西部第9。</p> <p></p><br><div class=\"article-source\">  来源： X</div><br><br><br></div><div class=\"seo-dom\">    <p></p><div data-hupu-node=\"image\"><p><image src=\"https://i1.hoopchina.com.cn/news-editor/2025-3-20/15-31-12/57ad0a38-2319-43bb-a838-7384649a8061.png?x-oss-process=image/resize,w_800/format,webp\"></p></div><p>虎扑03月20日讯 今日NBA常规赛，国王主场123-119战胜骑士。</p><p>赛后，国王主教练道格-克里斯蒂接受了记者采访，谈到了这场赢下东部第一的胜利。</p><p>记者提问：“在第一节球队只得到了15分，但之后球队拿出了上佳的表现，这中间发生了什么？”</p><p>克里斯蒂表示：“我知道骑士有非常多优秀的球员，像多诺万（米切尔），埃文（莫布利），但我们后三节打出了我们自己的篮球，萨克拉门托式的篮球。但我们穿上这身球员，这就是我们希望做到的。我给队员传递的信息是，团结在彼此周围，为彼此战斗。”</p><p>今日获胜后，国王战绩来到35胜33负，位列西部第9。</p> <p></p><br><div class=\"article-source\">  来源： X</div><br><br><br></div></div><div class=\"post-operate_post-operate-comp-wrapper___odBI\"><div class=\"post-operate-comp main-operate\"><div class=\"post-operate-comp-main\"><div class=\"post-operate-comp-main-recommend hove-deep todo-list \"><i class=\"iconfont icontuijian todo-list-icon\"></i><span class=\"todo-list-text\">推荐<!-- --> (2)</span></div><div class=\"post-operate-comp-main-reply todo-list\"><i class=\"iconfont iconpinglun todo-list-icon\"></i><span class=\"todo-list-text\">评论<!-- --> (10)</span></div><div class=\"post-operate-comp-main-collect  todo-list\"><i class=\"iconfont iconshoucang todo-list-icon\"></i><span class=\"todo-list-text\">收藏</span></div><div class=\"post-operate-comp-main-share todo-list\"><i class=\"iconfont iconfenxiang todo-list-icon\"></i><span class=\"todo-list-text\">分享</span><div class=\"share-modal\"><div class=\"prefix\"></div><div class=\"ct\"><div class=\"left-share\"><div class=\"icons\"><div class=\"icon-list\"><i class=\"iconfont iconQQ icon-list-img\"></i><p class=\"icon-list-name\">QQ</p></div><div class=\"icon-list\"><i class=\"iconfont iconQQkongjian icon-list-img\"></i><p class=\"icon-list-name\">QQ空间</p></div><div class=\"icon-list\"><i class=\"iconfont iconxinlangweibo icon-list-img\"></i><p class=\"icon-list-name\">微博</p></div></div><div class=\"copy-board\"><div class=\"copy-value\">https://bbs.hupu.com/631253845.html?is_reflow=pc</div><div title=\"点击复制分享地址\" class=\"copy-btn\">复制</div></div></div><div class=\"right-qrcode\"><p class=\"qr-tip\">微信扫一扫分享</p><div class=\"qr-img\"></div></div></div></div></div></div><div class=\"post-operate-comp-other\"><div class=\"post-operate-comp-other-report todo-list\"><i class=\"iconfont iconjubao1 todo-list-icon\"></i><span class=\"todo-list-text\">举报</span></div><div class=\"post-operate-comp-other-only-main todo-list\"><i class=\"iconfont iconzhikanlouzhu todo-list-icon\"></i><span class=\"todo-list-text\">只看楼主</span></div></div></div></div></div>`\r\n    }\r\n  },\r\n  onLoad() {\r\n    this.data = uni.getStorageSync('noticeData') || {}\r\n  },\r\n  onUnload() {\r\n    uni.removeStorageSync('noticeData')\r\n  }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.notice-detail {\r\n  padding: 20rpx;\r\n  background-color: #fff;\r\n  .title {\r\n    font-size: 30rpx;\r\n    font-weight: bold;\r\n    margin-bottom: 20rpx;\r\n  }\r\n  .date {\r\n    font-size: 24rpx;\r\n    color: #999;\r\n    margin-bottom: 20rpx;\r\n  }\r\n  .content {\r\n    // font-size: 28rpx;\r\n    // line-height: 1.5;\r\n    // color: #333;\r\n  }\r\n}\r\n</style>\r\n", "import mod from \"-!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./noticeDetail.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./noticeDetail.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754188039885\n      var cssReload = require(\"D:/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}