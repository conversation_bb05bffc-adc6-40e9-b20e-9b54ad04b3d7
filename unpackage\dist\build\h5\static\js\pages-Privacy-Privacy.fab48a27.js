(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-Privacy-Privacy"],{"04fd":function(n,e,t){"use strict";t.r(e);var o=t("c5a6"),a=t.n(o);for(var u in o)["default"].indexOf(u)<0&&function(n){t.d(e,n,(function(){return o[n]}))}(u);e["default"]=a.a},"1b5b":function(n,e,t){var o=t("24fb");e=o(!1),e.push([n.i,"uni-page-body[data-v-3dc354eb]{padding:15px}",""]),n.exports=e},"411e":function(n,e,t){"use strict";t.d(e,"b",(function(){return o})),t.d(e,"c",(function(){return a})),t.d(e,"a",(function(){}));var o=function(){var n=this.$createElement,e=this._self._c||n;return e("v-uni-view",[e("v-uni-web-view",{attrs:{src:this.URL}})],1)},a=[]},"66ea":function(n,e,t){"use strict";var o=t("b509"),a=t.n(o);a.a},b509:function(n,e,t){var o=t("1b5b");o.__esModule&&(o=o.default),"string"===typeof o&&(o=[[n.i,o,""]]),o.locals&&(n.exports=o.locals);var a=t("4f06").default;a("39eecd1a",o,!0,{sourceMap:!1,shadowMode:!1})},c5a6:function(n,e,t){"use strict";t("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var o=t("30ea"),a=(t("9e56"),{data:function(){return{URL:o.privacyUrl}},onLoad:function(n){},onReady:function(){},onShow:function(){},onHide:function(){},onUnload:function(){},onPullDownRefresh:function(){},onReachBottom:function(){},onShareAppMessage:function(){},methods:{}});e.default=a},f1bf:function(n,e,t){"use strict";t.r(e);var o=t("411e"),a=t("04fd");for(var u in a)["default"].indexOf(u)<0&&function(n){t.d(e,n,(function(){return a[n]}))}(u);t("66ea");var i=t("f0c5"),c=Object(i["a"])(a["default"],o["b"],o["c"],!1,null,"3dc354eb",null,!1,o["a"],void 0);e["default"]=c.exports}}]);