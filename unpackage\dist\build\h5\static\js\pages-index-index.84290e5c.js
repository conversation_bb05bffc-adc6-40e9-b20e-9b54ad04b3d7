(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-index-index"],{"065a":function(t,e,i){var n=i("24fb");e=n(!1),e.push([t.i,'@charset "UTF-8";\r\n/* uni.scss */\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 字体变量 */\r\n/* 颜色变量 */\r\n/* 行为相关颜色 */\r\n/* 文字基本颜色 */\r\n/* 背景颜色 */\r\n/* 边框颜色 */\r\n/* 尺寸变量 */\r\n/* 文字尺寸 */\r\n/* 图片尺寸 */\r\n/* Border Radius */\r\n/* 水平间距 */\r\n/* 垂直间距 */\r\n/* 透明度 */\r\n/* 文章场景相关 */.u-swiper-wrap[data-v-133bb712]{position:relative;overflow:hidden;-webkit-transform:translateY(0);transform:translateY(0)}.u-swiper-image[data-v-133bb712]{width:100%;will-change:transform;height:100%;display:block;pointer-events:none}.u-swiper-indicator[data-v-133bb712]{padding:0 %?24?%;position:absolute;display:flex;flex-direction:row;width:100%;z-index:1}.u-indicator-item-rect[data-v-133bb712]{width:%?26?%;height:%?8?%;margin:0 %?6?%;transition:all .5s;background-color:rgba(0,0,0,.3)}.u-indicator-item-rect-active[data-v-133bb712]{background-color:hsla(0,0%,100%,.8)}.u-indicator-item-dot[data-v-133bb712]{width:%?14?%;height:%?14?%;margin:0 %?6?%;border-radius:%?20?%;transition:all .5s;background-color:rgba(0,0,0,.3)}.u-indicator-item-dot-active[data-v-133bb712]{background-color:hsla(0,0%,100%,.8)}.u-indicator-item-round[data-v-133bb712]{width:%?14?%;height:%?14?%;margin:0 %?6?%;border-radius:%?20?%;transition:all .5s;background-color:rgba(0,0,0,.3)}.u-indicator-item-round-active[data-v-133bb712]{width:%?34?%;background-color:hsla(0,0%,100%,.8)}.u-indicator-item-number[data-v-133bb712]{padding:%?6?% %?16?%;line-height:1;background-color:rgba(0,0,0,.3);border-radius:%?100?%;font-size:%?26?%;color:hsla(0,0%,100%,.8)}.u-list-scale[data-v-133bb712]{-webkit-transform-origin:center center;transform-origin:center center}.u-list-image-wrap[data-v-133bb712]{width:100%;height:100%;flex:1;transition:all .5s;overflow:hidden;box-sizing:initial;position:relative}.u-swiper-title[data-v-133bb712]{position:absolute;background-color:rgba(0,0,0,.3);bottom:0;left:0;width:100%;font-size:%?28?%;padding:%?12?% %?24?%;color:hsla(0,0%,100%,.9)}.u-swiper-item[data-v-133bb712]{display:flex;flex-direction:row;overflow:hidden;align-items:center}',""]),t.exports=e},"0bde":function(t,e,i){"use strict";var n=i("21e4"),r=i.n(n);r.a},"13d0":function(t,e,i){"use strict";i("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("a9e3"),i("c975");var n={name:"u-swiper",props:{list:{type:Array,default:function(){return[]}},title:{type:Boolean,default:!1},indicator:{type:Object,default:function(){return{}}},borderRadius:{type:[Number,String],default:8},interval:{type:[String,Number],default:3e3},mode:{type:String,default:"round"},height:{type:[Number,String],default:250},indicatorPos:{type:String,default:"bottomCenter"},effect3d:{type:Boolean,default:!1},effect3dPreviousMargin:{type:[Number,String],default:50},autoplay:{type:Boolean,default:!0},duration:{type:[Number,String],default:500},circular:{type:Boolean,default:!0},imgMode:{type:String,default:"aspectFill"},name:{type:String,default:"image"},bgColor:{type:String,default:"#f3f4f6"},current:{type:[Number,String],default:0},titleStyle:{type:Object,default:function(){return{}}}},watch:{list:function(t,e){t.length!==e.length&&(this.uCurrent=0)},current:function(t){this.uCurrent=t}},data:function(){return{uCurrent:this.current}},computed:{justifyContent:function(){return"topLeft"==this.indicatorPos||"bottomLeft"==this.indicatorPos?"flex-start":"topCenter"==this.indicatorPos||"bottomCenter"==this.indicatorPos?"center":"topRight"==this.indicatorPos||"bottomRight"==this.indicatorPos?"flex-end":void 0},titlePaddingBottom:function(){var t=0;return"none"==this.mode?"12rpx":(t=["bottomLeft","bottomCenter","bottomRight"].indexOf(this.indicatorPos)>=0&&"number"==this.mode?"60rpx":["bottomLeft","bottomCenter","bottomRight"].indexOf(this.indicatorPos)>=0&&"number"!=this.mode?"40rpx":"12rpx",t)},elCurrent:function(){return Number(this.current)}},methods:{listClick:function(t){this.$emit("click",t)},change:function(t){var e=t.detail.current;this.uCurrent=e,this.$emit("change",e)},animationfinish:function(t){}}};e.default=n},"1c3a":function(t,e,i){"use strict";i.d(e,"b",(function(){return n})),i.d(e,"c",(function(){return r})),i.d(e,"a",(function(){}));var n=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",{staticClass:"u-divider",style:{height:"auto"==t.height?"auto":t.height+"rpx",backgroundColor:t.bgColor,marginBottom:t.marginBottom+"rpx",marginTop:t.marginTop+"rpx"},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.click.apply(void 0,arguments)}}},[i("v-uni-view",{staticClass:"u-divider-line",class:[t.type?"u-divider-line--bordercolor--"+t.type:""],style:[t.lineStyle]}),t.useSlot?i("v-uni-view",{staticClass:"u-divider-text",style:{color:t.color,fontSize:t.fontSize+"rpx"}},[t._t("default")],2):t._e(),i("v-uni-view",{staticClass:"u-divider-line",class:[t.type?"u-divider-line--bordercolor--"+t.type:""],style:[t.lineStyle]})],1)},r=[]},"1efa":function(t,e,i){"use strict";i.r(e);var n=i("8c14"),r=i("8165");for(var a in r)["default"].indexOf(a)<0&&function(t){i.d(e,t,(function(){return r[t]}))}(a);i("e23e"),i("b50f");var o=i("f0c5"),s=Object(o["a"])(r["default"],n["b"],n["c"],!1,null,"47cfc34c",null,!1,n["a"],void 0);e["default"]=s.exports},"21e4":function(t,e,i){var n=i("065a");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var r=i("4f06").default;r("0e1c2d88",n,!0,{sourceMap:!1,shadowMode:!1})},"33c0":function(t,e,i){"use strict";i.r(e);var n=i("586e"),r=i.n(n);for(var a in n)["default"].indexOf(a)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(a);e["default"]=r.a},"357e":function(t,e,i){"use strict";i("7a82");var n=i("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("4d90"),i("99af");var r=n(i("c7eb")),a=n(i("1da1")),o=n(i("c8ed")),s=i("30ea"),c=i("9e56"),d={data:function(){return{number:o.default,bannerlist:[],loadStatus:"loading",loadText:{loadmore:"加载更多",loading:"努力加载中",nomore:"已经到底了"},goodsList:[],storeInfo:{},show:!1,tzGold:{},gyGold:{},timeGold:""}},onLoad:function(){},onShow:function(){var t=this;return(0,a.default)((0,r.default)().mark((function e(){var i;return(0,r.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return i=t,e.next=3,t.getIndexinfo();case 3:uni.getStorage({key:"store_info",success:function(t){i.storeInfo=t.data}});case 4:case"end":return e.stop()}}),e)})))()},methods:{goShop:function(t){uni.navigateTo({url:"/pages/indexChild/GoodsDetails/GoodsDetails?goodsId="+t.id})},location:function(){uni.navigateTo({url:"/pages/promotion/store"})},onSearch:function(){uni.navigateTo({url:"/pages/search/search"})},onGoldPrice:function(){this.show=!0},getIndexinfo:function(){var t=this;return(0,a.default)((0,r.default)().mark((function e(){var i;return(0,r.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,c.request(s.indexInfoUrl,{},"POST");case 2:i=e.sent,0!==i.code?uni.showToast({title:i.msg,icon:"none"}):(t.bannerlist=i.data.imgList,t.goodsList=i.data.goodsList,t.gyGold=i.data.gyGold,t.tzGold=i.data.tzGold,t.timeGold=t.getCurrentDateTime(),t.loadStatus="nomore");case 4:case"end":return e.stop()}}),e)})))()},getCurrentDateTime:function(){var t=new Date,e=t.getFullYear(),i=String(t.getMonth()+1).padStart(2,"0"),n=String(t.getDate()).padStart(2,"0"),r=String(t.getHours()).padStart(2,"0"),a=String(t.getMinutes()).padStart(2,"0"),o=String(t.getSeconds()).padStart(2,"0");return this.dateTime="".concat(e,"-").concat(i,"-").concat(n," ").concat(r,":").concat(a,":").concat(o),"".concat(e,"-").concat(i,"-").concat(n," ").concat(r,":").concat(a,":").concat(o)}}};e.default=d},"38ad":function(t,e,i){"use strict";i.r(e);var n=i("efc5"),r=i("4d11");for(var a in r)["default"].indexOf(a)<0&&function(t){i.d(e,t,(function(){return r[t]}))}(a);i("0bde");var o=i("f0c5"),s=Object(o["a"])(r["default"],n["b"],n["c"],!1,null,"133bb712",null,!1,n["a"],void 0);e["default"]=s.exports},"38cf":function(t,e,i){var n=i("23e7"),r=i("1148");n({target:"String",proto:!0},{repeat:r})},"4ad7":function(t,e,i){"use strict";var n=i("9af8"),r=i.n(n);r.a},"4bd9":function(t,e,i){var n=i("24fb");e=n(!1),e.push([t.i,'@charset "UTF-8";\r\n/* uni.scss */\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 字体变量 */\r\n/* 颜色变量 */\r\n/* 行为相关颜色 */\r\n/* 文字基本颜色 */\r\n/* 背景颜色 */\r\n/* 边框颜色 */\r\n/* 尺寸变量 */\r\n/* 文字尺寸 */\r\n/* 图片尺寸 */\r\n/* Border Radius */\r\n/* 水平间距 */\r\n/* 垂直间距 */\r\n/* 透明度 */\r\n/* 文章场景相关 */.u-waterfall[data-v-d7928fe4]{display:flex;flex-direction:row;flex-direction:row;align-items:flex-start}.u-column[data-v-d7928fe4]{display:flex;flex-direction:row;flex:1;flex-direction:column;height:auto}.u-image[data-v-d7928fe4]{width:100%}',""]),t.exports=e},"4d11":function(t,e,i){"use strict";i.r(e);var n=i("13d0"),r=i.n(n);for(var a in n)["default"].indexOf(a)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(a);e["default"]=r.a},"586e":function(t,e,i){"use strict";i("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("a9e3"),i("c975");var n={name:"u-divider",props:{halfWidth:{type:[Number,String],default:150},borderColor:{type:String,default:"#dcdfe6"},type:{type:String,default:"primary"},color:{type:String,default:"#909399"},fontSize:{type:[Number,String],default:26},bgColor:{type:String,default:"#ffffff"},height:{type:[Number,String],default:"auto"},marginTop:{type:[String,Number],default:0},marginBottom:{type:[String,Number],default:0},useSlot:{type:Boolean,default:!0}},computed:{lineStyle:function(){var t={};return-1!=String(this.halfWidth).indexOf("%")?t.width=this.halfWidth:t.width=this.halfWidth+"rpx",this.borderColor&&(t.borderColor=this.borderColor),t}},methods:{click:function(){this.$emit("click")}}};e.default=n},"663a":function(t,e,i){var n=i("24fb");e=n(!1),e.push([t.i,'@charset "UTF-8";\r\n/* uni.scss */\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 字体变量 */\r\n/* 颜色变量 */\r\n/* 行为相关颜色 */\r\n/* 文字基本颜色 */\r\n/* 背景颜色 */\r\n/* 边框颜色 */\r\n/* 尺寸变量 */\r\n/* 文字尺寸 */\r\n/* 图片尺寸 */\r\n/* Border Radius */\r\n/* 水平间距 */\r\n/* 垂直间距 */\r\n/* 透明度 */\r\n/* 文章场景相关 */.u-divider[data-v-f2b386da]{width:100%;position:relative;text-align:center;display:flex;flex-direction:row;justify-content:center;align-items:center;overflow:hidden;flex-direction:row}.u-divider-line[data-v-f2b386da]{border-bottom:1px solid #e4e7ed;-webkit-transform:scaleY(.5);transform:scaleY(.5);-webkit-transform-origin:center;transform-origin:center}.u-divider-line--bordercolor--primary[data-v-f2b386da]{border-color:#2979ff}.u-divider-line--bordercolor--success[data-v-f2b386da]{border-color:#19be6b}.u-divider-line--bordercolor--error[data-v-f2b386da]{border-color:#2979ff}.u-divider-line--bordercolor--info[data-v-f2b386da]{border-color:#909399}.u-divider-line--bordercolor--warning[data-v-f2b386da]{border-color:#f90}.u-divider-text[data-v-f2b386da]{white-space:nowrap;padding:0 %?16?%;display:inline-flex}',""]),t.exports=e},8165:function(t,e,i){"use strict";i.r(e);var n=i("357e"),r=i.n(n);for(var a in n)["default"].indexOf(a)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(a);e["default"]=r.a},"8c14":function(t,e,i){"use strict";i.d(e,"b",(function(){return r})),i.d(e,"c",(function(){return a})),i.d(e,"a",(function(){return n}));var n={uIcon:i("c0fb").default,uSwiper:i("38ad").default,uDivider:i("9710").default,uWaterfall:i("a121").default,uLoadmore:i("84c5").default,uPopup:i("24e3").default},r=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",{staticClass:"page"},[i("v-uni-view",{staticClass:"status_bar"},[i("v-uni-view",{staticClass:"location_info",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.location.apply(void 0,arguments)}}},[i("v-uni-view",[t._v(t._s(t.storeInfo.name||"祥杰金条专家"))]),i("v-uni-view",{staticStyle:{display:"flex"}},[i("v-uni-image",{staticStyle:{width:"32rpx",height:"32rpx"},attrs:{src:"/static/img/index/index_location.png",alt:""}})],1)],1),i("v-uni-view",{staticClass:"search_icon",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.onSearch.apply(void 0,arguments)}}},[i("u-icon",{attrs:{size:"32",name:"/static/img/index/search-normal.png"}})],1)],1),i("v-uni-view",{staticClass:"index-concent-item"},[i("u-swiper",{attrs:{borderRadius:"0",height:868,name:"imgUrl",list:t.bannerlist,mode:"none"}})],1),i("v-uni-view",{staticStyle:{padding:"30rpx 0 6rpx 0"}},[i("u-divider",{attrs:{"half-width":"260","border-color":"#BBA186","bg-color":"#F5F5F5"}},[i("v-uni-image",{staticStyle:{width:"128rpx",height:"34rpx"},attrs:{mode:"widthFix",src:"/static/img/index/index_tuijian.png",alt:"",srcset:""}})],1)],1),i("v-uni-view",{staticClass:"shop_content"},[i("u-waterfall",{scopedSlots:t._u([{key:"left",fn:function(e){var n=e.leftList;return t._l(n,(function(e,n){return i("v-uni-view",{key:n,staticClass:"shop_list",on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.goShop(e)}}},[i("v-uni-image",{staticStyle:{width:"100%",height:"340rpx"},attrs:{src:e.goodsImg,alt:""}}),i("v-uni-view",{staticClass:"demo-title text-ellipsis_2"},[t._v(t._s(e.goodsName))]),i("v-uni-view",{staticClass:"shop-price"},[i("v-uni-view",{staticClass:"shop-price-num"},[i("v-uni-view",[i("v-uni-text",[t._v("¥")]),i("v-uni-text",{staticStyle:{"font-size":"32rpx"}},[t._v(t._s(e.price))]),i("v-uni-text",{staticStyle:{"font-weight":"400",color:"#9FA3B0","margin-left":"10rpx"}},[t._v(t._s(e.shop))])],1)],1),i("v-uni-view",{staticClass:"shop_add"},[i("v-uni-image",{staticStyle:{width:"32rpx",height:"32rpx"},attrs:{src:"/static/img/index/index-add-circle.png",alt:"",srcset:""}})],1)],1)],1)}))}},{key:"right",fn:function(e){var n=e.rightList;return t._l(n,(function(e,n){return i("v-uni-view",{key:n,staticClass:"shop_list",on:{click:function(i){arguments[0]=i=t.$handleEvent(i),t.goShop(e)}}},[i("v-uni-image",{staticStyle:{width:"100%",height:"340rpx"},attrs:{src:e.goodsImg,alt:""}}),i("v-uni-view",{staticClass:"demo-title text-ellipsis_2"},[t._v(t._s(e.goodsName))]),i("v-uni-view",{staticClass:"shop-price"},[i("v-uni-view",{staticClass:"shop-price-num"},[i("v-uni-view",[i("v-uni-text",[t._v("¥")]),i("v-uni-text",{staticStyle:{"font-size":"32rpx"}},[t._v(t._s(e.price))]),i("v-uni-text",{staticStyle:{"font-weight":"400",color:"#9FA3B0","margin-left":"10rpx"}},[t._v(t._s(e.shop))])],1)],1),i("v-uni-view",{staticClass:"shop_add"},[i("v-uni-image",{staticStyle:{width:"32rpx",height:"32rpx"},attrs:{src:"/static/img/index/index-add-circle.png",alt:"",srcset:""}})],1)],1)],1)}))}}]),model:{value:t.goodsList,callback:function(e){t.goodsList=e},expression:"goodsList"}})],1),i("u-loadmore",{attrs:{status:t.loadStatus,"load-text":t.loadText},on:{loadmore:function(e){arguments[0]=e=t.$handleEvent(e),t.addRandomData.apply(void 0,arguments)}}}),i("v-uni-view",{staticClass:"page_bottom"}),i("v-uni-view",{staticClass:"gold-price",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.onGoldPrice.apply(void 0,arguments)}}},[i("v-uni-view",{staticStyle:{display:"flex","justify-content":"center"}},[i("v-uni-image",{staticStyle:{width:"36rpx",height:"36rpx"},attrs:{src:"/static/img/index/index-gold-price.png",alt:"",srcset:""}})],1),i("v-uni-view",{staticStyle:{"font-size":"24rpx","text-align":"center","margin-top":"4rpx"}},[t._v("金价")])],1),i("u-popup",{attrs:{mode:"center","border-radius":14,length:"90%","close-icon-color":"#FFFFFF",closeable:!0},model:{value:t.show,callback:function(e){t.show=e},expression:"show"}},[i("v-uni-view",[i("v-uni-view",{staticClass:"gold_new"},[i("v-uni-view",{staticClass:"gold_new_title"},[t._v("今日金价")]),i("v-uni-view",[t._v("更新于 "+t._s(t.timeGold))])],1),i("v-uni-view",{staticStyle:{padding:"0 32rpx"}},[i("v-uni-view",{staticClass:"gold_price_show"},[i("v-uni-view",{staticStyle:{"font-size":"28rpx"}},[t._v(t._s(t.gyGold.remarks))]),i("v-uni-view",[i("v-uni-text",{staticStyle:{"font-size":"32rpx",color:"#BBA186"}},[t._v(t._s(t.gyGold.configValue))]),i("v-uni-text",{staticStyle:{"font-size":"24rpx","margin-left":"4rpx"}},[t._v("元/克")])],1)],1),i("v-uni-view",{staticClass:"gold_price_show"},[i("v-uni-view",{staticStyle:{"font-size":"28rpx"}},[t._v(t._s(t.tzGold.remarks))]),i("v-uni-view",[i("v-uni-text",{staticStyle:{"font-size":"32rpx",color:"#BBA186"}},[t._v(t._s(t.tzGold.configValue))]),i("v-uni-text",{staticStyle:{"font-size":"24rpx","margin-left":"4rpx"}},[t._v("元/克")])],1)],1)],1),i("v-uni-view",{staticClass:"goods_detail_footer"},[i("v-uni-view",{on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.show=!1}}},[t._v("我知道了")])],1)],1)],1)],1)},a=[]},"8f12":function(t,e,i){"use strict";var n=i("e324"),r=i.n(n);r.a},9710:function(t,e,i){"use strict";i.r(e);var n=i("1c3a"),r=i("33c0");for(var a in r)["default"].indexOf(a)<0&&function(t){i.d(e,t,(function(){return r[t]}))}(a);i("8f12");var o=i("f0c5"),s=Object(o["a"])(r["default"],n["b"],n["c"],!1,null,"f2b386da",null,!1,n["a"],void 0);e["default"]=s.exports},9944:function(t,e,i){var n=i("c69b");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var r=i("4f06").default;r("102f152e",n,!0,{sourceMap:!1,shadowMode:!1})},"9af8":function(t,e,i){var n=i("4bd9");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var r=i("4f06").default;r("5b37dc48",n,!0,{sourceMap:!1,shadowMode:!1})},a121:function(t,e,i){"use strict";i.r(e);var n=i("ba83"),r=i("a632");for(var a in r)["default"].indexOf(a)<0&&function(t){i.d(e,t,(function(){return r[t]}))}(a);i("4ad7");var o=i("f0c5"),s=Object(o["a"])(r["default"],n["b"],n["c"],!1,null,"d7928fe4",null,!1,n["a"],void 0);e["default"]=s.exports},a632:function(t,e,i){"use strict";i.r(e);var n=i("f6a8"),r=i.n(n);for(var a in n)["default"].indexOf(a)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(a);e["default"]=r.a},b50f:function(t,e,i){"use strict";var n=i("f4f0"),r=i.n(n);r.a},ba83:function(t,e,i){"use strict";i.d(e,"b",(function(){return n})),i.d(e,"c",(function(){return r})),i.d(e,"a",(function(){}));var n=function(){var t=this.$createElement,e=this._self._c||t;return e("v-uni-view",{staticClass:"u-waterfall"},[e("v-uni-view",{staticClass:"u-column",attrs:{id:"u-left-column"}},[this._t("left",null,{leftList:this.leftList})],2),e("v-uni-view",{staticClass:"u-column",attrs:{id:"u-right-column"}},[this._t("right",null,{rightList:this.rightList})],2)],1)},r=[]},c69b:function(t,e,i){var n=i("24fb");e=n(!1),e.push([t.i,"uni-page-body[data-v-47cfc34c]{background-color:#f9f5f2}body.?%PAGE?%[data-v-47cfc34c]{background-color:#f9f5f2}",""]),t.exports=e},c8ed:function(t,e,i){"use strict";i("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,i("d401"),i("d3b7"),i("25f0"),i("c975"),i("fb6a"),i("38cf"),i("99af");var n={parsePrice:function(t){if(t||0===t){var e=t.toString(),i=e.indexOf(".");return-1!==i&&e.length-i===3?e:-1!==i?e.length-i===2?e+"0":e.slice(0,i+3):e+".00"}return""},hideMiddleDigits:function(t){if(!t||"string"!==typeof t)return"";var e=t.slice(0,3)+"*".repeat(4),i=t.slice(7);return"".concat(e).concat(i)},oneparsePrice:function(t){if(null!=t&&""!==t){var e=t.toString(),i=e.indexOf(".");if(-1!==i){var n=e.length-i-1;return 1===n?e:n>1?e.slice(0,i+2):e+"0"}return e+".0"}return""}};e.default=n},e23e:function(t,e,i){"use strict";var n=i("9944"),r=i.n(n);r.a},e324:function(t,e,i){var n=i("663a");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var r=i("4f06").default;r("eec325a4",n,!0,{sourceMap:!1,shadowMode:!1})},efc5:function(t,e,i){"use strict";i.d(e,"b",(function(){return n})),i.d(e,"c",(function(){return r})),i.d(e,"a",(function(){}));var n=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("v-uni-view",{staticClass:"u-swiper-wrap",style:{borderRadius:t.borderRadius+"rpx"}},[i("v-uni-swiper",{style:{height:t.height+"rpx",backgroundColor:t.bgColor},attrs:{current:t.elCurrent,interval:t.interval,circular:t.circular,duration:t.duration,autoplay:t.autoplay,"previous-margin":t.effect3d?t.effect3dPreviousMargin+"rpx":"0","next-margin":t.effect3d?t.effect3dPreviousMargin+"rpx":"0"},on:{change:function(e){arguments[0]=e=t.$handleEvent(e),t.change.apply(void 0,arguments)},animationfinish:function(e){arguments[0]=e=t.$handleEvent(e),t.animationfinish.apply(void 0,arguments)}}},t._l(t.list,(function(e,n){return i("v-uni-swiper-item",{key:n,staticClass:"u-swiper-item"},[i("v-uni-view",{staticClass:"u-list-image-wrap",class:[t.uCurrent!=n?"u-list-scale":""],style:{borderRadius:t.borderRadius+"rpx",transform:t.effect3d&&t.uCurrent!=n?"scaleY(0.9)":"scaleY(1)",margin:t.effect3d&&t.uCurrent!=n?"0 20rpx":0},on:{click:function(e){e.stopPropagation(),e.preventDefault(),arguments[0]=e=t.$handleEvent(e),t.listClick(n)}}},[i("v-uni-image",{staticClass:"u-swiper-image",attrs:{src:e[t.name]||e,mode:t.imgMode}}),t.title&&e.title?i("v-uni-view",{staticClass:"u-swiper-title u-line-1",style:[{"padding-bottom":t.titlePaddingBottom},t.titleStyle]},[t._v(t._s(e.title))]):t._e()],1)],1)})),1),i("v-uni-view",{staticClass:"u-swiper-indicator",style:{top:"topLeft"==t.indicatorPos||"topCenter"==t.indicatorPos||"topRight"==t.indicatorPos?"12rpx":"auto",bottom:"bottomLeft"==t.indicatorPos||"bottomCenter"==t.indicatorPos||"bottomRight"==t.indicatorPos?"12rpx":"auto",justifyContent:t.justifyContent,padding:"0 "+(t.effect3d?"74rpx":"24rpx")}},["rect"==t.mode?t._l(t.list,(function(e,n){return i("v-uni-view",{key:n,staticClass:"u-indicator-item-rect",class:{"u-indicator-item-rect-active":n==t.uCurrent}})})):t._e(),"dot"==t.mode?t._l(t.list,(function(e,n){return i("v-uni-view",{key:n,staticClass:"u-indicator-item-dot",class:{"u-indicator-item-dot-active":n==t.uCurrent}})})):t._e(),"round"==t.mode?t._l(t.list,(function(e,n){return i("v-uni-view",{key:n,staticClass:"u-indicator-item-round",class:{"u-indicator-item-round-active":n==t.uCurrent}})})):t._e(),"number"==t.mode?[i("v-uni-view",{staticClass:"u-indicator-item-number"},[t._v(t._s(t.uCurrent+1)+"/"+t._s(t.list.length))])]:t._e()],2)],1)},r=[]},f216:function(t,e,i){var n=i("24fb");e=n(!1),e.push([t.i,'@charset "UTF-8";\r\n/* uni.scss */\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 字体变量 */\r\n/* 颜色变量 */\r\n/* 行为相关颜色 */\r\n/* 文字基本颜色 */\r\n/* 背景颜色 */\r\n/* 边框颜色 */\r\n/* 尺寸变量 */\r\n/* 文字尺寸 */\r\n/* 图片尺寸 */\r\n/* Border Radius */\r\n/* 水平间距 */\r\n/* 垂直间距 */\r\n/* 透明度 */\r\n/* 文章场景相关 */.page[data-v-47cfc34c]{padding-bottom:%?20?%}.status_bar[data-v-47cfc34c]{position:absolute;z-index:5;left:0;top:0;right:0;padding:%?24?% %?24?% %?10?% %?24?%;font-size:%?28?%;color:#171b25;display:flex;align-items:center;justify-content:space-between}.status_bar .location_info[data-v-47cfc34c]{display:flex;align-items:center;height:32px;padding:5px 12px;gap:4px;border-radius:999px;background:hsla(0,0%,100%,.3);color:#fff;font-size:%?24?%;-webkit-backdrop-filter:blur(7.5px);backdrop-filter:blur(7.5px);opacity:1;transition:opacity .3s ease-in}.status_bar .search_icon[data-v-47cfc34c]{width:%?64?%;height:%?64?%;border-radius:50%;display:flex;justify-items:center;justify-content:center;background:hsla(0,0%,100%,.3)}.shop_content[data-v-47cfc34c]{padding-left:%?24?%}.shop_content .shop_list[data-v-47cfc34c]{border-radius:%?10?% %?10?% 0 0;overflow:hidden;background-color:#fff;margin:%?24?% 0;margin-right:%?24?%}.shop_content .shop_list .demo-title[data-v-47cfc34c]{font-size:%?28?%;margin:%?16?%;color:#171b25}.shop_content .shop_list .shop-price[data-v-47cfc34c]{display:flex;justify-content:space-between;align-items:center;padding:%?10?% %?24?% %?20?% %?24?%}.shop_content .shop_list .shop-price .shop-price-num[data-v-47cfc34c]{display:flex;align-items:center;color:#ff0046;font-size:%?24?%;font-weight:600}.shop_content .shop_list .shop-price .shop_add[data-v-47cfc34c]{display:flex}.gold-price[data-v-47cfc34c]{position:fixed;top:60vh;right:%?24?%;width:%?88?%;height:%?88?%;padding:%?10?% 0;flex-direction:column;justify-content:center;flex-shrink:0;border-radius:8px;background:#fff;box-shadow:0 4px 10px 0 rgba(0,0,0,.10196078431372549)}.gold_new[data-v-47cfc34c]{padding:%?40?% 0;background:linear-gradient(94deg,#8f8174 -2.53%,#c4b39f 131.45%)}.gold_new uni-view[data-v-47cfc34c]{color:#fff;text-align:center;font-size:%?24?%}.gold_new .gold_new_title[data-v-47cfc34c]{font-size:%?36?%;padding-bottom:%?20?%}.gold_price_show[data-v-47cfc34c]{display:flex;justify-content:space-between;padding:%?24?% 0;border-bottom:%?1?% solid #f1f2f5}.goods_detail_footer[data-v-47cfc34c]{margin-top:%?32?%;width:100%;height:50px;display:flex;justify-content:center}.goods_detail_footer > uni-view[data-v-47cfc34c]{height:%?84?%;width:80%;border-radius:9999px;background-color:#bba186;color:#fff;font-size:%?28?%;text-align:center;line-height:%?84?%}',""]),t.exports=e},f4f0:function(t,e,i){var n=i("f216");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[t.i,n,""]]),n.locals&&(t.exports=n.locals);var r=i("4f06").default;r("29720686",n,!0,{sourceMap:!1,shadowMode:!1})},f6a8:function(t,e,i){"use strict";i("7a82");var n=i("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var r=n(i("c7eb")),a=n(i("1da1"));i("a9e3"),i("99af"),i("fb6a"),i("14d9"),i("a434"),i("e9c4"),i("c740");var o={name:"u-waterfall",props:{value:{type:Array,required:!0,default:function(){return[]}},addTime:{type:[Number,String],default:200},idKey:{type:String,default:"id"}},data:function(){return{leftList:[],rightList:[],tempList:[],children:[]}},watch:{copyFlowList:function(t,e){var i=Array.isArray(e)&&e.length>0?e.length:0;this.tempList=this.tempList.concat(this.cloneData(t.slice(i))),this.splitData()}},mounted:function(){this.tempList=this.cloneData(this.copyFlowList),this.splitData()},computed:{copyFlowList:function(){return this.cloneData(this.value)}},methods:{splitData:function(){var t=this;return(0,a.default)((0,r.default)().mark((function e(){var i,n,a;return(0,r.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(t.tempList.length){e.next=2;break}return e.abrupt("return");case 2:return e.next=4,t.$uGetRect("#u-left-column");case 4:return i=e.sent,e.next=7,t.$uGetRect("#u-right-column");case 7:if(n=e.sent,a=t.tempList[0],a){e.next=11;break}return e.abrupt("return");case 11:i.height<n.height?t.leftList.push(a):i.height>n.height?t.rightList.push(a):t.leftList.length<=t.rightList.length?t.leftList.push(a):t.rightList.push(a),t.tempList.splice(0,1),t.tempList.length&&setTimeout((function(){t.splitData()}),t.addTime);case 14:case"end":return e.stop()}}),e)})))()},cloneData:function(t){return JSON.parse(JSON.stringify(t))},clear:function(){this.leftList=[],this.rightList=[],this.$emit("input",[]),this.tempList=[]},remove:function(t){var e=this,i=-1;i=this.leftList.findIndex((function(i){return i[e.idKey]==t})),-1!=i?this.leftList.splice(i,1):(i=this.rightList.findIndex((function(i){return i[e.idKey]==t})),-1!=i&&this.rightList.splice(i,1)),i=this.value.findIndex((function(i){return i[e.idKey]==t})),-1!=i&&this.$emit("input",this.value.splice(i,1))},modify:function(t,e,i){var n=this,r=-1;if(r=this.leftList.findIndex((function(e){return e[n.idKey]==t})),-1!=r?this.leftList[r][e]=i:(r=this.rightList.findIndex((function(e){return e[n.idKey]==t})),-1!=r&&(this.rightList[r][e]=i)),r=this.value.findIndex((function(e){return e[n.idKey]==t})),-1!=r){var a=this.cloneData(this.value);a[r][e]=i,this.$emit("input",a)}}}};e.default=o}}]);