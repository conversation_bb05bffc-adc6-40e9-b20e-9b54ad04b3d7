{"version": 3, "sources": ["webpack:///D:/work/IdeaProjects/test/pos2_hhlm/uni_modules/uv-input/components/uv-input/uv-input.vue?2b51", "webpack:///D:/work/IdeaProjects/test/pos2_hhlm/uni_modules/uv-input/components/uv-input/uv-input.vue?c9d7", "webpack:///D:/work/IdeaProjects/test/pos2_hhlm/uni_modules/uv-input/components/uv-input/uv-input.vue?8ec8", "webpack:///D:/work/IdeaProjects/test/pos2_hhlm/uni_modules/uv-input/components/uv-input/uv-input.vue?f337", "uni-app:///uni_modules/uv-input/components/uv-input/uv-input.vue", "webpack:///D:/work/IdeaProjects/test/pos2_hhlm/uni_modules/uv-input/components/uv-input/uv-input.vue?1f42", "webpack:///D:/work/IdeaProjects/test/pos2_hhlm/uni_modules/uv-input/components/uv-input/uv-input.vue?2c89"], "names": ["name", "mixins", "data", "innerValue", "focused", "innerFormatter", "created", "watch", "value", "modelValue", "computed", "isShowClear", "readonly", "inputClass", "border", "disabled", "shape", "classes", "wrapperStyle", "style", "inputStyle", "color", "fontSize", "textAlign", "methods", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "onInput", "onBlur", "onFocus", "onConfirm", "onkeyboardheightchange", "valueChange", "onClear", "clickHandler"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAiI;AACjI;AAC4D;AACL;AACsC;;;AAG7F;AACwL;AACxL,gBAAgB,sLAAU;AAC1B,EAAE,8EAAM;AACR,EAAE,+FAAM;AACR,EAAE,wGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,mGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,oSAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC5CA;AAAA;AAAA;AAAA;AAA8sB,CAAgB,+pBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;ACyEluB;AACA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAzCA,eA0CA;EACAA;EACAC;EACAC;IACA;MACA;MACAC;MACA;MACAC;MACA;MACAC;QAAA;MAAA;IACA;EACA;EACAC;IAEA;EAKA;EACAC;IACAC;MACA;IACA;IACAC;MACA;IACA;EACA;EACAC;IACA;IACAC;MACA;QAAAC;QAAAR;QAAAD;MACA;IACA;IACA;IACAU;MACA;QACAC;QAAAC;QAAAC;MACAF,0BACAG;MACAA;MACAH,wBACAG,0BACA,oBACA,sBACA;MACA;IACA;IACA;IACAC;MACA;MACA;MACA;QACAC;MACA;MACA;MACA;QACAA;MACA;QACA;QACAA;QACAA;QACAA;QACAA;MACA;MACA;IACA;IACA;IACAC;MACA;QACAC;QACAC;QACAC;MACA;MAEA;QACAJ;MACA;MAEA;IACA;EACA;EACAK;IACA;IACAC;MACA;IACA;IACA;IACAC;MAAA;MACA;QAAA;QAAAlB;MACA;MACA;MACA;MACA;MACA;MACA;QACA;QACA;MACA;IACA;IACA;IACAmB;MAAA;MACA;MACA;MACA;MACA;QACA;MACA;MACA;MACA;IACA;IACA;IACAC;MACA;MACA;IACA;IACA;IACAC;MACA;IACA;IACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MAAA;MACA;MACA;MACA;QACA;QACA;QACA;QACA;QACA;MACA;IACA;IACA;IACAC;MAAA;MACA;MACA;MACA;QACA;MACA;MACA;QACA;QACA;MACA;IACA;IACA;AACA;AACA;AACA;AACA;IACAC,uCASA;EACA;AACA;AAAA,2B;;;;;;;;;;;;AC5RA;AAAA;AAAA;AAAA;AAAq2C,CAAgB,0sCAAG,EAAC,C;;;;;;;;;;;ACAz3C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "uni_modules/uv-input/components/uv-input/uv-input.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./uv-input.vue?vue&type=template&id=34be0c5c&scoped=true&\"\nvar renderjs\nimport script from \"./uv-input.vue?vue&type=script&lang=js&\"\nexport * from \"./uv-input.vue?vue&type=script&lang=js&\"\nimport style0 from \"./uv-input.vue?vue&type=style&index=0&id=34be0c5c&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../../../../../software/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"34be0c5c\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"uni_modules/uv-input/components/uv-input/uv-input.vue\"\nexport default component.exports", "export * from \"-!../../../../../../../../software/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../../../../../software/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../../../../../software/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../../../../../software/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../../../../../software/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../software/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uv-input.vue?vue&type=template&id=34be0c5c&scoped=true&\"", "var components\ntry {\n  components = {\n    uvIcon: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uv-icon/components/uv-icon/uv-icon\" */ \"@/uni_modules/uv-icon/components/uv-icon/uv-icon.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var s0 = _vm.__get_style([_vm.wrapperStyle])\n  var s1 = _vm.__get_style([_vm.inputStyle])\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        s0: s0,\n        s1: s1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../../../../../software/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../software/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../software/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../software/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../software/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uv-input.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../../../../../software/HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../../../../../software/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../../../../../software/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../../../../../software/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../software/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uv-input.vue?vue&type=script&lang=js&\"", "<template>\r\n    <view class=\"uv-input\" :class=\"inputClass\" :style=\"[wrapperStyle]\">\r\n      <view class=\"uv-input__content\">\r\n        <view class=\"uv-input__content__prefix-icon\">\r\n          <slot name=\"prefix\">\r\n            <uv-icon\r\n\t\t\t\t\t\t\tv-if=\"prefixIcon\"\r\n              :name=\"prefixIcon\"\r\n              size=\"18\"\r\n              :customStyle=\"prefixIconStyle\"\r\n            ></uv-icon>\r\n          </slot>\r\n        </view>\r\n        <view class=\"uv-input__content__field-wrapper\" @click=\"clickHandler\">\r\n\t\t\t\t<!-- 根据uni-app的input组件文档，H5和APP中只要声明了password参数(无论true还是false)，type均失效，此时\r\n\t\t\t\t为了防止type=number时，又存在password属性，type无效，此时需要设置password为undefined\r\n\t\t\t -->\r\n        \t<input\r\n        \t  class=\"uv-input__content__field-wrapper__field\"\r\n        \t  :style=\"[inputStyle]\"\r\n        \t  :type=\"type\"\r\n        \t  :focus=\"focus\"\r\n        \t  :cursor=\"cursor\"\r\n        \t  :value=\"innerValue\"\r\n        \t  :auto-blur=\"autoBlur\"\r\n        \t  :disabled=\"disabled || readonly\"\r\n        \t  :maxlength=\"maxlength\"\r\n        \t  :placeholder=\"placeholder\"\r\n        \t  :placeholder-style=\"placeholderStyle\"\r\n        \t  :placeholder-class=\"placeholderClass\"\r\n        \t  :confirm-type=\"confirmType\"\r\n        \t  :confirm-hold=\"confirmHold\"\r\n        \t  :hold-keyboard=\"holdKeyboard\"\r\n        \t  :cursor-spacing=\"cursorSpacing\"\r\n        \t  :adjust-position=\"adjustPosition\"\r\n        \t  :selection-end=\"selectionEnd\"\r\n        \t  :selection-start=\"selectionStart\"\r\n        \t  :password=\"password || type === 'password' || undefined\"\r\n            :ignoreCompositionEvent=\"ignoreCompositionEvent\"\r\n        \t  @input=\"onInput\"\r\n        \t  @blur=\"onBlur\"\r\n        \t  @focus=\"onFocus\"\r\n        \t  @confirm=\"onConfirm\"\r\n        \t  @keyboardheightchange=\"onkeyboardheightchange\"\r\n        \t/>\r\n        </view>\r\n        <view\r\n          class=\"uv-input__content__clear\"\r\n          v-if=\"isShowClear\"\r\n          @tap=\"onClear\"\r\n        >\r\n          <uv-icon\r\n            name=\"close\"\r\n            size=\"11\"\r\n            color=\"#ffffff\"\r\n            customStyle=\"line-height: 12px\"\r\n          ></uv-icon>\r\n        </view>\r\n        <view class=\"uv-input__content__subfix-icon\">\r\n          <slot name=\"suffix\">\r\n            <uv-icon\r\n\t\t\t\t\t\t\tv-if=\"suffixIcon\"\r\n              :name=\"suffixIcon\"\r\n              size=\"18\"\r\n              :customStyle=\"suffixIconStyle\"\r\n            ></uv-icon>\r\n          </slot>\r\n        </view>\r\n      </view>\r\n    </view>\r\n</template>\r\n\r\n<script>\r\n\timport mpMixin from '@/uni_modules/uv-ui-tools/libs/mixin/mpMixin.js'\r\n\timport mixin from '@/uni_modules/uv-ui-tools/libs/mixin/mixin.js'\r\n\timport props from \"./props.js\";\r\n\t/**\r\n\t * Input 输入框\r\n\t * @description  此组件为一个输入框，默认没有边框和样式，是专门为配合表单组件uv-form而设计的，利用它可以快速实现表单验证，输入内容，下拉选择等功能。\r\n\t * @tutorial https://www.uvui.cn/components/input.html\r\n\t * @property {String | Number}\tvalue\t\t\t\t\t输入的值\r\n\t * @property {String}\t\t\ttype\t\t\t\t\t输入框类型，见上方说明 （ 默认 'text' ）\r\n\t * @property {Boolean}\t\t\tfixed\t\t\t\t\t如果 textarea 是在一个 position:fixed 的区域，需要显示指定属性 fixed 为 true，兼容性：微信小程序、百度小程序、字节跳动小程序、QQ小程序 （ 默认 false ）\r\n\t * @property {Boolean}\t\t\tdisabled\t\t\t\t是否禁用输入框 （ 默认 false ）\r\n\t * @property {String}\t\t\tdisabledColor\t\t\t禁用状态时的背景色（ 默认 '#f5f7fa' ）\r\n\t * @property {Boolean}\t\t\tclearable\t\t\t\t是否显示清除控件 （ 默认 false ）\r\n\t * @property {Boolean}\t\t\tpassword\t\t\t\t是否密码类型 （ 默认 false ）\r\n\t * @property {String | Number}\tmaxlength\t\t\t\t最大输入长度，设置为 -1 的时候不限制最大长度 （ 默认 -1 ）\r\n\t * @property {String}\t\t\tplaceholder\t\t\t\t输入框为空时的占位符\r\n\t * @property {String}\t\t\tplaceholderClass\t\t指定placeholder的样式类，注意页面或组件的style中写了scoped时，需要在类名前写/deep/ （ 默认 'input-placeholder' ）\r\n\t * @property {String | Object}\tplaceholderStyle\t\t指定placeholder的样式，字符串/对象形式，如\"color: red;\"\r\n\t * @property {Boolean}\t\t\tshowWordLimit\t\t\t是否显示输入字数统计，只在 type =\"text\"或type =\"textarea\"时有效 （ 默认 false ）\r\n\t * @property {String}\t\t\tconfirmType\t\t\t\t设置右下角按钮的文字，兼容性详见uni-app文档 （ 默认 'done' ）\r\n\t * @property {Boolean}\t\t\tconfirmHold\t\t\t\t点击键盘右下角按钮时是否保持键盘不收起，H5无效 （ 默认 false ）\r\n\t * @property {Boolean}\t\t\tholdKeyboard\t\t\tfocus时，点击页面的时候不收起键盘，微信小程序有效 （ 默认 false ）\r\n\t * @property {Boolean}\t\t\tfocus\t\t\t\t\t自动获取焦点，在 H5 平台能否聚焦以及软键盘是否跟随弹出，取决于当前浏览器本身的实现。nvue 页面不支持，需使用组件的 focus()、blur() 方法控制焦点 （ 默认 false ）\r\n\t * @property {Boolean}\t\t\tautoBlur\t\t\t\t键盘收起时，是否自动失去焦点，目前仅App3.0.0+有效 （ 默认 false ）\r\n\t * @property {Boolean}\t\t\tdisableDefaultPadding\t是否去掉 iOS 下的默认内边距，仅微信小程序，且type=textarea时有效 （ 默认 false ）\r\n\t * @property {String ｜ Number}\tcursor\t\t\t\t\t指定focus时光标的位置（ 默认 -1 ）\r\n\t * @property {String ｜ Number}\tcursorSpacing\t\t\t输入框聚焦时底部与键盘的距离 （ 默认 30 ）\r\n\t * @property {String ｜ Number}\tselectionStart\t\t\t光标起始位置，自动聚集时有效，需与selection-end搭配使用 （ 默认 -1 ）\r\n\t * @property {String ｜ Number}\tselectionEnd\t\t\t光标结束位置，自动聚集时有效，需与selection-start搭配使用 （ 默认 -1 ）\r\n\t * @property {Boolean}\t\t\tadjustPosition\t\t\t键盘弹起时，是否自动上推页面 （ 默认 true ）\r\n\t * @property {String}\t\t\tinputAlign\t\t\t\t输入框内容对齐方式（ 默认 'left' ）\r\n\t * @property {String | Number}\tfontSize\t\t\t\t输入框字体的大小 （ 默认 '15px' ）\r\n\t * @property {String}\t\t\tcolor\t\t\t\t\t输入框字体颜色\t（ 默认 '#303133' ）\r\n\t * @property {Function}\t\t\tformatter\t\t\t    内容式化函数\r\n\t * @property {String}\t\t\tprefixIcon\t\t\t\t输入框前置图标\r\n\t * @property {String | Object}\tprefixIconStyle\t\t\t前置图标样式，对象或字符串\r\n\t * @property {String}\t\t\tsuffixIcon\t\t\t\t输入框后置图标\r\n\t * @property {String | Object}\tsuffixIconStyle\t\t\t后置图标样式，对象或字符串\r\n\t * @property {String}\t\t\tborder\t\t\t\t\t边框类型，surround-四周边框，bottom-底部边框，none-无边框 （ 默认 'surround' ）\r\n\t * @property {Boolean}\t\t\treadonly\t\t\t\t是否只读，与disabled不同之处在于disabled会置灰组件，而readonly则不会 （ 默认 false ）\r\n\t * @property {String}\t\t\tshape\t\t\t\t\t输入框形状，circle-圆形，square-方形 （ 默认 'square' ）\r\n\t * @property {Object}\t\t\tcustomStyle\t\t\t\t定义需要用到的外部样式\r\n\t * @property {Boolean}\t\t\tignoreCompositionEvent\t是否忽略组件内对文本合成系统事件的处理。\r\n\t * @example <uv-input v-model=\"value\" :password=\"true\" suffix-icon=\"lock-fill\" />\r\n\t */\r\n\texport default {\r\n\t\tname: \"uv-input\",\r\n\t\tmixins: [mpMixin, mixin, props],\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\t// 输入框的值\r\n\t\t\t\tinnerValue: \"\",\r\n\t\t\t\t// 是否处于获得焦点状态\r\n\t\t\t\tfocused: false,\r\n\t\t\t\t// 过滤处理方法\r\n\t\t\t\tinnerFormatter: value => value\r\n\t\t\t}\r\n\t\t},\r\n\t\tcreated() {\r\n\t\t\t// #ifdef VUE2\r\n\t\t\tthis.innerValue = this.value;\r\n\t\t\t// #endif\r\n\t\t\t// #ifdef VUE3\r\n\t\t\tthis.innerValue = this.modelValue;\r\n\t\t\t// #endif\r\n\t\t},\r\n\t\twatch: {\r\n\t\t\tvalue(newVal){\r\n\t\t\t\tthis.innerValue = newVal;\r\n\t\t\t},\r\n\t\t\tmodelValue(newVal){\r\n\t\t\t\tthis.innerValue = newVal;\r\n\t\t\t}\r\n\t\t},\r\n\t\tcomputed: {\r\n\t\t\t// 是否显示清除控件\r\n\t\t\tisShowClear() {\r\n\t\t\t\tconst { clearable, readonly, focused, innerValue } = this;\r\n\t\t\t\treturn !!clearable && !readonly && !!focused && innerValue !== \"\";\r\n\t\t\t},\r\n\t\t\t// 组件的类名\r\n\t\t\tinputClass() {\r\n\t\t\t\tlet classes = [],\r\n\t\t\t\t\t{ border, disabled, shape } = this;\r\n\t\t\t\tborder === \"surround\" &&\r\n\t\t\t\t\t(classes = classes.concat([\"uv-border\", \"uv-input--radius\"]));\r\n\t\t\t\tclasses.push(`uv-input--${shape}`);\r\n\t\t\t\tborder === \"bottom\" &&\r\n\t\t\t\t\t(classes = classes.concat([\r\n\t\t\t\t\t\t\"uv-border-bottom\",\r\n\t\t\t\t\t\t\"uv-input--no-radius\",\r\n\t\t\t\t\t]));\r\n\t\t\t\treturn classes.join(\" \");\r\n\t\t\t},\r\n\t\t\t// 组件的样式\r\n\t\t\twrapperStyle() {\r\n\t\t\t\tconst style = {};\r\n\t\t\t\t// 禁用状态下，被背景色加上对应的样式\r\n\t\t\t\tif (this.disabled) {\r\n\t\t\t\t\tstyle.backgroundColor = this.disabledColor;\r\n\t\t\t\t}\r\n\t\t\t\t// 无边框时，去除内边距\r\n\t\t\t\tif (this.border === \"none\") {\r\n\t\t\t\t\tstyle.padding = \"0\";\r\n\t\t\t\t} else {\r\n\t\t\t\t\t// 由于uni-app的iOS开发者能力有限，导致需要分开写才有效\r\n\t\t\t\t\tstyle.paddingTop = \"6px\";\r\n\t\t\t\t\tstyle.paddingBottom = \"6px\";\r\n\t\t\t\t\tstyle.paddingLeft = \"9px\";\r\n\t\t\t\t\tstyle.paddingRight = \"9px\";\r\n\t\t\t\t}\r\n\t\t\t\treturn this.$uv.deepMerge(style, this.$uv.addStyle(this.customStyle));\r\n\t\t\t},\r\n\t\t\t// 输入框的样式\r\n\t\t\tinputStyle() {\r\n\t\t\t\tconst style = {\r\n\t\t\t\t\tcolor: this.color,\r\n\t\t\t\t\tfontSize: this.$uv.addUnit(this.fontSize),\r\n\t\t\t\t\ttextAlign: this.inputAlign\r\n\t\t\t\t};\r\n\t\t\t\t// #ifndef APP-NVUE\r\n\t\t\t\tif(this.disabled || this.readonly) {\r\n\t\t\t\t\tstyle['pointer-events'] = 'none';\r\n\t\t\t\t}\r\n\t\t\t\t// #endif\r\n\t\t\t\treturn style;\r\n\t\t\t}\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\t// 在微信小程序中，不支持将函数当做props参数，故只能通过ref形式调用\r\n\t\t\tsetFormatter(e) {\r\n\t\t\t\tthis.innerFormatter = e\r\n\t\t\t},\r\n\t\t\t// 当键盘输入时，触发input事件\r\n\t\t\tonInput(e) {\r\n\t\t\t\tlet { value = \"\" } = e.detail || {};\r\n\t\t\t\t// 格式化过滤方法\r\n\t\t\t\tconst formatter = this.formatter || this.innerFormatter\r\n\t\t\t\tconst formatValue = formatter(value)\r\n\t\t\t\t// 为了避免props的单向数据流特性，需要先将innerValue值设置为当前值，再在$nextTick中重新赋予设置后的值才有效\r\n\t\t\t\tthis.innerValue = value\r\n\t\t\t\tthis.$nextTick(() => {\r\n\t\t\t\t\tthis.innerValue = formatValue;\r\n\t\t\t\t\tthis.valueChange();\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t// 输入框失去焦点时触发\r\n\t\t\tonBlur(event) {\r\n\t\t\t\tthis.$emit(\"blur\", event.detail.value);\r\n\t\t\t\t// H5端的blur会先于点击清除控件的点击click事件触发，导致focused\r\n\t\t\t\t// 瞬间为false，从而隐藏了清除控件而无法被点击到\r\n\t\t\t\tthis.$uv.sleep(100).then(() => {\r\n\t\t\t\t\tthis.focused = false;\r\n\t\t\t\t});\r\n\t\t\t\t// 尝试调用uv-form的验证方法\r\n\t\t\t\tthis.$uv.formValidate(this, \"blur\");\r\n\t\t\t},\r\n\t\t\t// 输入框聚焦时触发\r\n\t\t\tonFocus(event) {\r\n\t\t\t\tthis.focused = true;\r\n\t\t\t\tthis.$emit(\"focus\");\r\n\t\t\t},\r\n\t\t\t// 点击完成按钮时触发\r\n\t\t\tonConfirm(event) {\r\n\t\t\t\tthis.$emit(\"confirm\", this.innerValue);\r\n\t\t\t},\r\n\t\t\t// 键盘高度发生变化的时候触发此事件\r\n\t\t\t// 兼容性：微信小程序2.7.0+、App 3.1.0+\r\n\t\t\tonkeyboardheightchange(e) {\r\n\t\t\t\tthis.$emit(\"keyboardheightchange\",e);\r\n\t\t\t},\r\n\t\t\t// 内容发生变化，进行处理\r\n\t\t\tvalueChange() {\r\n\t\t\t\tif(this.isClear) this.innerValue = '';\r\n\t\t\t\tconst value = this.innerValue;\r\n\t\t\t\tthis.$nextTick(() => {\r\n\t\t\t\t\tthis.$emit(\"input\", value);\r\n\t\t\t\t\tthis.$emit(\"update:modelValue\", value);\r\n\t\t\t\t\tthis.$emit(\"change\", value);\r\n\t\t\t\t\t// 尝试调用uv-form的验证方法\r\n\t\t\t\t\tthis.$uv.formValidate(this, \"change\");\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t// 点击清除控件\r\n\t\t\tonClear() {\r\n\t\t\t\tthis.innerValue = \"\";\r\n\t\t\t\tthis.isClear = true;\r\n\t\t\t\tthis.$uv.sleep(200).then(res=>{\r\n\t\t\t\t\tthis.isClear = false;\r\n\t\t\t\t})\r\n\t\t\t\tthis.$nextTick(() => {\r\n\t\t\t\t\tthis.$emit(\"clear\");\r\n\t\t\t\t\tthis.valueChange();\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 在安卓nvue上，事件无法冒泡\r\n\t\t\t * 在某些时间，我们希望监听uv-from-item的点击事件，此时会导致点击uv-form-item内的uv-input后\r\n\t\t\t * 无法触发uv-form-item的点击事件，这里通过手动调用uv-form-item的方法进行触发\r\n\t\t\t */\r\n\t\t\tclickHandler() {\r\n\t\t\t\t// #ifdef APP-NVUE\r\n\t\t\t\tif (this.$uv.os() === \"android\") {\r\n\t\t\t\t\tconst formItem = this.$uv.$parent.call(this, \"uv-form-item\");\r\n\t\t\t\t\tif (formItem) {\r\n\t\t\t\t\t\tformItem.clickHandler();\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\t// #endif\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n\t$show-border: 1;\r\n\t$show-border-surround: 1;\r\n\t$show-border-bottom: 1;\r\n\t@import '@/uni_modules/uv-ui-tools/libs/css/variable.scss';\r\n\t@import '@/uni_modules/uv-ui-tools/libs/css/components.scss';\r\n\t@import '@/uni_modules/uv-ui-tools/libs/css/color.scss';\r\n\t.uv-input {\r\n\t\t@include flex(row);\r\n\t\talign-items: center;\r\n\t\tjustify-content: space-between;\r\n\t\tflex: 1;\r\n\t\t&--radius,\r\n\t\t&--square {\r\n\t\t\tborder-radius: 4px;\r\n\t\t}\r\n\t\t&--no-radius {\r\n\t\t\tborder-radius: 0;\r\n\t\t}\r\n\t\t&--circle {\r\n\t\t\tborder-radius: 100px;\r\n\t\t}\r\n\t\t&__content {\r\n\t\t\tflex: 1;\r\n\t\t\t@include flex(row);\r\n\t\t\talign-items: center;\r\n\t\t\tjustify-content: space-between;\r\n\t\t\t&__field-wrapper {\r\n\t\t\t\tposition: relative;\r\n\t\t\t\t@include flex(row);\r\n\t\t\t\tmargin: 0;\r\n\t\t\t\tflex: 1;\r\n\t\t\t\t&__field {\r\n\t\t\t\t\tline-height: 26px;\r\n\t\t\t\t\ttext-align: left;\r\n\t\t\t\t\tcolor: $uv-main-color;\r\n\t\t\t\t\theight: 24px;\r\n\t\t\t\t\tfont-size: 15px;\r\n\t\t\t\t\tflex: 1;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t&__clear {\r\n\t\t\t\twidth: 20px;\r\n\t\t\t\theight: 20px;\r\n\t\t\t\tborder-radius: 100px;\r\n\t\t\t\tbackground-color: #c6c7cb;\r\n\t\t\t\t@include flex(row);\r\n\t\t\t\talign-items: center;\r\n\t\t\t\tjustify-content: center;\r\n\t\t\t\ttransform: scale(0.82);\r\n\t\t\t\tmargin-left: 15px;\r\n\t\t\t}\r\n\t\t\t&__subfix-icon {\r\n\t\t\t\tmargin-left: 4px;\r\n\t\t\t}\r\n\t\t\t&__prefix-icon {\r\n\t\t\t\tmargin-right: 4px;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</style>", "import mod from \"-!../../../../../../../../software/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../software/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../software/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../software/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../software/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../software/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../software/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../software/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../software/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uv-input.vue?vue&type=style&index=0&id=34be0c5c&lang=scss&scoped=true&\"; export default mod; export * from \"-!../../../../../../../../software/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../../../../../software/HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../../../../../software/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../../../../../software/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../../../../../software/HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../../../../../software/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../../../../../software/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../../../../../software/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../../../../../software/HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./uv-input.vue?vue&type=style&index=0&id=34be0c5c&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1752369745753\n      var cssReload = require(\"D:/software/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}