// 以下是业务服务器API地址
// 开发时使用
// var WxApiRoot = 'http://[2409:8a3c:17d:c870:5054:ff:fe53:96db]:8083'
// var WxApiRoot = 'http://1.92.207.76:8082'
// var WxApiRoot = 'http://192.168.1.196:8083'
// var WxApiRoot = 'http://192.168.1.3:80'
// var WxApiRoot = 'http://rbfbe669.natappfree.cc'

// 正式服务器地址
// var WxApiRoot = 'http://ooseek.iepose.cn';
// var WxApiRoot = 'https://gold.xinxiangfu.cn';
var WxApiRoot = 'http://qg52961sd27.vicp.fun';


module.exports = {
	LoginUrl: WxApiRoot + '/app/auth/login', //登录接口
	indexInfoUrl: WxApiRoot + '/app/index/info', //首页数据
	noticeListUrl: WxApiRoot + '/app/notice/list', //首页通知
	infoAmtUrl: WxApiRoot + '/app/index/infoAmt', //首页统计数据(0-当日 1-当月)--登录
	listFltUrl: WxApiRoot + '/app/goods/list/fl', //商品分类列表
	storeListUrl: WxApiRoot + '/app/store/list', //门店列表
	userListUrl: WxApiRoot + '/app/store/userList', //邀请人列表
	goodsListUrl: WxApiRoot + '/app/goods/list', //商品列表
	goodsUrl: WxApiRoot + '/app/goods', //商品详情
	goodsDetailsUrl: WxApiRoot + '/app/goods/Details', //商品详情
	settleOrderUrl: WxApiRoot + '/app/goods/settleOrder', //提交订单
	defaultAdressUrl: WxApiRoot + '/app/address/defaultAdress', //默认收货地址列表
	orderPayUrl: WxApiRoot + '/app/goods/orderPay', //订单支付接口

	getUserInfoUrl: WxApiRoot + '/app/auth/getUserInfo', //获取用户信息
	getUserkefuUrl: WxApiRoot + '/app/base/kefu', //获取用户信息
	myAgentUrl: WxApiRoot + '/app/agent/myAgent', //我的代理
	skUrl: WxApiRoot + '/app/index/sk', //我的代理
	piclistUrl: WxApiRoot + '/app/pic/list', //app接口-商学院图片信息
	merchantlistUrl: WxApiRoot + '/app/merchant/list', //商户列表
	cplistUrl: WxApiRoot + '/app/base/cp/list', //产品列表
	getOneUrl: WxApiRoot + '/app/merchant/getOne', //商户详情
	editphoneUrl: WxApiRoot + '/app/merchant/edit', //修改商户手机号

	changeUserPhotoUrl: WxApiRoot + '/app/auth/userPhoto', //修改用户头像

	listConfigUrl: WxApiRoot + '/app/pic/listConfig', //营销海报
	appRegisterUrl: WxApiRoot + '/app/h5/appRegister/3/', //二维码生成链接

	privacyUrl: WxApiRoot +'/app/h5/privacy/3', //隐私协议页面
	zhuceUrl: WxApiRoot +'/app/h5/zhuce/3', //注册协议页面

	uploadUrl: WxApiRoot + '/app/file/upload', //上传文件
	changeUeerPhotoUrl: WxApiRoot + '/app/auth/userPhoto', //修改用户头像
	modifyNameUrl: WxApiRoot + `/app/auth/modifyName`, //修改用户姓名
	myOrderUrl: WxApiRoot + '/app/goodsOrder/showGoodsOrder', //我的订单-登陆
	orderDetailUrl: WxApiRoot + '/app/goodsOrder/orderDetail/', //订单详情-登陆
	cancelOrderUrl: WxApiRoot + '/app/goodsOrder/cancel/', //取消订单-登陆
	makeOutUrl: WxApiRoot + '/app/goodsOrder/makeOut', //开具发票
	realNameUrl: WxApiRoot + '/app/settlement/realName', //实名认证

	ImgCodeUrl: WxApiRoot + '/app/auth/genCaptcha', //图形验证码接口
	resetPasswordUrl: WxApiRoot + '/app/auth/resetPassword', // 忘记密码
	PhoneCodeUrl: WxApiRoot + '/app/auth/getSms', //短信验证码接口
	registerAuthUrl: WxApiRoot + '/app/auth/register', //用户注册
	// /app/base/appVersion/{tenantId}/{type}
	// app版本 type:0ios 1安卓
	appVersionUrl: WxApiRoot + '/app/base/appVersion', //获取版本

}
