{"version": 3, "sources": ["uni-app:///main.js", "webpack:///E:/pos/黄金/gold_client/pages/login/login.vue?20c9", "webpack:///E:/pos/黄金/gold_client/pages/login/login.vue?5022", "webpack:///E:/pos/黄金/gold_client/pages/login/login.vue?08b0", "webpack:///E:/pos/黄金/gold_client/pages/login/login.vue?cbd2", "uni-app:///pages/login/login.vue", "webpack:///E:/pos/黄金/gold_client/pages/login/login.vue?8e8d", "webpack:///E:/pos/黄金/gold_client/pages/login/login.vue?75d5"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "loginForm", "passwd", "phone", "passwordtype", "onLoad", "methods", "gozhuce", "uni", "url", "hideenPasst", "sowPasst", "goForgotPassword", "<PERSON><PERSON>", "title", "icon", "util", "api", "tenantId", "res", "setTimeout", "index", "text"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACsC;;;AAG1F;AACgL;AAChL,gBAAgB,yLAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAspB,CAAgB,2qBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACsC1qB;AACA;AAAA,eACA;EACAC;IAAA;IACA;MACAC;QACAC;QACAC;MAAA,uDACA,mEACA,sEAGA,iBACA;MACA;MACA;MACA;MACA;MACA;MACAC;IACA;EACA;EACAC,2BAEA;EACAC;IACAC;MACAC;QACAC;MACA;IACA;IACAC;MACA;IAEA;IACAC;MACA;IACA;IACA;IACAC;MAEAJ;QACAC;MACA;IACA;IACAI;MACAL;QACAM;MACA;MACA;IACA;EAAA,qEACA;IACAN;MACAM;IACA;IACA;EACA,kFAEA;IAAA;IAAA;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA,IACA;gBAAA;gBAAA;cAAA;cACAN;gBACAM;gBACAC;cACA;cAAA;YAAA;cAAA,IAGA;gBAAA;gBAAA;cAAA;cACAP;gBACAM;gBACAC;cACA;cAAA;YAAA;cAAA;cAAA,OAIAC,aACAC;gBACAf;gBACAC;gBACAe;cACA,GACA,OACA;YAAA;cAPAC;cAAA,MAQAA;gBAAA;gBAAA;cAAA;cACAX;cACAY;gBACAZ;kBACAM;kBACAC;gBACA;cACA;cAAA;YAAA;cAIAP;cACA;gBACAA;gBACAA;kBACAa;kBACAC;gBACA;gBACAd;kBACAa;kBACAC;gBACA;cACA;gBACAd;cACA;;cAEA;;cAEA;cACA;cACA;;cAEA;cACA;cACAY;gBACAZ;kBACAM;kBACAC;gBACA;gBAEAP;kBACAC;gBACA;cACA;;cAIA;YAAA;YAAA;cAAA;UAAA;QAAA;MAAA;IAAA;EAEA;AAEA;AAAA,2B;;;;;;;;;;;;;AC3KA;AAAA;AAAA;AAAA;AAAqwC,CAAgB,kuCAAG,EAAC,C;;;;;;;;;;;ACAzxC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/login/login.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/login/login.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./login.vue?vue&type=template&id=b237504c&scoped=true&\"\nvar renderjs\nimport script from \"./login.vue?vue&type=script&lang=js&\"\nexport * from \"./login.vue?vue&type=script&lang=js&\"\nimport style0 from \"./login.vue?vue&type=style&index=0&id=b237504c&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"b237504c\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/login/login.vue\"\nexport default component.exports", "export * from \"-!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./login.vue?vue&type=template&id=b237504c&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./login.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./login.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"loginpage\">\r\n\t\t<!-- #ifdef APP-PLUS -->\r\n\t\t<view class=\"status_bar\">\r\n\t\t  <!-- 这是状态栏 -->\r\n\t\t</view>\r\n\t\t<!-- #endif -->\r\n\t\t<view class=\"\" style=\"height: 50px; width: 100%;\">\r\n\t\t\t\r\n\t\t</view>\r\n\t\t<view class=\"login-img\">\r\n\t\t\t<image style=\"width: 150px;height: 150px;border-radius: 10px;\" src=\"/static/KLLD.png\" mode=\"\"></image>\r\n\t\t</view>\r\n\t\t<view class=\"login-form\">\r\n\t\t\t<view class=\"login-account\">\r\n\t\t\t\t<image src=\"/static/img/login/ic_phone.png\" mode=\"widthFix\" style=\"width: 15px; height: 15px;\"></image>\r\n\t\t\t\t<input placeholder=\"请输入手机号\" type=\"number\" v-model=\"loginForm.phone\" />\r\n\t\t\t</view>\r\n\t\t\t<view class=\"login-account\">\r\n\t\t\t\t<image src=\"/static/img/login/ic_password.png\" mode=\"widthFix\" style=\"width: 15px; height: 15px;\"></image>\r\n\t\t\t\t<input type=\"password\" placeholder=\"请输入密码\"  v-model=\"loginForm.passwd\" v-if=\"passwordtype == 'password'\" />\r\n\t\t\t\t<input type=\"text\" placeholder=\"请输入密码\"  v-model=\"loginForm.passwd\" v-else />\r\n\t\t\t\t\r\n\t\t\t\t<image class=\"showOre\" src=\"/static/img/login/ic_password_normal.png\" mode=\"widthFix\" style=\"width: 15px; height: 10px;\" @click=\"sowPasst\"  v-if=\"passwordtype == 'password' \"></image>\r\n\t\t\t\t<image class=\"showOre\" src=\"/static/img/login/ic_password_press.png\" mode=\"widthFix\" style=\"width: 15px; height: 10px;\" @click=\"hideenPasst\" v-else ></image>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"footer\">\r\n\t\t\t\t<text  @click=\"gozhuce\">立即注册</text>\r\n\t\t\t\t<text  @click=\"goForgotPassword\">忘记密码</text>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view class=\"authorized-btn\" @click=\"Login\">\r\n\t\t\t登录\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\tconst api = require('../../config/api');\r\n\tconst util = require('../../utils/util');\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tloginForm: {\r\n\t\t\t\t\tpasswd: \"\",\r\n\t\t\t\t\tphone: \"\",\r\n\t\t\t\t\tpasswd: '17753422520',\r\n\t\t\t\t\tphone: '15358381598',\r\n\t\t\t\t\t // \"passwd\": \"17753422520\",\r\n\t\t\t\t\t //  \"phone\": \"18534567716\",\r\n\t\t\t\t\ttenantId: '3'\r\n\t\t\t\t},\r\n\t\t\t\t// passwd: \"17753422520\",\r\n\t\t\t\t// phone: \"18453511319\",\r\n\t\t\t\t// passwd: '17753422520',\r\n\t\t\t\t // phone: '13603543256',\r\n\t\t\t\t// tenantId: '3'\r\n\t\t\t\tpasswordtype:\"password\"\r\n\t\t\t}\r\n\t\t},\r\n\t\tonLoad() {\r\n\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tgozhuce(){\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: '/pages/register/register'\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\thideenPasst(){\r\n\t\t\t\tthis.passwordtype = \"password\"\r\n\t\t\t\t\r\n\t\t\t},\r\n\t\t\tsowPasst(){\r\n\t\t\t\tthis.passwordtype = \"number\"\r\n\t\t\t},\r\n\t\t\t// 忘记密码\r\n\t\t\tgoForgotPassword() {\r\n\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: '/pages/ForgotPassword/ForgotPassword'\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tLogin() {\r\n\t\t\t\tuni.showLoading({\r\n\t\t\t\t\ttitle: '正在登录'\r\n\t\t\t\t});\r\n\t\t\t\tthis.handleLogin()\r\n\t\t\t},\r\n\t\t\tLogin() {\r\n\t\t\t\tuni.showLoading({\r\n\t\t\t\t\ttitle: '正在登录'\r\n\t\t\t\t});\r\n\t\t\t\tthis.handleLogin()\r\n\t\t\t},\r\n\t\t\t// 登录\r\n\t\t\tasync handleLogin() {\r\n\t\t\t\tif (!this.loginForm.phone.trim()) {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '手机号不能为空',\r\n\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t})\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tif (!this.loginForm.passwd.trim()) {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '密码不能空',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t});\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\t// try {\r\n\t\t\t\t\tconst res = await util.request(\r\n\t\t\t\t\t\tapi.LoginUrl, {\r\n\t\t\t\t\t\t\tpasswd: this.loginForm.passwd,\r\n\t\t\t\t\t\t\tphone: this.loginForm.phone,\r\n\t\t\t\t\t\t\ttenantId: this.loginForm.tenantId\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t\t'POST'\r\n\t\t\t\t\t);\r\n\t\t\t\t\tif (res.code !== 0) {\r\n\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\ttitle: res.msg,\r\n\t\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t}, 30)\r\n\r\n\t\t\t\t\t\treturn;\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\tuni.setStorageSync('token', res.data.token)\r\n\t\t\t\t\t\t\tif(res.data.user.phone =='17753422520'){\r\n\t\t\t\t\t\t\t\tuni.setStorageSync('isTest', true)\r\n\t\t\t\t\t\t\t\tuni.setTabBarItem({\r\n\t\t\t\t\t\t\t\t\tindex: 2,\r\n\t\t\t\t\t\t\t\t\ttext: \"消息\"\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\tuni.setTabBarItem({\r\n\t\t\t\t\t\t\t\t\tindex: 1,\r\n\t\t\t\t\t\t\t\t\ttext: \"文案\"\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t}else{\r\n\t\t\t\t\t\t\t\tuni.setStorageSync('isTest', false)\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t// \tuni.setStorageSync('phone',this.loginForm.phone);\r\n\t\t\t\t\t\r\n\t\t\t\t\t// \tuni.setStorageSync('userid', res.user.id);\r\n\t\t\t\t\t// \tuni.setStorageSync('user', res.user);\r\n\t\t\t\t\t// \tuni.setStorageSync('agentId', res.user.agentId);\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t// \tuni.setStorageSync('merchantId', res.user.merchantId)\r\n\t\t\t\t\t// \tuni.hideLoading();\r\n\t\t\t\t\t\tsetTimeout(() => { \r\n\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\ttitle: \"登陆成功\",\r\n\t\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\tuni.switchTab({\r\n\t\t\t\t\t\t\t    url: '/pages/index/index'\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t}, 30)\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t\t// this.getmerDetail();\r\n\t\t\t\t\t}\r\n\t\t\t},\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n\r\n<style lang=\"scss\" scoped>\r\n.loginpage{\r\n\tpadding: 2px;\r\n\t\r\n}\r\n\t.login-img {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\tmargin-top: 15px;\r\n\t}\r\n\r\n\t/* 表单部分 */\r\n\t.login-form {\r\n\t\tmargin-left: 25px;\r\n\t\tmargin-right: 25px;\r\n\t\tmargin-top: 25px;\r\n\t}\r\n\r\n\t.login-account {\r\n\t\tposition: relative;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tfont-size: 13px;\r\n\t\talign-items: center;\r\n\t\tbackground-color: #fff;\r\n\t\theight: 45px;\r\n\t\tborder-bottom: 1px solid #E5E5E5;\r\n\t}\r\n\r\n\t.login-account input {\r\n\t\tflex: 1;\r\n\t\tmargin-left: 15px;\r\n\t\tpadding: 5px;\r\n\t\tpadding-right: 15px;\r\n\t}\r\n\r\n\t.inp-palcehoder {\r\n\t\tfont-size: 13px;\r\n\t}\r\n\r\n\t.input-item {\r\n\t\tposition: relative;\r\n\t\tmargin-left: 20px;\r\n\t\twidth: 40px;\r\n\t}\r\n\r\n\t.footer {\r\n\t\tfont-size: 14px;\r\n\t\tmargin-top: 20px;\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: space-between;\r\n\t}\r\n\r\n\t.authorized-btn {\r\n\t\tmargin-top: 15px;\r\n\t\tmargin-left: 15px;\r\n\t\tmargin-right: 15px;\r\n\t\t// padding: 10px;\r\n\t\theight: 44px;\r\n\t\tline-height: 44px;\r\n\t\tcolor: #fff;\r\n\t\t// margin: 0 auto;\r\n\t\ttext-align: center;\r\n\t\tbackground-color: #6A9FFB;\r\n\t\tborder-radius: 20px;\r\n\t}\r\n\t.showOre{\r\n\t\t// position: absolute;\r\n\t\t// right: 10px;\r\n\t\t// top: 50%;\r\n\t\t// \ttransform: translateY(-50%);\r\n\t}\r\n\t.footer{\r\n\t\tcolor: #76A7FB;\r\n\t\tfont-size: 15px;\r\n\t}\r\n\r\n</style>", "import mod from \"-!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./login.vue?vue&type=style&index=0&id=b237504c&lang=scss&scoped=true&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./login.vue?vue&type=style&index=0&id=b237504c&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754188039896\n      var cssReload = require(\"D:/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}