(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-my-nameAuth"],{"1ac2":function(e,a,t){"use strict";var n=t("7c27"),r=t.n(n);r.a},"67c9":function(e,a,t){"use strict";t.r(a);var n=t("cf7f"),r=t.n(n);for(var i in n)["default"].indexOf(i)<0&&function(e){t.d(a,e,(function(){return n[e]}))}(i);a["default"]=r.a},"7c27":function(e,a,t){var n=t("b7bd");n.__esModule&&(n=n.default),"string"===typeof n&&(n=[[e.i,n,""]]),n.locals&&(e.exports=n.locals);var r=t("4f06").default;r("4a8b7fc6",n,!0,{sourceMap:!1,shadowMode:!1})},"95dd":function(e,a,t){"use strict";t.d(a,"b",(function(){return n})),t.d(a,"c",(function(){return r})),t.d(a,"a",(function(){}));var n=function(){var e=this,a=e.$createElement,t=e._self._c||a;return t("v-uni-view",{staticClass:"real-auth-page"},[t("v-uni-view",{staticClass:"each-line"},[t("v-uni-text",{staticClass:"label"},[e._v("姓名")]),t("v-uni-input",{staticClass:"no-input",attrs:{disabled:!0,type:"text",placeholder:"请输入姓名"},model:{value:e.authInfo.realName,callback:function(a){e.$set(e.authInfo,"realName",a)},expression:"authInfo.realName"}})],1),t("v-uni-view",{staticClass:"each-line"},[t("v-uni-text",{staticClass:"label"},[e._v("身份证号")]),t("v-uni-input",{staticClass:"no-input",attrs:{disabled:!0,type:"text",placeholder:"请输入身份证号"},model:{value:e.authInfo.idCard,callback:function(a){e.$set(e.authInfo,"idCard",a)},expression:"authInfo.idCard"}})],1),t("v-uni-view",{staticClass:"each-line"},[t("v-uni-text",{staticClass:"label"},[e._v("手机号")]),t("v-uni-input",{staticClass:"no-input",attrs:{disabled:!0,type:"text",placeholder:"请输入手机号"},model:{value:e.authInfo.phone,callback:function(a){e.$set(e.authInfo,"phone",a)},expression:"authInfo.phone"}})],1)],1)},r=[]},b7bd:function(e,a,t){var n=t("24fb");a=n(!1),a.push([e.i,'@charset "UTF-8";\r\n/* uni.scss */\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 字体变量 */\r\n/* 颜色变量 */\r\n/* 行为相关颜色 */\r\n/* 文字基本颜色 */\r\n/* 背景颜色 */\r\n/* 边框颜色 */\r\n/* 尺寸变量 */\r\n/* 文字尺寸 */\r\n/* 图片尺寸 */\r\n/* Border Radius */\r\n/* 水平间距 */\r\n/* 垂直间距 */\r\n/* 透明度 */\r\n/* 文章场景相关 */.real-auth-page .each-line[data-v-3ed83c35]{display:flex;justify-content:normal;border-bottom:1px solid #e5e5e5;padding:%?20?% %?40?%}.real-auth-page .each-line .label[data-v-3ed83c35]{width:%?160?%}.real-auth-page .each-line .no-input[data-v-3ed83c35]{padding-left:%?30?%;flex-grow:1;font-size:%?28?%}.real-auth-page .idcard-image[data-v-3ed83c35]{display:flex;justify-content:space-around;margin-top:%?120?%}.real-auth-page .idcard-image .imgUpload[data-v-3ed83c35]{width:%?360?%;align-items:center;justify-content:center;text-align:center}.real-auth-page .idcard-image .imgUpload uni-image[data-v-3ed83c35]{border:%?1?% solid #b6b4b4;width:%?340?%;height:%?228?%}.real-auth-page .submit-btn[data-v-3ed83c35]{width:90%;height:%?90?%;line-height:%?90?%;color:#fff;font-size:%?32?%;margin-top:%?40?%;border:1px solid #007aff;background-color:#007aff;border-radius:%?20?%}',""]),e.exports=a},cf7f:function(e,a,t){"use strict";t("7a82");var n=t("4ea4").default;Object.defineProperty(a,"__esModule",{value:!0}),a.default=void 0;var r=n(t("c7eb")),i=n(t("1da1")),o=t("30ea"),s=t("9e56"),u={name:"realAuth",data:function(){return{authInfo:{realName:"",idCard:"",phone:""}}},onLoad:function(e){this.getUserInfo()},methods:{getUserInfo:function(){var e=this;return(0,i.default)((0,r.default)().mark((function a(){var t;return(0,r.default)().wrap((function(a){while(1)switch(a.prev=a.next){case 0:return a.next=2,s.request(o.getUserInfoUrl,{},"POST");case 2:t=a.sent,console.log(t),0!==t.code?uni.showToast({title:t.msg,icon:"none"}):t.data.realName?(e.authInfo.realName=s.processName(t.data.realName.realName),e.authInfo.idCard=s.phoneSubstringfct(t.data.realName.idCard),e.authInfo.phone=s.phoneSubstring(t.data.realName.phone)):uni.navigateTo({url:"/pages/my/myBankCard?from=nameAuth"});case 5:case"end":return a.stop()}}),a)})))()},submitEvent:function(){}}};a.default=u},f091:function(e,a,t){"use strict";t.r(a);var n=t("95dd"),r=t("67c9");for(var i in r)["default"].indexOf(i)<0&&function(e){t.d(a,e,(function(){return r[e]}))}(i);t("1ac2");var o=t("f0c5"),s=Object(o["a"])(r["default"],n["b"],n["c"],!1,null,"3ed83c35",null,!1,n["a"],void 0);a["default"]=s.exports}}]);