{"version": 3, "sources": ["uni-app:///main.js", null, "webpack:///E:/pos/黄金/gold_client/pages/invoice/invoice.vue?7e35", "webpack:///E:/pos/黄金/gold_client/pages/invoice/invoice.vue?183c", "webpack:///E:/pos/黄金/gold_client/pages/invoice/invoice.vue?e04f", "uni-app:///pages/invoice/invoice.vue", "webpack:///E:/pos/黄金/gold_client/pages/invoice/invoice.vue?bce3", "webpack:///E:/pos/黄金/gold_client/pages/invoice/invoice.vue?fed8"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "name", "data", "orderInfo", "orderNo", "goodsName", "totalAmt", "invoiceForm", "infoKind", "headerType", "clientName", "clientTaxNo", "submitting", "onLoad", "methods", "validateForm", "uni", "title", "icon", "submitInvoice", "util", "setTimeout", "resetForm"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,gBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAoH;AACpH;AAC2D;AACL;AACc;;;AAGpE;AACgL;AAChL,gBAAgB,yLAAU;AAC1B,EAAE,6EAAM;AACR,EAAE,kFAAM;AACR,EAAE,2FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,sFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,+TAEN;AACP,KAAK;AACL;AACA,aAAa,qOAEN;AACP,KAAK;AACL;AACA,aAAa,2RAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC3CA;AAAA;AAAA;AAAA;AAAwpB,CAAgB,6qBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eC4F5qB;EACAC;EACAC;IACA;MACAC;QACAC;QACAC;QACAC;MACA;MACAC;QACAH;QAAA;QACAI;QAAA;QACAC;QAAA;QACAC;QAAA;QACAC;MACA;;MACAC;IACA;EACA;EACAC;IACA;IACA;MACA;MACA;IACA;;IACA;MACA;IACA;IACA;MACA;IACA;EACA;EACAC;IACA;IACAC;MACA;QACAC;UACAC;UACAC;QACA;QACA;MACA;;MAEA;MACA;QACAF;UACAC;UACAC;QACA;QACA;MACA;MAEA;IACA;IAEA;IACAC;MAAA;MACA;QACA;MACA;MAEA;;MAEA;MACA;MACA;MAEA;QACAf;QACAI;QACAC;QACAC;QACAC;MACA;MAEAS;QACA;QACA;UACAJ;YACAC;YACAC;UACA;UAEAG;YACAL;UACA;QACA;UACAA;YACAC;YACAC;UACA;QACA;MACA;QACA;QACAF;UACAC;UACAC;QACA;MACA;IACA;IAEA;IACAI;MACA;QACAlB;QAAA;QACAI;QAAA;QACAC;QAAA;QACAC;QAAA;QACAC;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC5MA;AAAA;AAAA;AAAA;AAA+uC,CAAgB,4sCAAG,EAAC,C;;;;;;;;;;;ACAnwC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/invoice/invoice.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/invoice/invoice.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./invoice.vue?vue&type=template&id=11ab5162&\"\nvar renderjs\nimport script from \"./invoice.vue?vue&type=script&lang=js&\"\nexport * from \"./invoice.vue?vue&type=script&lang=js&\"\nimport style0 from \"./invoice.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/invoice/invoice.vue\"\nexport default component.exports", "export * from \"-!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./invoice.vue?vue&type=template&id=11ab5162&\"", "var components\ntry {\n  components = {\n    uRadioGroup: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-radio-group/u-radio-group\" */ \"uview-ui/components/u-radio-group/u-radio-group.vue\"\n      )\n    },\n    uRadio: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-radio/u-radio\" */ \"uview-ui/components/u-radio/u-radio.vue\"\n      )\n    },\n    uInput: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-input/u-input\" */ \"uview-ui/components/u-input/u-input.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./invoice.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./invoice.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"page\">\n\t\t\n\t\t<view class=\"form-container\">\n\t\t\t<!-- 订单信息展示 -->\n\t\t\t<view class=\"order-info\">\n\t\t\t\t<view class=\"order-title\">订单信息</view>\n\t\t\t\t<view class=\"order-item\">\n\t\t\t\t\t<text class=\"label\">订单编号：</text>\n\t\t\t\t\t<text class=\"value\">{{orderInfo.orderNo}}</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"order-item\">\n\t\t\t\t\t<text class=\"label\">商品名称：</text>\n\t\t\t\t\t<text class=\"value\">{{orderInfo.goodsName}}</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"order-item\">\n\t\t\t\t\t<text class=\"label\">订单金额：</text>\n\t\t\t\t\t<text class=\"value\">¥{{orderInfo.totalAmt}}</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\n\t\t\t<!-- 发票信息表单 -->\n\t\t\t<view class=\"invoice-form\">\n\t\t\t\t<view class=\"form-title\">发票信息</view>\n\t\t\t\t\n\t\t\t\t<!-- 发票信息 -->\n\t\t\t\t<view class=\"form-section\">\n\t\t\t\t\t<view class=\"section-title\">发票信息</view>\n\n\t\t\t\t\t<view class=\"form-item\">\n\t\t\t\t\t\t<view class=\"item-label required\">发票类型</view>\n\t\t\t\t\t\t<u-radio-group v-model=\"invoiceForm.infoKind\" placement=\"row\">\n\t\t\t\t\t\t\t<u-radio\n\t\t\t\t\t\t\t\t:customStyle=\"{marginRight: '32rpx'}\"\n\t\t\t\t\t\t\t\tname=\"12\"\n\t\t\t\t\t\t\t\tactiveColor=\"#BBA186\"\n\t\t\t\t\t\t\t>普通发票</u-radio>\n\t\t\t\t\t\t\t<u-radio\n\t\t\t\t\t\t\t\tname=\"11\"\n\t\t\t\t\t\t\t\tactiveColor=\"#BBA186\"\n\t\t\t\t\t\t\t>增值税专用发票</u-radio>\n\t\t\t\t\t\t</u-radio-group>\n\t\t\t\t\t</view>\n\n\t\t\t\t\t<view class=\"form-item\">\n\t\t\t\t\t\t<view class=\"item-label required\">开票对象</view>\n\t\t\t\t\t\t<u-radio-group v-model=\"invoiceForm.headerType\" placement=\"row\">\n\t\t\t\t\t\t\t<u-radio\n\t\t\t\t\t\t\t\t:customStyle=\"{marginRight: '32rpx'}\"\n\t\t\t\t\t\t\t\tname=\"1\"\n\t\t\t\t\t\t\t\tactiveColor=\"#BBA186\"\n\t\t\t\t\t\t\t>个人</u-radio>\n\t\t\t\t\t\t\t<u-radio\n\t\t\t\t\t\t\t\tname=\"0\"\n\t\t\t\t\t\t\t\tactiveColor=\"#BBA186\"\n\t\t\t\t\t\t\t>企业</u-radio>\n\t\t\t\t\t\t</u-radio-group>\n\t\t\t\t\t</view>\n\n\t\t\t\t\t<view class=\"form-item\">\n\t\t\t\t\t\t<view class=\"item-label required\">购方名称</view>\n\t\t\t\t\t\t<u-input\n\t\t\t\t\t\t\tv-model=\"invoiceForm.clientName\"\n\t\t\t\t\t\t\tplaceholder=\"请填写购方名称\"\n\t\t\t\t\t\t\t:clearable=\"true\"\n\t\t\t\t\t\t></u-input>\n\t\t\t\t\t</view>\n\n\t\t\t\t\t<view class=\"form-item\" v-if=\"invoiceForm.headerType === '0'\">\n\t\t\t\t\t\t<view class=\"item-label required\">购方税号</view>\n\t\t\t\t\t\t<u-input\n\t\t\t\t\t\t\tv-model=\"invoiceForm.clientTaxNo\"\n\t\t\t\t\t\t\tplaceholder=\"请填写购方税号\"\n\t\t\t\t\t\t\t:clearable=\"true\"\n\t\t\t\t\t\t></u-input>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\n\t\t\t</view>\n\t\t</view>\n\t\t\n\t\t<!-- 底部按钮 -->\n\t\t<view class=\"footer-buttons\">\n\t\t\t<button class=\"btn reset-btn\" @click=\"resetForm\">重置</button>\n\t\t\t<button class=\"btn submit-btn\" @click=\"submitInvoice\" :disabled=\"submitting\">\n\t\t\t\t{{submitting ? '提交中...' : '提交申请'}}\n\t\t\t</button>\n\t\t</view>\n\t</view>\n</template>\n\n<script>\n\texport default {\n\t\tname: 'invoice',\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\torderInfo: {\n\t\t\t\t\torderNo: '',\n\t\t\t\t\tgoodsName: '',\n\t\t\t\t\ttotalAmt: ''\n\t\t\t\t},\n\t\t\t\tinvoiceForm: {\n\t\t\t\t\torderNo: '',        // 订单号\n\t\t\t\t\tinfoKind: '12',     // 发票类型：11-增值税专用发票，12-普通发票\n\t\t\t\t\theaderType: '1',    // 开票对象：0-企业，1-个人\n\t\t\t\t\tclientName: '',     // 购方名称\n\t\t\t\t\tclientTaxNo: ''     // 购方税号\n\t\t\t\t},\n\t\t\t\tsubmitting: false\n\t\t\t}\n\t\t},\n\t\tonLoad(options) {\n\t\t\t// 获取传递的订单信息\n\t\t\tif (options.orderNo) {\n\t\t\t\tthis.orderInfo.orderNo = options.orderNo\n\t\t\t\tthis.invoiceForm.orderNo = options.orderNo // 设置表单中的订单号\n\t\t\t}\n\t\t\tif (options.totalAmt) {\n\t\t\t\tthis.orderInfo.totalAmt = options.totalAmt\n\t\t\t}\n\t\t\tif (options.goodsName) {\n\t\t\t\tthis.orderInfo.goodsName = decodeURIComponent(options.goodsName)\n\t\t\t}\n\t\t},\n\t\tmethods: {\n\t\t\t// 验证表单\n\t\t\tvalidateForm() {\n\t\t\t\tif (!this.invoiceForm.clientName) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '请填写购方名称',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t})\n\t\t\t\t\treturn false\n\t\t\t\t}\n\n\t\t\t\t// 如果是企业开票，需要填写税号\n\t\t\t\tif (this.invoiceForm.headerType === '0' && !this.invoiceForm.clientTaxNo) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '企业开票需要填写购方税号',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t})\n\t\t\t\t\treturn false\n\t\t\t\t}\n\n\t\t\t\treturn true\n\t\t\t},\n\t\t\t\n\t\t\t// 提交发票申请\n\t\t\tsubmitInvoice() {\n\t\t\t\tif (!this.validateForm()) {\n\t\t\t\t\treturn\n\t\t\t\t}\n\n\t\t\t\tthis.submitting = true\n\n\t\t\t\t// 调用开具发票接口\n\t\t\t\tconst util = require('../../utils/util')\n\t\t\t\tconst api = require('../../config/api')\n\n\t\t\t\tconst requestData = {\n\t\t\t\t\torderNo: this.invoiceForm.orderNo,\n\t\t\t\t\tinfoKind: this.invoiceForm.infoKind,\n\t\t\t\t\theaderType: this.invoiceForm.headerType,\n\t\t\t\t\tclientName: this.invoiceForm.clientName,\n\t\t\t\t\tclientTaxNo: this.invoiceForm.clientTaxNo\n\t\t\t\t}\n\n\t\t\t\tutil.request(api.makeOutUrl, requestData, 'POST').then((res) => {\n\t\t\t\t\tthis.submitting = false\n\t\t\t\t\tif (res.code === 0) {\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: '发票申请已提交',\n\t\t\t\t\t\t\ticon: 'success'\n\t\t\t\t\t\t})\n\n\t\t\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t\t\tuni.navigateBack()\n\t\t\t\t\t\t}, 1500)\n\t\t\t\t\t} else {\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ttitle: res.message || '提交失败，请重试',\n\t\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t\t})\n\t\t\t\t\t}\n\t\t\t\t}).catch(() => {\n\t\t\t\t\tthis.submitting = false\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '网络错误，请重试',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t})\n\t\t\t\t})\n\t\t\t},\n\t\t\t\n\t\t\t// 重置表单\n\t\t\tresetForm() {\n\t\t\t\tthis.invoiceForm = {\n\t\t\t\t\torderNo: this.orderInfo.orderNo, // 保持订单号不变\n\t\t\t\t\tinfoKind: '12',     // 重置为普通发票\n\t\t\t\t\theaderType: '1',    // 重置为个人\n\t\t\t\t\tclientName: '',     // 清空购方名称\n\t\t\t\t\tclientTaxNo: ''     // 清空购方税号\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n</script>\n\n<style lang=\"scss\">\n\tpage {\n\t\tbackground-color: #F7F7F7;\n\t}\n\t\n\t.page {\n\t\tmin-height: 100vh;\n\t}\n\t\n\t.form-container {\n\t\tpadding: 24rpx;\n\t}\n\t\n\t.order-info {\n\t\tbackground-color: #FFFFFF;\n\t\tborder-radius: 12rpx;\n\t\tpadding: 24rpx;\n\t\tmargin-bottom: 24rpx;\n\t\t\n\t\t.order-title {\n\t\t\tfont-size: 32rpx;\n\t\t\tfont-weight: 600;\n\t\t\tcolor: #171B25;\n\t\t\tmargin-bottom: 20rpx;\n\t\t}\n\t\t\n\t\t.order-item {\n\t\t\tdisplay: flex;\n\t\t\tmargin-bottom: 12rpx;\n\t\t\t\n\t\t\t.label {\n\t\t\t\tcolor: #61687C;\n\t\t\t\tfont-size: 28rpx;\n\t\t\t\twidth: 160rpx;\n\t\t\t}\n\t\t\t\n\t\t\t.value {\n\t\t\t\tcolor: #171B25;\n\t\t\t\tfont-size: 28rpx;\n\t\t\t\tflex: 1;\n\t\t\t}\n\t\t}\n\t}\n\t\n\t.invoice-form {\n\t\tbackground-color: #FFFFFF;\n\t\tborder-radius: 12rpx;\n\t\tpadding: 24rpx;\n\t\t\n\t\t.form-title {\n\t\t\tfont-size: 32rpx;\n\t\t\tfont-weight: 600;\n\t\t\tcolor: #171B25;\n\t\t\tmargin-bottom: 24rpx;\n\t\t}\n\t\t\n\t\t.form-section {\n\t\t\tmargin-bottom: 32rpx;\n\t\t\t\n\t\t\t.section-title {\n\t\t\t\tfont-size: 28rpx;\n\t\t\t\tcolor: #171B25;\n\t\t\t\tmargin-bottom: 20rpx;\n\t\t\t\tpadding-bottom: 12rpx;\n\t\t\t\tborder-bottom: 1px solid #F2F4F7;\n\t\t\t}\n\t\t}\n\t\t\n\t\t.form-item {\n\t\t\tmargin-bottom: 24rpx;\n\t\t\tdisplay: flex;\n\t\t\talign-items: center;\n\n\t\t\t.item-label {\n\t\t\t\tfont-size: 28rpx;\n\t\t\t\tcolor: #171B25;\n\t\t\t\twidth: 160rpx;\n\t\t\t\tflex-shrink: 0;\n\n\t\t\t\t&.required::after {\n\t\t\t\t\tcontent: '*';\n\t\t\t\t\tcolor: #FF0046;\n\t\t\t\t\tmargin-left: 4rpx;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\t\n\t.footer-buttons {\n\t\tbackground-color: #FFFFFF;\n\t\tpadding: 24rpx;\n\t\tdisplay: flex;\n\t\tgap: 24rpx;\n\t\tmargin-top: 24rpx;\n\t\t\n\t\t.btn {\n\t\t\tflex: 1;\n\t\t\theight: 84rpx;\n\t\t\tborder-radius: 42rpx;\n\t\t\tfont-size: 28rpx;\n\t\t\tborder: none;\n\t\t\tline-height: 84rpx;\n\t\t\t\n\t\t\t&.reset-btn {\n\t\t\t\tbackground-color: #F2F4F7;\n\t\t\t\tcolor: #61687C;\n\t\t\t}\n\t\t\t\n\t\t\t&.submit-btn {\n\t\t\t\tbackground-color: #BBA186;\n\t\t\t\tcolor: #FFFFFF;\n\t\t\t\t\n\t\t\t\t&:disabled {\n\t\t\t\t\tbackground-color: #D0D0D0;\n\t\t\t\t\tcolor: #999999;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n</style>\n", "import mod from \"-!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./invoice.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./invoice.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754275419659\n      var cssReload = require(\"D:/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}