{"version": 3, "sources": ["webpack:///E:/Home/ma-Yi/gold/uni_modules/mescroll-uni/components/mescroll-uni/mescroll-uni.vue?bd5a", "webpack:///E:/Home/ma-Yi/gold/uni_modules/mescroll-uni/components/mescroll-uni/mescroll-uni.vue?7c7c", "webpack:///E:/Home/ma-Yi/gold/uni_modules/mescroll-uni/components/mescroll-uni/mescroll-uni.vue?2597", "webpack:///E:/Home/ma-Yi/gold/uni_modules/mescroll-uni/components/mescroll-uni/mescroll-uni.vue?94a4", "uni-app:///uni_modules/mescroll-uni/components/mescroll-uni/mescroll-uni.vue", "webpack:///E:/Home/ma-Yi/gold/uni_modules/mescroll-uni/components/mescroll-uni/mescroll-uni.vue?3b48", "webpack:///E:/Home/ma-Yi/gold/uni_modules/mescroll-uni/components/mescroll-uni/mescroll-uni.vue?0db4", "webpack:///E:/Home/ma-Yi/gold/uni_modules/mescroll-uni/components/mescroll-uni/wxs/wxs.wxs?61ad", "webpack:///E:/Home/ma-Yi/gold/uni_modules/mescroll-uni/components/mescroll-uni/wxs/wxs.wxs?0c5d"], "names": ["name", "mixins", "components", "MescrollTop", "props", "down", "up", "i18n", "top", "topbar", "bottom", "safearea", "fixed", "type", "default", "height", "bottombar", "disableScroll", "data", "mescroll", "optDown", "optUp", "viewId", "downHight", "downRate", "downLoadType", "upLoadType", "isShowEmpty", "isShowToTop", "scrollTop", "scrollAnim", "windowTop", "windowBottom", "windowHeight", "statusBarHeight", "watch", "computed", "isFixed", "scrollHeight", "numTop", "fixedTop", "padTop", "numBottom", "fixedBottom", "padBottom", "isDownReset", "transition", "translateY", "scrollable", "isDownLoading", "downRotate", "downText", "methods", "toPx", "num", "scroll", "emptyClick", "toTopClick", "setClientHeight", "setTimeout", "getClientInfo", "view", "success", "created", "inOffset", "vm", "outOffset", "onMoving", "showLoading", "beforeEndDownScroll", "endDownScroll", "callback", "showNoMore", "hideUpScroll", "empty", "onShow", "toTop", "MeScroll", "selector", "uni", "console", "mescrollI18n", "mounted", "destroyed"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAyc;AACzc;AACgE;AACL;AACa;;;AAGxE;AACqM;AACrM,gBAAgB,8MAAU;AAC1B,EAAE,kFAAM;AACR,EAAE,uaAAM;AACR,EAAE,gbAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,2aAAU;AACZ;AACA;;AAEA;AACoN;AACpN,WAAW,sOAAM,iBAAiB,8OAAM;;AAExC;AACe,gF;;;;;;;;;;;;AC3Bf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,uSAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjCA;AAAA;AAAA;AAAA;AAAswB,CAAgB,2xBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;AC+E1xB;AAEA;AAEA;AAIA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AATA;AAEA;AAEA;AAAA;EAAA;IAAA;EAAA;AAAA;AAOA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AApBA,eAqBA;EACAA;EACAC;EACAC;IACAC;EACA;EACAC;IACAC;IACAC;IACAC;IACAC;IACAC;IACAC;IACAC;IACAC;MACAC;MACAC;IACA;IACAC;IACAC;MACAH;MACAC;IACA;IACAG;EACA;EACAC;IACA;MACAC;QAAAC;QAAAC;MAAA;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;IACA;EACA;;EACAC;IACApB;MACA;MACA;IACA;EACA;EACAqB;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;QACA;MACA;QACA;MACA;QACA;MACA;IACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;MACA;IACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;MACA;QACA;UAAA;QACA;UAAA;QACA;UAAA;QACA;UAAA;QACA;UAAA;MAAA;IAEA;EACA;EACAC;IACA;IACAC;MACA;QACA;UACA;YAAA;YACAC;UACA;YAAA;YACAA;UACA;YAAA;YACA;UACA;QACA;UACA;UACA;UACA;QACA;MACA;MACA;IACA;IACA;IACAC;MAAA;MACA;QACA;MACA;IACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;MACA;IACA;IACA;IACAC;MAAA;MACA;QACA;QACA;UAAA;UACA;YACA;YACA;cACA;YACA;cAAA;cACA;cACAC;gBACA;cACA;YACA;UACA;QACA;MACA;IACA;IACA;IACAC;MACA;MACA;MACAC;QACAC;MACA;IACA;EACA;EACA;EACAC;IACA;IAEA;MACA;MACA1D;QACA2D;UACAC;QACA;QACAC;UACAD;QACA;QACAE;UACA;UACAF;UACAA;QACA;QACAG;UACAH;UACAA;QACA;QACAI;UACAJ;UACA;QACA;QACAK;UACAL;UACAA;UACAA;UACAA;YAAA;YACA;UACA;QACA;QACA;QACAM;UACAN;QACA;MACA;MACA;MACA3D;QACA;QACA8D;UACAH;QACA;QACA;QACAO;UACAP;QACA;QACA;QACAQ;UACAR;QACA;QACA;QACAS;UACAC;YAAA;YACAV;UACA;QACA;QACA;QACAW;UACAD;YAAA;YACAV;UACA;QACA;QACA;QACAM;UACAN;UACA;UACAA;QACA;MACA;IACA;IAEA;IACA;MAAApD;IAAA;IACAgE;IACAA;IACAA;IACAA;MAAAxE;MAAAC;IAAA;IACA;MAAA;MAAA;IAAA;IACAuE;;IAEA;IACAZ;IACAA;IACAA;IACA;IACAA;;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;IACAA;;IAEA;IACAA;MACAA;MACA;QACA;QACAA;UACA;UACA;UACA;YACAa;UACA;YACAA;UAMA;UACAC;YACA;cACA;cACA;cACAvE;cACA;cACAyD;cACAA;gBACAA;cACA;YACA;cACAe;YACA;UACA;QACA;QACA;MACA;MACA;MACA;QAAA;QACAf;QACAA;UACAA;QACA;MACA;QACAA;UAAA;UACAA;QACA;MACA;IACA;;IAEA;IACA;MACAA;IACA;;IAEA;IACAc;MACA;MACA;MACA;QACAd;QACAgB;QACAJ;MACA;MACA;QACA;QACAZ;MACA;MACA;QACA;QACAA;MACA;IACA;EACA;EACAiB;IACA;IACA;EACA;EACAC;IACA;IACAJ;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACxdA;AAAA;AAAA;AAAA;AAAwlC,CAAgB,klCAAG,EAAC,C;;;;;;;;;;;ACA5mC;AACA,OAAO,KAAU,EAAE,kBAKd;;;;;;;;;;;;;ACNL;AAAA;AAAA,wCAA+W,CAAgB,obAAG,EAAC,C;;;;;;;;;;;;ACAnY;AAAe;AACf;AACA;AACA;AACA;AACA,M", "file": "uni_modules/mescroll-uni/components/mescroll-uni/mescroll-uni.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./mescroll-uni.vue?vue&type=template&id=6f5cf468&filter-modules=eyJ3eHNCaXoiOnsidHlwZSI6InNjcmlwdCIsImNvbnRlbnQiOiIiLCJzdGFydCI6MzU2NywiYXR0cnMiOnsic3JjIjoiLi93eHMvd3hzLnd4cyIsIm1vZHVsZSI6Ind4c0JpeiIsImxhbmciOiJ3eHMifSwiZW5kIjozNTY3fSwicmVuZGVyQml6Ijp7InR5cGUiOiJyZW5kZXJqcyIsImNvbnRlbnQiOiIiLCJzdGFydCI6MzcwMCwiYXR0cnMiOnsibW9kdWxlIjoicmVuZGVyQml6IiwibGFuZyI6ImpzIn0sImVuZCI6Mzc5Mn19&\"\nvar renderjs\nimport script from \"./mescroll-uni.vue?vue&type=script&lang=js&\"\nexport * from \"./mescroll-uni.vue?vue&type=script&lang=js&\"\nimport style0 from \"./mescroll-uni.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\n/* custom blocks */\nimport block0 from \"./wxs/wxs.wxs?vue&type=custom&index=0&blockType=script&issuerPath=E%3A%5CHome%5Cma-Yi%5Cgold%5Cuni_modules%5Cmescroll-uni%5Ccomponents%5Cmescroll-uni%5Cmescroll-uni.vue&module=wxsBiz&lang=wxs\"\nif (typeof block0 === 'function') block0(component)\n\ncomponent.options.__file = \"uni_modules/mescroll-uni/components/mescroll-uni/mescroll-uni.vue\"\nexport default component.exports", "export * from \"-!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./mescroll-uni.vue?vue&type=template&id=6f5cf468&filter-modules=eyJ3eHNCaXoiOnsidHlwZSI6InNjcmlwdCIsImNvbnRlbnQiOiIiLCJzdGFydCI6MzU2NywiYXR0cnMiOnsic3JjIjoiLi93eHMvd3hzLnd4cyIsIm1vZHVsZSI6Ind4c0JpeiIsImxhbmciOiJ3eHMifSwiZW5kIjozNTY3fSwicmVuZGVyQml6Ijp7InR5cGUiOiJyZW5kZXJqcyIsImNvbnRlbnQiOiIiLCJzdGFydCI6MzcwMCwiYXR0cnMiOnsibW9kdWxlIjoicmVuZGVyQml6IiwibGFuZyI6ImpzIn0sImVuZCI6Mzc5Mn19&\"", "var components\ntry {\n  components = {\n    mescrollEmpty: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/mescroll-uni/components/mescroll-empty/mescroll-empty\" */ \"@/uni_modules/mescroll-uni/components/mescroll-empty/mescroll-empty.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./mescroll-uni.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./mescroll-uni.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"mescroll-uni-warp\">\r\n\t\t<scroll-view :id=\"viewId\" class=\"mescroll-uni\" :class=\"{'mescroll-uni-fixed':isFixed}\" :style=\"{'height':scrollHeight,'padding-top':padTop,'padding-bottom':padBottom,'top':fixedTop,'bottom':fixedBottom}\" :scroll-top=\"scrollTop\" :scroll-with-animation=\"scrollAnim\" @scroll=\"scroll\" :scroll-y='scrollable' :enable-back-to-top=\"true\" :throttle=\"false\">\r\n\t\t\t<view class=\"mescroll-uni-content mescroll-render-touch\"\r\n\t\t\t@touchstart=\"wxsBiz.touchstartEvent\" \r\n\t\t\t@touchmove=\"wxsBiz.touchmoveEvent\" \r\n\t\t\t@touchend=\"wxsBiz.touchendEvent\" \r\n\t\t\t@touchcancel=\"wxsBiz.touchendEvent\"\r\n\t\t\t:change:prop=\"wxsBiz.propObserver\"\r\n\t\t\t:prop=\"wxsProp\">\r\n\t\t\t\t<!-- 状态栏 -->\r\n\t\t\t\t<view v-if=\"topbar&&statusBarHeight\" class=\"mescroll-topbar\" :style=\"{height: statusBarHeight+'px', background: topbar}\"></view>\r\n\t\t\r\n\t\t\t\t<view class=\"mescroll-wxs-content\" :style=\"{'transform': translateY, 'transition': transition}\" :change:prop=\"wxsBiz.callObserver\" :prop=\"callProp\">\r\n\t\t\t\t\t<!-- 下拉加载区域 (支付宝小程序子组件传参给子子组件仍报单项数据流的异常,暂时不通过mescroll-down组件实现)-->\r\n\t\t\t\t\t<!-- <mescroll-down :option=\"mescroll.optDown\" :type=\"downLoadType\" :rate=\"downRate\"></mescroll-down> -->\r\n\t\t\t\t\t<view v-if=\"mescroll.optDown.use\" class=\"mescroll-downwarp\" :style=\"{'background':mescroll.optDown.bgColor,'color':mescroll.optDown.textColor}\">\r\n\t\t\t\t\t\t<view class=\"downwarp-content\">\r\n\t\t\t\t\t\t\t<view class=\"downwarp-progress mescroll-wxs-progress\" :class=\"{'mescroll-rotate': isDownLoading}\" :style=\"{'border-color':mescroll.optDown.textColor, 'transform': downRotate}\"></view>\r\n\t\t\t\t\t\t\t<view class=\"downwarp-tip\">{{downText}}</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t<!-- 列表内容 -->\r\n\t\t\t\t\t<slot></slot>\r\n\r\n\t\t\t\t\t<!-- 空布局 -->\r\n\t\t\t\t\t<mescroll-empty v-if=\"isShowEmpty\" :option=\"mescroll.optUp.empty\" @emptyclick=\"emptyClick\"></mescroll-empty>\r\n\r\n\t\t\t\t\t<!-- 上拉加载区域 (下拉刷新时不显示, 支付宝小程序子组件传参给子子组件仍报单项数据流的异常,暂时不通过mescroll-up组件实现)-->\r\n\t\t\t\t\t<!-- <mescroll-up v-if=\"mescroll.optUp.use && !isDownLoading && upLoadType!==3\" :option=\"mescroll.optUp\" :type=\"upLoadType\"></mescroll-up> -->\r\n\t\t\t\t\t<view v-if=\"mescroll.optUp.use && !isDownLoading && upLoadType!==3\" class=\"mescroll-upwarp\" :style=\"{'background':mescroll.optUp.bgColor,'color':mescroll.optUp.textColor}\">\r\n\t\t\t\t\t\t<!-- 加载中 (此处不能用v-if,否则android小程序快速上拉可能会不断触发上拉回调) -->\r\n\t\t\t\t\t\t<view v-show=\"upLoadType===1\">\r\n\t\t\t\t\t\t\t<view class=\"upwarp-progress mescroll-rotate\" :style=\"{'border-color':mescroll.optUp.textColor}\"></view>\r\n\t\t\t\t\t\t\t<view class=\"upwarp-tip\">{{ mescroll.optUp.textLoading }}</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<!-- 无数据 -->\r\n\t\t\t\t\t\t<view v-if=\"upLoadType===2\" class=\"upwarp-nodata\">{{ mescroll.optUp.textNoMore }}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\r\n\t\t\t\t<!-- 底部是否偏移TabBar的高度(默认仅在H5端的tab页生效) -->\r\n\t\t\t\t<!-- #ifdef H5 -->\r\n\t\t\t\t<view v-if=\"bottombar && windowBottom>0\" class=\"mescroll-bottombar\" :style=\"{height: windowBottom+'px'}\"></view>\r\n\t\t\t\t<!-- #endif -->\r\n\t\t\t\t\r\n\t\t\t\t<!-- 适配iPhoneX -->\r\n\t\t\t\t<view v-if=\"safearea\" class=\"mescroll-safearea\"></view>\r\n\t\t\t</view>\r\n\t\t</scroll-view>\r\n\r\n\t\t<!-- 回到顶部按钮 (fixed元素,需写在scroll-view外面,防止滚动的时候抖动)-->\r\n\t\t<mescroll-top v-model=\"isShowToTop\" :option=\"mescroll.optUp.toTop\" @click=\"toTopClick\"></mescroll-top>\r\n\t\t\r\n\t\t<!-- #ifdef MP-WEIXIN || MP-QQ || APP-PLUS || H5 -->\r\n\t\t<!-- renderjs的数据载体,不可写在mescroll-downwarp内部,避免use为false时,载体丢失,无法更新数据 -->\r\n\t\t<view :change:prop=\"renderBiz.propObserver\" :prop=\"wxsProp\"></view>\r\n\t\t<!-- #endif -->\r\n\t</view>\r\n</template>\r\n\r\n<!-- 微信小程序, QQ小程序, app, h5使用wxs -->\r\n<!-- #ifdef MP-WEIXIN || MP-QQ || APP-PLUS || H5 -->\r\n<script src=\"./wxs/wxs.wxs\" module=\"wxsBiz\" lang=\"wxs\"></script>\r\n<!-- #endif -->\r\n\r\n<!-- app, h5使用renderjs -->\r\n<!-- #ifdef APP-PLUS || H5 -->\r\n<script module=\"renderBiz\" lang=\"renderjs\">\r\n\timport renderBiz from './wxs/renderjs.js';\r\n\texport default {\r\n\t\tmixins:[renderBiz]\r\n\t}\r\n</script>\r\n<!-- #endif -->\r\n\r\n<script>\r\n\t// 引入mescroll-uni.js,处理核心逻辑\r\n\timport MeScroll from './mescroll-uni.js';\r\n\t// 引入全局配置\r\n\timport GlobalOption from './mescroll-uni-option.js';\r\n\t// 引入国际化工具类\r\n\timport mescrollI18n from './mescroll-i18n.js';\r\n\t// 引入回到顶部组件\r\n\timport MescrollTop from './components/mescroll-top.vue';\r\n\t// 引入兼容wxs(含renderjs)写法的mixins\r\n\timport WxsMixin from './wxs/mixins.js';\r\n\t\r\n\t/**\r\n\t * mescroll-uni 嵌在页面某个区域的下拉刷新和上拉加载组件, 如嵌在弹窗,浮层,swiper中...\r\n\t * @property {Object} down 下拉刷新的参数配置\r\n\t * @property {Object} up 上拉加载的参数配置\r\n\t * @property {Object} i18n 国际化的参数配置\r\n\t * @property {String, Number} top 下拉布局往下的偏移量 (支持20, \"20rpx\", \"20px\", \"20%\"格式的值, 其中纯数字则默认单位rpx, 百分比则相对于windowHeight)\r\n\t * @property {Boolean, String} topbar 偏移量top是否加上状态栏高度, 默认false (使用场景:取消原生导航栏时,配置此项可留出状态栏的占位, 支持传入字符串背景,如色值,背景图,渐变)\r\n\t * @property {String, Number} bottom 上拉布局往上的偏移量 (支持20, \"20rpx\", \"20px\", \"20%\"格式的值, 其中纯数字则默认单位rpx, 百分比则相对于windowHeight)\r\n\t * @property {Boolean} safearea 偏移量bottom是否加上底部安全区的距离, 默认false (需要适配iPhoneX时使用)\r\n\t * @property {Boolean} fixed 是否通过fixed固定mescroll的高度, 默认true\r\n\t * @property {String, Number} height 指定mescroll的高度, 此项有值,则不使用fixed. (支持20, \"20rpx\", \"20px\", \"20%\"格式的值, 其中纯数字则默认单位rpx, 百分比则相对于windowHeight)\r\n\t * @property {Boolean} bottombar 底部是否偏移TabBar的高度 (仅在H5端的tab页生效)\r\n\t * @property {Boolean} disableScroll 是否禁止滚动, 默认false\r\n\t * @event {Function} init 初始化完成的回调 \r\n\t * @event {Function} down 下拉刷新的回调\r\n\t * @event {Function} up 上拉加载的回调 \r\n\t * @event {Function} emptyclick 点击empty配置的btnText按钮回调\r\n\t * @event {Function} topclick 点击回到顶部的按钮回调\r\n\t * @event {Function} scroll 滚动监听 (需在 up 配置 onScroll:true 才生效)\r\n\t * @example <mescroll-uni @init=\"mescrollInit\" @down=\"downCallback\" @up=\"upCallback\"> ... </mescroll-uni>\r\n\t */\r\n\texport default {\r\n\t\tname: 'mescroll-uni',\r\n\t\tmixins: [WxsMixin],\r\n\t\tcomponents: {\r\n\t\t\tMescrollTop\r\n\t\t},\r\n\t\tprops: {\r\n\t\t\tdown: Object,\r\n\t\t\tup: Object,\r\n\t\t\ti18n: Object,\r\n\t\t\ttop: [String, Number],\r\n\t\t\ttopbar: [Boolean, String],\r\n\t\t\tbottom: [String, Number],\r\n\t\t\tsafearea: Boolean,\r\n\t\t\tfixed: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: true\r\n\t\t\t},\r\n\t\t\theight: [String, Number],\r\n\t\t\tbottombar:{\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: true\r\n\t\t\t},\r\n\t\t\tdisableScroll: Boolean\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tmescroll: {optDown:{},optUp:{}}, // mescroll实例\r\n\t\t\t\tviewId: 'id_' + Math.random().toString(36).substr(2,16), // 随机生成mescroll的id(不能数字开头,否则找不到元素)\r\n\t\t\t\tdownHight: 0, //下拉刷新: 容器高度\r\n\t\t\t\tdownRate: 0, // 下拉比率(inOffset: rate<1; outOffset: rate>=1)\r\n\t\t\t\tdownLoadType: 0, // 下拉刷新状态: 0(loading前), 1(inOffset), 2(outOffset), 3(showLoading), 4(endDownScroll)\r\n\t\t\t\tupLoadType: 0, // 上拉加载状态: 0(loading前), 1loading中, 2没有更多了,显示END文本提示, 3(没有更多了,不显示END文本提示)\r\n\t\t\t\tisShowEmpty: false, // 是否显示空布局\r\n\t\t\t\tisShowToTop: false, // 是否显示回到顶部按钮\r\n\t\t\t\tscrollTop: 0, // 滚动条的位置\r\n\t\t\t\tscrollAnim: false, // 是否开启滚动动画\r\n\t\t\t\twindowTop: 0, // 可使用窗口的顶部位置\r\n\t\t\t\twindowBottom: 0, // 可使用窗口的底部位置\r\n\t\t\t\twindowHeight: 0, // 可使用窗口的高度\r\n\t\t\t\tstatusBarHeight: 0 // 状态栏高度\r\n\t\t\t}\r\n\t\t},\r\n\t\twatch: {\r\n\t\t\theight() {\r\n\t\t\t\t// 设置容器的高度\r\n\t\t\t\tthis.setClientHeight()\r\n\t\t\t}\r\n\t\t},\r\n\t\tcomputed: {\r\n\t\t\t// 是否使用fixed定位 (当height有值,则不使用)\r\n\t\t\tisFixed(){\r\n\t\t\t\treturn !this.height && this.fixed\r\n\t\t\t},\r\n\t\t\t// mescroll的高度\r\n\t\t\tscrollHeight(){\r\n\t\t\t\tif (this.isFixed) {\r\n\t\t\t\t\treturn \"auto\"\r\n\t\t\t\t} else if(this.height){\r\n\t\t\t\t\treturn this.toPx(this.height) + 'px'\r\n\t\t\t\t}else{\r\n\t\t\t\t\treturn \"100%\"\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t// 下拉布局往下偏移的距离 (px)\r\n\t\t\tnumTop() {\r\n\t\t\t\treturn this.toPx(this.top)\r\n\t\t\t},\r\n\t\t\tfixedTop() {\r\n\t\t\t\treturn this.isFixed ? (this.numTop + this.windowTop) + 'px' : 0\r\n\t\t\t},\r\n\t\t\tpadTop() {\r\n\t\t\t\treturn !this.isFixed ? this.numTop + 'px' : 0\r\n\t\t\t},\r\n\t\t\t// 上拉布局往上偏移 (px)\r\n\t\t\tnumBottom() {\r\n\t\t\t\treturn this.toPx(this.bottom)\r\n\t\t\t},\r\n\t\t\tfixedBottom() {\r\n\t\t\t\treturn this.isFixed ? (this.numBottom + this.windowBottom) + 'px' : 0\r\n\t\t\t},\r\n\t\t\tpadBottom() {\r\n\t\t\t\treturn !this.isFixed ? this.numBottom + 'px' : 0\r\n\t\t\t},\r\n\t\t\t// 是否为重置下拉的状态\r\n\t\t\tisDownReset(){\r\n\t\t\t\treturn this.downLoadType===3 || this.downLoadType===4\r\n\t\t\t},\r\n\t\t\t// 过渡\r\n\t\t\ttransition() {\r\n\t\t\t\treturn this.isDownReset ? 'transform 300ms' : '';\r\n\t\t\t},\r\n\t\t\ttranslateY() {\r\n\t\t\t\treturn this.downHight > 0 ? 'translateY(' + this.downHight + 'px)' : ''; // transform会使fixed失效,需注意把fixed元素写在mescroll之外\r\n\t\t\t},\r\n\t\t\t// 列表是否可滑动\r\n\t\t\tscrollable(){\r\n\t\t\t\tif(this.disableScroll) return false\r\n\t\t\t\treturn this.downLoadType===0 || this.isDownReset\r\n\t\t\t},\r\n\t\t\t// 是否在加载中\r\n\t\t\tisDownLoading(){\r\n\t\t\t\treturn this.downLoadType === 3\r\n\t\t\t},\r\n\t\t\t// 旋转的角度\r\n\t\t\tdownRotate(){\r\n\t\t\t\treturn 'rotate(' + 360 * this.downRate + 'deg)'\r\n\t\t\t},\r\n\t\t\t// 文本提示\r\n\t\t\tdownText(){\r\n\t\t\t\tif(!this.mescroll) return \"\"; // 避免头条小程序初始化时报错\r\n\t\t\t\tswitch (this.downLoadType){\r\n\t\t\t\t\tcase 1: return this.mescroll.optDown.textInOffset;\r\n\t\t\t\t\tcase 2: return this.mescroll.optDown.textOutOffset;\r\n\t\t\t\t\tcase 3: return this.mescroll.optDown.textLoading;\r\n\t\t\t\t\tcase 4: return this.mescroll.isDownEndSuccess ? this.mescroll.optDown.textSuccess : this.mescroll.isDownEndSuccess==false ? this.mescroll.optDown.textErr : this.mescroll.optDown.textInOffset;\r\n\t\t\t\t\tdefault: return this.mescroll.optDown.textInOffset;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\t//number,rpx,upx,px,% --> px的数值\r\n\t\t\ttoPx(num){\r\n\t\t\t\tif(typeof num === \"string\"){\r\n\t\t\t\t\tif (num.indexOf('px') !== -1) {\r\n\t\t\t\t\t\tif(num.indexOf('rpx') !== -1) { // \"10rpx\"\r\n\t\t\t\t\t\t\tnum = num.replace('rpx', '');\r\n\t\t\t\t\t\t} else if(num.indexOf('upx') !== -1) { // \"10upx\"\r\n\t\t\t\t\t\t\tnum = num.replace('upx', '');\r\n\t\t\t\t\t\t} else { // \"10px\"\r\n\t\t\t\t\t\t\treturn Number(num.replace('px', ''))\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}else if (num.indexOf('%') !== -1){\r\n\t\t\t\t\t\t// 传百分比,则相对于windowHeight,传\"10%\"则等于windowHeight的10%\r\n\t\t\t\t\t\tlet rate = Number(num.replace(\"%\",\"\")) / 100\r\n\t\t\t\t\t\treturn this.windowHeight * rate\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\treturn num ? uni.upx2px(Number(num)) : 0\r\n\t\t\t},\r\n\t\t\t//注册列表滚动事件,用于下拉刷新和上拉加载\r\n\t\t\tscroll(e) {\r\n\t\t\t\tthis.mescroll.scroll(e.detail, () => {\r\n\t\t\t\t\tthis.$emit('scroll', this.mescroll) // 此时可直接通过 this.mescroll.scrollTop获取滚动条位置; this.mescroll.isScrollUp获取是否向上滑动\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t// 点击空布局的按钮回调\r\n\t\t\temptyClick() {\r\n\t\t\t\tthis.$emit('emptyclick', this.mescroll)\r\n\t\t\t},\r\n\t\t\t// 点击回到顶部的按钮回调\r\n\t\t\ttoTopClick() {\r\n\t\t\t\tthis.mescroll.scrollTo(0, this.mescroll.optUp.toTop.duration); // 执行回到顶部\r\n\t\t\t\tthis.$emit('topclick', this.mescroll); // 派发点击回到顶部按钮的回调\r\n\t\t\t},\r\n\t\t\t// 更新滚动区域的高度 (使内容不满屏和到底,都可继续翻页)\r\n\t\t\tsetClientHeight() {\r\n\t\t\t\tif (!this.isExec) {\r\n\t\t\t\t\tthis.isExec = true; // 避免多次获取\r\n\t\t\t\t\tthis.$nextTick(() => { // 确保dom已渲染\r\n\t\t\t\t\t\tthis.getClientInfo(data=>{\r\n\t\t\t\t\t\t\tthis.isExec = false;\r\n\t\t\t\t\t\t\tif (data) {\r\n\t\t\t\t\t\t\t\tthis.mescroll.setClientHeight(data.height);\r\n\t\t\t\t\t\t\t} else if (this.clientNum != 3) { // 极少部分情况,可能dom还未渲染完毕,递归获取,最多重试3次\r\n\t\t\t\t\t\t\t\tthis.clientNum = this.clientNum == null ? 1 : this.clientNum + 1;\r\n\t\t\t\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\t\t\t\tthis.setClientHeight()\r\n\t\t\t\t\t\t\t\t}, this.clientNum * 100)\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t// 获取滚动区域的信息\r\n\t\t\tgetClientInfo(success){\r\n\t\t\t\tlet query = uni.createSelectorQuery().in(this);\r\n\t\t\t\tlet view = query.select('#' + this.viewId);\r\n\t\t\t\tview.boundingClientRect(data => {\r\n\t\t\t\t\tsuccess(data)\r\n\t\t\t\t}).exec();\r\n\t\t\t}\r\n\t\t},\r\n\t\t// 使用created初始化mescroll对象; 如果用mounted部分css样式编译到H5会失效\r\n\t\tcreated() {\r\n\t\t\tlet vm = this;\r\n\r\n\t\t\tlet diyOption = {\r\n\t\t\t\t// 下拉刷新的配置\r\n\t\t\t\tdown: {\r\n\t\t\t\t\tinOffset() {\r\n\t\t\t\t\t\tvm.downLoadType = 1; // 下拉的距离进入offset范围内那一刻的回调 (自定义mescroll组件时,此行不可删)\r\n\t\t\t\t\t},\r\n\t\t\t\t\toutOffset() {\r\n\t\t\t\t\t\tvm.downLoadType = 2; // 下拉的距离大于offset那一刻的回调 (自定义mescroll组件时,此行不可删)\r\n\t\t\t\t\t},\r\n\t\t\t\t\tonMoving(mescroll, rate, downHight) {\r\n\t\t\t\t\t\t// 下拉过程中的回调,滑动过程一直在执行;\r\n\t\t\t\t\t\tvm.downHight = downHight; // 设置下拉区域的高度 (自定义mescroll组件时,此行不可删)\r\n\t\t\t\t\t\tvm.downRate = rate; //下拉比率 (inOffset: rate<1; outOffset: rate>=1)\r\n\t\t\t\t\t},\r\n\t\t\t\t\tshowLoading(mescroll, downHight) {\r\n\t\t\t\t\t\tvm.downLoadType = 3; // 显示下拉刷新进度的回调 (自定义mescroll组件时,此行不可删)\r\n\t\t\t\t\t\tvm.downHight = downHight; // 设置下拉区域的高度 (自定义mescroll组件时,此行不可删)\r\n\t\t\t\t\t},\r\n\t\t\t\t\tbeforeEndDownScroll(mescroll){\r\n\t\t\t\t\t\tvm.downLoadType = 4; \r\n\t\t\t\t\t\treturn mescroll.optDown.beforeEndDelay // 延时结束的时长\r\n\t\t\t\t\t},\r\n\t\t\t\t\tendDownScroll() {\r\n\t\t\t\t\t\tvm.downLoadType = 4; // 结束下拉 (自定义mescroll组件时,此行不可删)\r\n\t\t\t\t\t\tvm.downHight = 0; // 设置下拉区域的高度 (自定义mescroll组件时,此行不可删)\r\n\t\t\t\t\t\tvm.downResetTimer && clearTimeout(vm.downResetTimer)\r\n\t\t\t\t\t\tvm.downResetTimer = setTimeout(()=>{ // 过渡动画执行完毕后,需重置为0的状态,以便置空this.transition,避免iOS小程序列表渲染不完整\r\n\t\t\t\t\t\t\tif(vm.downLoadType===4) vm.downLoadType = 0\r\n\t\t\t\t\t\t},300)\r\n\t\t\t\t\t},\r\n\t\t\t\t\t// 派发下拉刷新的回调\r\n\t\t\t\t\tcallback: function(mescroll) {\r\n\t\t\t\t\t\tvm.$emit('down', mescroll)\r\n\t\t\t\t\t}\r\n\t\t\t\t},\r\n\t\t\t\t// 上拉加载的配置\r\n\t\t\t\tup: {\r\n\t\t\t\t\t// 显示加载中的回调\r\n\t\t\t\t\tshowLoading() {\r\n\t\t\t\t\t\tvm.upLoadType = 1;\r\n\t\t\t\t\t},\r\n\t\t\t\t\t// 显示无更多数据的回调\r\n\t\t\t\t\tshowNoMore() {\r\n\t\t\t\t\t\tvm.upLoadType = 2;\r\n\t\t\t\t\t},\r\n\t\t\t\t\t// 隐藏上拉加载的回调\r\n\t\t\t\t\thideUpScroll(mescroll) {\r\n\t\t\t\t\t\tvm.upLoadType = mescroll.optUp.hasNext ? 0 : 3;\r\n\t\t\t\t\t},\r\n\t\t\t\t\t// 空布局\r\n\t\t\t\t\tempty: {\r\n\t\t\t\t\t\tonShow(isShow) { // 显示隐藏的回调\r\n\t\t\t\t\t\t\tvm.isShowEmpty = isShow;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t},\r\n\t\t\t\t\t// 回到顶部\r\n\t\t\t\t\ttoTop: {\r\n\t\t\t\t\t\tonShow(isShow) { // 显示隐藏的回调\r\n\t\t\t\t\t\t\tvm.isShowToTop = isShow;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t},\r\n\t\t\t\t\t// 派发上拉加载的回调\r\n\t\t\t\t\tcallback: function(mescroll) {\r\n\t\t\t\t\t\tvm.$emit('up', mescroll);\r\n\t\t\t\t\t\t// 更新容器的高度 (多mescroll的情况)\r\n\t\t\t\t\t\tvm.setClientHeight()\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\tlet i18nType = mescrollI18n.getType() // 当前语言类型\r\n\t\t\tlet i18nOption = {type: i18nType} // 国际化配置\r\n\t\t\tMeScroll.extend(i18nOption, vm.i18n) // 具体页面的国际化配置\r\n\t\t\tMeScroll.extend(i18nOption, GlobalOption.i18n) // 全局的国际化配置\r\n\t\t\tMeScroll.extend(diyOption, i18nOption[i18nType]); // 混入国际化配置\r\n\t\t\tMeScroll.extend(diyOption, {down:GlobalOption.down, up:GlobalOption.up}); // 混入全局的配置\r\n\t\t\tlet myOption = JSON.parse(JSON.stringify({'down': vm.down,'up': vm.up})) // 深拷贝,避免对props的影响\r\n\t\t\tMeScroll.extend(myOption, diyOption); // 混入具体界面的配置\r\n\r\n\t\t\t// 初始化MeScroll对象\r\n\t\t\tvm.mescroll = new MeScroll(myOption);\r\n\t\t\tvm.mescroll.viewId = vm.viewId; // 附带id\r\n\t\t\tvm.mescroll.i18n = i18nOption; // 挂载语言包\r\n\t\t\t// init回调mescroll对象\r\n\t\t\tvm.$emit('init', vm.mescroll);\r\n\t\t\t\r\n\t\t\t// 设置高度\r\n\t\t\tconst sys = uni.getSystemInfoSync();\r\n\t\t\tif(sys.windowTop) vm.windowTop = sys.windowTop;\r\n\t\t\tif(sys.windowBottom) vm.windowBottom = sys.windowBottom;\r\n\t\t\tif(sys.windowHeight) vm.windowHeight = sys.windowHeight;\r\n\t\t\tif(sys.statusBarHeight) vm.statusBarHeight = sys.statusBarHeight;\r\n\t\t\t// 使down的bottomOffset生效\r\n\t\t\tvm.mescroll.setBodyHeight(sys.windowHeight);\r\n\r\n\t\t\t// 因为使用的是scrollview,这里需自定义scrollTo\r\n\t\t\tvm.mescroll.resetScrollTo((y, t) => {\r\n\t\t\t\tvm.scrollAnim = (t !== 0); // t为0,则不使用动画过渡\r\n\t\t\t\tif(typeof y === 'string'){\r\n\t\t\t\t\t// 小程序不支持slot里面的scroll-into-view, 统一使用计算的方式实现\r\n\t\t\t\t\tvm.getClientInfo(function(rect){\r\n\t\t\t\t\t\tlet mescrollTop = rect.top // mescroll到顶部的距离\r\n\t\t\t\t\t\tlet selector;\r\n\t\t\t\t\t\tif(y.indexOf('#')==-1 && y.indexOf('.')==-1){\r\n\t\t\t\t\t\t\tselector = '#'+y // 不带#和. 则默认为id选择器\r\n\t\t\t\t\t\t}else{\r\n\t\t\t\t\t\t\tselector = y\r\n\t\t\t\t\t\t\t// #ifdef APP-PLUS || H5 || MP-ALIPAY || MP-DINGTALK\r\n\t\t\t\t\t\t\tif(y.indexOf('>>>')!=-1){ // 不支持跨自定义组件的后代选择器 (转为普通的选择器即可跨组件查询)\r\n\t\t\t\t\t\t\t\tselector = y.split('>>>')[1].trim()\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t// #endif\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tuni.createSelectorQuery().select(selector).boundingClientRect(function(rect){\r\n\t\t\t\t\t\t\tif (rect) {\r\n\t\t\t\t\t\t\t\tlet curY = vm.mescroll.getScrollTop()\r\n\t\t\t\t\t\t\t\tlet top = rect.top - mescrollTop\r\n\t\t\t\t\t\t\t\ttop += curY\r\n\t\t\t\t\t\t\t\tif(!vm.isFixed) top -= vm.numTop\r\n\t\t\t\t\t\t\t\tvm.scrollTop = curY;\r\n\t\t\t\t\t\t\t\tvm.$nextTick(function() {\r\n\t\t\t\t\t\t\t\t\tvm.scrollTop = top\r\n\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t} else{\r\n\t\t\t\t\t\t\t\tconsole.error(selector + ' does not exist');\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}).exec()\r\n\t\t\t\t\t})\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tlet curY = vm.mescroll.getScrollTop()\r\n\t\t\t\tif (t === 0 || t === 300) { // 当t使用默认配置的300时,则使用系统自带的动画过渡\r\n\t\t\t\t\tvm.scrollTop = curY;\r\n\t\t\t\t\tvm.$nextTick(function() {\r\n\t\t\t\t\t\tvm.scrollTop = y\r\n\t\t\t\t\t})\r\n\t\t\t\t} else {\r\n\t\t\t\t\tvm.mescroll.getStep(curY, y, step => { // 此写法可支持配置t\r\n\t\t\t\t\t\tvm.scrollTop = step\r\n\t\t\t\t\t}, t)\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t\t\r\n\t\t\t// 具体的界面如果不配置up.toTop.safearea,则取本vue的safearea值\r\n\t\t\tif (vm.up && vm.up.toTop && vm.up.toTop.safearea != null) {} else {\r\n\t\t\t\tvm.mescroll.optUp.toTop.safearea = vm.safearea;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t// 全局配置监听\r\n\t\t\tuni.$on(\"setMescrollGlobalOption\", options=>{\r\n\t\t\t\tif(!options) return;\r\n\t\t\t\tlet i18nType = options.i18n ? options.i18n.type : null\r\n\t\t\t\tif(i18nType && vm.mescroll.i18n.type != i18nType){\r\n\t\t\t\t\tvm.mescroll.i18n.type = i18nType\r\n\t\t\t\t\tmescrollI18n.setType(i18nType)\r\n\t\t\t\t\tMeScroll.extend(options, vm.mescroll.i18n[i18nType])\r\n\t\t\t\t}\r\n\t\t\t\tif(options.down){\r\n\t\t\t\t\tlet down = MeScroll.extend({}, options.down)\r\n\t\t\t\t\tvm.mescroll.optDown = MeScroll.extend(down, vm.mescroll.optDown)\r\n\t\t\t\t}\r\n\t\t\t\tif(options.up){\r\n\t\t\t\t\tlet up = MeScroll.extend({}, options.up)\r\n\t\t\t\t\tvm.mescroll.optUp = MeScroll.extend(up, vm.mescroll.optUp)\r\n\t\t\t\t}\r\n\t\t\t})\r\n\t\t},\r\n\t\tmounted() {\r\n\t\t\t// 设置容器的高度\r\n\t\t\tthis.setClientHeight()\r\n\t\t},\r\n\t\tdestroyed() {\r\n\t\t\t// 注销全局配置监听\r\n\t\t\tuni.$off(\"setMescrollGlobalOption\")\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style>\r\n\t@import \"./mescroll-uni.css\";\r\n\t@import \"./components/mescroll-down.css\";\r\n\t@import './components/mescroll-up.css';\r\n</style>\r\n", "import mod from \"-!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./mescroll-uni.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./mescroll-uni.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1752590363492\n      var cssReload = require(\"D:/HBuilderX.3.3.13.20220314.full/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"../../\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  ", "import mod from \"-!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-filter-loader\\\\index.js!./wxs.wxs?vue&type=custom&index=0&blockType=script&issuerPath=E%3A%5CHome%5Cma-Yi%5Cgold%5Cuni_modules%5Cmescroll-uni%5Ccomponents%5Cmescroll-uni%5Cmescroll-uni.vue&module=wxsBiz&lang=wxs\"; export default mod; export * from \"-!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-filter-loader\\\\index.js!./wxs.wxs?vue&type=custom&index=0&blockType=script&issuerPath=E%3A%5CHome%5Cma-Yi%5Cgold%5Cuni_modules%5Cmescroll-uni%5Ccomponents%5Cmescroll-uni%5Cmescroll-uni.vue&module=wxsBiz&lang=wxs\"", "export default function (Component) {\n       if(!Component.options.wxsCallMethods){\n         Component.options.wxsCallMethods = []\n       }\n       Component.options.wxsCallMethods.push('wxsCall')\n     }"], "sourceRoot": ""}