{"version": 3, "sources": ["webpack:///E:/pos/黄金/gold_client/node_modules/uview-ui/components/u-waterfall/u-waterfall.vue?da0d", "webpack:///E:/pos/黄金/gold_client/node_modules/uview-ui/components/u-waterfall/u-waterfall.vue?8c9e", "webpack:///E:/pos/黄金/gold_client/node_modules/uview-ui/components/u-waterfall/u-waterfall.vue?69f9", "webpack:///E:/pos/黄金/gold_client/node_modules/uview-ui/components/u-waterfall/u-waterfall.vue?ae08", "uni-app:///node_modules/uview-ui/components/u-waterfall/u-waterfall.vue", "webpack:///E:/pos/黄金/gold_client/node_modules/uview-ui/components/u-waterfall/u-waterfall.vue?d66e", "webpack:///E:/pos/黄金/gold_client/node_modules/uview-ui/components/u-waterfall/u-waterfall.vue?0b23"], "names": ["name", "props", "value", "type", "required", "default", "addTime", "id<PERSON><PERSON>", "data", "leftList", "rightList", "tempList", "children", "watch", "copyFlowList", "mounted", "computed", "methods", "splitData", "leftRect", "rightRect", "item", "setTimeout", "cloneData", "clear", "remove", "index", "modify"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAoI;AACpI;AAC+D;AACL;AACsC;;;AAGhG;AACgL;AAChL,gBAAgB,yLAAU;AAC1B,EAAE,iFAAM;AACR,EAAE,kGAAM;AACR,EAAE,2GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,sGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AClBA;AAAA;AAAA;AAAA;AAA4pB,CAAgB,irBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACQhrB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAPA,gBAQA;EACAA;EACAC;IACAC;MACA;MACAC;MACAC;MACAC;QACA;MACA;IACA;IACA;IACA;IACAC;MACAH;MACAE;IACA;IACA;IACA;IACAE;MACAJ;MACAE;IACA;EACA;EACAG;IACA;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACAC;MACA;MACA;MACA;MACA;MACA;IACA;EACA;EACAC;IACA;IACA;EACA;EACAC;IACA;IACAF;MACA;IACA;EACA;EACAG;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,IACA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA,OACA;cAAA;gBAAAC;gBAAA;gBAAA,OACA;cAAA;gBAAAC;gBACA;gBACAC,0BACA;gBACA;gBAAA,IACAA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBACA;kBACA;gBACA;kBACA;gBACA;kBACA;kBACA;kBACA;oBACA;kBACA;oBACA;kBACA;gBACA;gBACA;gBACA;gBACA;gBACA;kBACAC;oBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;MACA;MACA;MACA;MACA;IACA;IACA;IACAC;MAAA;MACA;MACA;MACAC;QAAA;MAAA;MACA;QACA;QACA;MACA;QACA;QACAA;UAAA;QAAA;QACA;MACA;MACA;MACAA;QAAA;MAAA;MACA;IACA;IACA;IACAC;MAAA;MACA;MACA;MACAD;QAAA;MAAA;MACA;QACA;QACA;MACA;QACA;QACAA;UAAA;QAAA;QACA;MACA;MACA;MACAA;QAAA;MAAA;MACA;QACA;QACA;QACA;QACAlB;QACA;QACA;MACA;IACA;EACA;AACA;AAAA,4B;;;;;;;;;;;;ACzJA;AAAA;AAAA;AAAA;AAA2wC,CAAgB,wuCAAG,EAAC,C;;;;;;;;;;;ACA/xC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "node-modules/uview-ui/components/u-waterfall/u-waterfall.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./u-waterfall.vue?vue&type=template&id=07df8d1d&scoped=true&\"\nvar renderjs\nimport script from \"./u-waterfall.vue?vue&type=script&lang=js&\"\nexport * from \"./u-waterfall.vue?vue&type=script&lang=js&\"\nimport style0 from \"./u-waterfall.vue?vue&type=style&index=0&id=07df8d1d&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"07df8d1d\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"node_modules/uview-ui/components/u-waterfall/u-waterfall.vue\"\nexport default component.exports", "export * from \"-!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-waterfall.vue?vue&type=template&id=07df8d1d&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  _vm.$initSSP()\n  if (_vm.$scope.data.scopedSlotsCompiler === \"augmented\") {\n    _vm.$setSSP(\"left\", {\n      leftList: _vm.leftList,\n    })\n    _vm.$setSSP(\"right\", {\n      rightList: _vm.rightList,\n    })\n  }\n  _vm.$callSSP()\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-waterfall.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-waterfall.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"u-waterfall\">\r\n\t\t<view id=\"u-left-column\" class=\"u-column\"><slot name=\"left\" :leftList=\"leftList\"></slot></view>\r\n\t\t<view id=\"u-right-column\" class=\"u-column\"><slot name=\"right\" :rightList=\"rightList\"></slot></view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n/**\r\n * waterfall 瀑布流\r\n * @description 这是一个瀑布流形式的组件，内容分为左右两列，结合uView的懒加载组件效果更佳。相较于某些只是奇偶数左右分别，或者没有利用vue作用域插槽的做法，uView的瀑布流实现了真正的 组件化，搭配LazyLoad 懒加载和loadMore 加载更多组件，让您开箱即用，眼前一亮。\r\n * @tutorial https://www.uviewui.com/components/waterfall.html\r\n * @property {Array} flow-list 用于渲染的数据\r\n * @property {String Number} add-time 单条数据添加到队列的时间间隔，单位ms，见上方注意事项说明（默认200）\r\n * @example <u-waterfall :flowList=\"flowList\"></u-waterfall>\r\n */\r\nexport default {\r\n\tname: \"u-waterfall\",\r\n\tprops: {\r\n\t\tvalue: {\r\n\t\t\t// 瀑布流数据\r\n\t\t\ttype: Array,\r\n\t\t\trequired: true,\r\n\t\t\tdefault: function() {\r\n\t\t\t\treturn [];\r\n\t\t\t}\r\n\t\t},\r\n\t\t// 每次向结构插入数据的时间间隔，间隔越长，越能保证两列高度相近，但是对用户体验越不好\r\n\t\t// 单位ms\r\n\t\taddTime: {\r\n\t\t\ttype: [Number, String],\r\n\t\t\tdefault: 200\r\n\t\t},\r\n\t\t// id值，用于清除某一条数据时，根据此idKey名称找到并移除，如数据为{idx: 22, name: 'lisa'}\r\n\t\t// 那么该把idKey设置为idx\r\n\t\tidKey: {\r\n\t\t\ttype: String,\r\n\t\t\tdefault: 'id'\r\n\t\t}\r\n\t},\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\tleftList: [],\r\n\t\t\trightList: [],\r\n\t\t\ttempList: [],\r\n\t\t\tchildren: []\r\n\t\t}\r\n\t},\r\n\twatch: {\r\n\t\tcopyFlowList(nVal, oVal) {\r\n\t\t\t// 取差值，即这一次数组变化新增的部分\r\n\t\t\tlet startIndex = Array.isArray(oVal) && oVal.length > 0 ? oVal.length : 0;\r\n\t\t\t// 拼接上原有数据\r\n\t\t\tthis.tempList = this.tempList.concat(this.cloneData(nVal.slice(startIndex)));\r\n\t\t\tthis.splitData();\r\n\t\t}\r\n\t},\r\n\tmounted() {\r\n\t\tthis.tempList = this.cloneData(this.copyFlowList);\r\n\t\tthis.splitData();\r\n\t},\r\n\tcomputed: {\r\n\t\t// 破坏flowList变量的引用，否则watch的结果新旧值是一样的\r\n\t\tcopyFlowList() {\r\n\t\t\treturn this.cloneData(this.value);\r\n\t\t}\r\n\t},\r\n\tmethods: {\r\n\t\tasync splitData() {\r\n\t\t\tif (!this.tempList.length) return;\r\n\t\t\tlet leftRect = await this.$uGetRect('#u-left-column');\r\n\t\t\tlet rightRect = await this.$uGetRect('#u-right-column');\r\n\t\t\t// 如果左边小于或等于右边，就添加到左边，否则添加到右边\r\n\t\t\tlet item = this.tempList[0];\r\n\t\t\t// 解决多次快速上拉后，可能数据会乱的问题，因为经过上面的两个await节点查询阻塞一定时间，加上后面的定时器干扰\r\n\t\t\t// 数组可能变成[]，导致此item值可能为undefined\r\n\t\t\tif(!item) return ;\r\n\t\t\tif (leftRect.height < rightRect.height) {\r\n\t\t\t\tthis.leftList.push(item);\r\n\t\t\t} else if (leftRect.height > rightRect.height) {\r\n\t\t\t\tthis.rightList.push(item);\r\n\t\t\t} else {\r\n\t\t\t\t// 这里是为了保证第一和第二张添加时，左右都能有内容\r\n\t\t\t\t// 因为添加第一张，实际队列的高度可能还是0，这时需要根据队列元素长度判断下一个该放哪边\r\n\t\t\t\tif (this.leftList.length <= this.rightList.length) {\r\n\t\t\t\t\tthis.leftList.push(item);\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.rightList.push(item);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t// 移除临时列表的第一项\r\n\t\t\tthis.tempList.splice(0, 1);\r\n\t\t\t// 如果临时数组还有数据，继续循环\r\n\t\t\tif (this.tempList.length) {\r\n\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\tthis.splitData();\r\n\t\t\t\t}, this.addTime)\r\n\t\t\t}\r\n\t\t},\r\n\t\t// 复制而不是引用对象和数组\r\n\t\tcloneData(data) {\r\n\t\t\treturn JSON.parse(JSON.stringify(data));\r\n\t\t},\r\n\t\t// 清空数据列表\r\n\t\tclear() {\r\n\t\t\tthis.leftList = [];\r\n\t\t\tthis.rightList = [];\r\n\t\t\t// 同时清除父组件列表中的数据\r\n\t\t\tthis.$emit('input', []);\r\n\t\t\tthis.tempList = [];\r\n\t\t},\r\n\t\t// 清除某一条指定的数据，根据id实现\r\n\t\tremove(id) {\r\n\t\t\t// 如果findIndex找不到合适的条件，就会返回-1\r\n\t\t\tlet index = -1;\r\n\t\t\tindex = this.leftList.findIndex(val => val[this.idKey] == id);\r\n\t\t\tif(index != -1) {\r\n\t\t\t\t// 如果index不等于-1，说明已经找到了要找的id，根据index索引删除这一条数据\r\n\t\t\t\tthis.leftList.splice(index, 1);\r\n\t\t\t} else {\r\n\t\t\t\t// 同理于上方面的方法\r\n\t\t\t\tindex = this.rightList.findIndex(val => val[this.idKey] == id);\r\n\t\t\t\tif(index != -1) this.rightList.splice(index, 1);\r\n\t\t\t}\r\n\t\t\t// 同时清除父组件的数据中的对应id的条目\r\n\t\t\tindex = this.value.findIndex(val => val[this.idKey] == id);\r\n\t\t\tif(index != -1) this.$emit('input', this.value.splice(index, 1));\r\n\t\t},\r\n\t\t// 修改某条数据的某个属性\r\n\t\tmodify(id, key, value) {\r\n\t\t\t// 如果findIndex找不到合适的条件，就会返回-1\r\n\t\t\tlet index = -1;\r\n\t\t\tindex = this.leftList.findIndex(val => val[this.idKey] == id);\r\n\t\t\tif(index != -1) {\r\n\t\t\t\t// 如果index不等于-1，说明已经找到了要找的id，修改对应key的值\r\n\t\t\t\tthis.leftList[index][key] = value;\r\n\t\t\t} else {\r\n\t\t\t\t// 同理于上方面的方法\r\n\t\t\t\tindex = this.rightList.findIndex(val => val[this.idKey] == id);\r\n\t\t\t\tif(index != -1) this.rightList[index][key] = value;\r\n\t\t\t}\r\n\t\t\t// 修改父组件的数据中的对应id的条目\r\n\t\t\tindex = this.value.findIndex(val => val[this.idKey] == id);\r\n\t\t\tif(index != -1) {\r\n\t\t\t\t// 首先复制一份value的数据\r\n\t\t\t\tlet data = this.cloneData(this.value);\r\n\t\t\t\t// 修改对应索引的key属性的值为value\r\n\t\t\t\tdata[index][key] = value;\r\n\t\t\t\t// 修改父组件通过v-model绑定的变量的值\r\n\t\t\t\tthis.$emit('input', data);\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n@import \"../../libs/css/style.components.scss\";\r\n\r\n.u-waterfall {\r\n\t@include vue-flex;\r\n\tflex-direction: row;\r\n\talign-items: flex-start;\r\n}\r\n\r\n.u-column {\r\n\t@include vue-flex;\r\n\tflex: 1;\r\n\tflex-direction: column;\r\n\theight: auto;\r\n}\r\n\r\n.u-image {\r\n\twidth: 100%;\r\n}\r\n</style>\r\n", "import mod from \"-!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-waterfall.vue?vue&type=style&index=0&id=07df8d1d&lang=scss&scoped=true&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-waterfall.vue?vue&type=style&index=0&id=07df8d1d&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754273099913\n      var cssReload = require(\"D:/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}