{"version": 3, "sources": ["uni-app:///main.js", "webpack:///E:/pos/黄金/gold_client/pages/Set/Set.vue?0fd0", "webpack:///E:/pos/黄金/gold_client/pages/Set/Set.vue?1268", "webpack:///E:/pos/黄金/gold_client/pages/Set/Set.vue?5e40", "webpack:///E:/pos/黄金/gold_client/pages/Set/Set.vue?85f4", "uni-app:///pages/Set/Set.vue", "webpack:///E:/pos/黄金/gold_client/pages/Set/Set.vue?d9f8", "webpack:///E:/pos/黄金/gold_client/pages/Set/Set.vue?f1da"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "curVersion", "curVersionCode", "number", "userInfo", "totalAmt", "balance", "phone", "changeInform", "onShow", "onLoad", "methods", "toupdataPassd", "uni", "url", "getApp", "plus", "upadetaApp", "title", "setTimeout", "icon", "logOut"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,YAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAgH;AAChH;AACuD;AACL;AACa;;;AAG/D;AACgL;AAChL,gBAAgB,yLAAU;AAC1B,EAAE,yEAAM;AACR,EAAE,8EAAM;AACR,EAAE,uFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,kFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAopB,CAAgB,yqBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;ACqBxqB;;;;;;;;;;;;;;;;;;;;AAFA;AACA;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MAAA;MACAC;MACAC;MACAC;MACAC;MACAC;QACA;QACA;QACA;MACA;IACA;EACA;EACAC;IACA;EAAA,CAIA;EACAC;IACA;EAAA,CACA;EACAC;IACAC;MACAC;QACAC;MACA;IACA;IAEA;IACAC;MAAA;MACAC;QACA;QACA;MACA;IACA;IACA;IACAC;MACAJ;QACAK;MACA;MACAC;QACAN;UACAK;UACAE;QACA;MACA;IACA;IACAC;MACAR;MACAA;MACAA;QACAC;MACA;IACA;EACA;AAEA;AAAA,2B;;;;;;;;;;;;;ACnFA;AAAA;AAAA;AAAA;AAA47B,CAAgB,s7BAAG,EAAC,C;;;;;;;;;;;ACAh9B;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/Set/Set.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/Set/Set.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./Set.vue?vue&type=template&id=944debe8&\"\nvar renderjs\nimport script from \"./Set.vue?vue&type=script&lang=js&\"\nexport * from \"./Set.vue?vue&type=script&lang=js&\"\nimport style0 from \"./Set.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/Set/Set.vue\"\nexport default component.exports", "export * from \"-!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./Set.vue?vue&type=template&id=944debe8&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./Set.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./Set.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"\">\r\n\t\t<view class=\"set-item\" @click=\"upadetaApp\">\r\n\t\t\t<view class=\"\">\r\n\t\t\t\t检查更新\r\n\t\t\t</view>\r\n\t\t\t<view class=\"set-item-img\">\r\n\t\t\t\t<text>{{curVersion}}</text>\r\n\t\t\t\t<view class=\"right-icon\"></view>\r\n\t\t\t\t<!-- <image src=\"../../static/img/index/ic_next_black.png\" mode=\"widthFix\"></image> -->\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view class=\"\">\r\n\t\t\t<button type=\"default\" class=\"button\" @click=\"logOut\">退出登录</button>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\tconst api = require('../../config/api');\r\n\tconst util = require('../../utils/util');\r\n\timport number from \"../../utils/number.js\";\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tcurVersion: '',\r\n\t\t\t\tcurVersionCode: '',\r\n\t\t\t\tnumber: number, //声明number属性并赋值为引入的number模块\r\n\t\t\t\tuserInfo: {},\r\n\t\t\t\ttotalAmt: 0,\r\n\t\t\t\tbalance: 0,\r\n\t\t\t\tphone: '18205405955',\r\n\t\t\t\tchangeInform: {\r\n\t\t\t\t\t\"isVoiceBroadcast\": \"\",\r\n\t\t\t\t\t\"nickName\": \"\",\r\n\t\t\t\t\t\"userImg\": \"\"\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\t\tonShow() {\r\n\t\t\t// this.upadetaApp();\r\n\t\t\t//#ifdef APP-PLUS\r\n\t\t\tthis.getApp();\r\n\t\t\t//#endif\r\n\t\t},\r\n\t\tonLoad() {\r\n\t\t\t// this.getuserInfo();\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\ttoupdataPassd(){\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: '/pages/ForgotPassword/ForgotPassword'\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t\r\n\t\t\t// 版本更新\r\n\t\t\tgetApp() {\r\n\t\t\t\tplus.runtime.getProperty(plus.runtime.appid, (wgtinfo) => {\r\n\t\t\t\t\tthis.curVersion = wgtinfo.version; //应用版本名称\r\n\t\t\t\t\tthis.curVersionCode = wgtinfo.versionCode; //应用版本号\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t// 版本更新\r\n\t\t\tupadetaApp() {\r\n\t\t\t\tuni.showLoading({\r\n\t\t\t\t\ttitle: '正在检查更新'\r\n\t\t\t\t})\r\n\t\t\t\tsetTimeout(()=>{\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '已是最新版本',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t});\r\n\t\t\t\t},1000)\r\n\t\t\t},\r\n\t\t\tlogOut() {\r\n\t\t\t\tuni.removeStorageSync(\"token\");\r\n\t\t\t\tuni.removeStorageSync(\"store_info\");\r\n\t\t\t\tuni.reLaunch({\r\n\t\t\t\t\turl: '/pages/register/register'\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t}\r\n\r\n\t}\r\n</script>\r\n\r\n<style>\r\n\t.set-item {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: space-between;\r\n\t\tbackground-color: #fff;\r\n\t\tborder-bottom: 0.5px solid #F4F6FA;\r\n\t\tposition: relative;\r\n\t\tpadding: 15px;\r\n\t}\r\n\r\n\t.set-item-img {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t}\r\n\r\n\t.set-item-img image {\r\n\t\twidth: 10px;\r\n\t\theight: 20px;\r\n\t\tmargin-left: 10px;\r\n\t}\r\n\r\n/* \t.button {\r\n\t\twidth: 210px;\r\n\t\theight: 40px;\r\n\t\tbackground-color: #2C5FE5 !important;\r\n\t\tcolor: #fff !important;\r\n\t\tline-height: 40px;\r\n\t\tmargin-top: 30px;\r\n\t\tborder-radius: 20px;\r\n\t\tfont-size: 14px;\r\n\t} */\r\n\t.button {\r\n\t\t\r\n\t\twidth: 225px;\r\n\t\theight: 44px;\r\n\t\tmargin: 10px auto;\r\n\t\ttext-align: center;\r\n\t\t/* padding-right: 15px; */\r\n\t\tcolor: #fff !important;\r\n\t\tfont-size: 14px;\r\n\t\tline-height: 44px;\r\n\t\tbackground-color: #BBA186 !important;\r\n\t\tborder-radius: 5px;\r\n\t\tmargin-top: 20px;\r\n\t}\r\n</style>", "import mod from \"-!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./Set.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./Set.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754273096977\n      var cssReload = require(\"D:/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}