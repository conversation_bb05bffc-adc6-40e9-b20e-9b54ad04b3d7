(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-promotion-promotion"],{"458c":function(t,e,n){var a=n("24fb");e=a(!1),e.push([t.i,'@charset "UTF-8";\r\n/* uni.scss */\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 字体变量 */\r\n/* 颜色变量 */\r\n/* 行为相关颜色 */\r\n/* 文字基本颜色 */\r\n/* 背景颜色 */\r\n/* 边框颜色 */\r\n/* 尺寸变量 */\r\n/* 文字尺寸 */\r\n/* 图片尺寸 */\r\n/* Border Radius */\r\n/* 水平间距 */\r\n/* 垂直间距 */\r\n/* 透明度 */\r\n/* 文章场景相关 */.page[data-v-26009a07]{padding:%?24?% %?24?% 0 %?24?%}.page .store_content[data-v-26009a07]{display:flex;margin-bottom:%?24?%;padding:%?24?%;border-radius:%?10?%;background-color:#fff}.page .store_content .store_img[data-v-26009a07]{display:flex;border-radius:%?10?%;overflow:hidden}.page .store_content .store_name[data-v-26009a07]{flex:1;margin-left:%?24?%}.store_round[data-v-26009a07]{width:%?52?%;height:%?52?%;border-radius:50%;overflow:hidden;background-color:#f5f5f5;display:flex;align-items:center;margin-left:%?24?%;justify-content:center}.gold_new[data-v-26009a07]{padding:%?40?% 0;background:linear-gradient(94deg,#8f8174 -2.53%,#c4b39f 131.45%)}.gold_new uni-view[data-v-26009a07]{color:#fff;text-align:center;font-size:%?24?%}.gold_new .gold_new_title[data-v-26009a07]{font-size:%?36?%;padding-bottom:%?20?%}.gold_price_show[data-v-26009a07]{display:flex;justify-content:space-between;padding:%?24?% 0;border-bottom:%?1?% solid #f1f2f5}.goods_detail_footer[data-v-26009a07]{margin-top:%?32?%;width:100%;height:50px;display:flex;justify-content:center}.goods_detail_footer > uni-view[data-v-26009a07]{height:%?84?%;width:80%;border-radius:9999px;background-color:#bba186;color:#fff;font-size:%?28?%;text-align:center;line-height:%?84?%}',""]),t.exports=e},4847:function(t,e,n){var a=n("a32f");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var i=n("4f06").default;i("4fbfff0e",a,!0,{sourceMap:!1,shadowMode:!1})},"620e":function(t,e,n){"use strict";var a=n("8331"),i=n.n(a);i.a},"7d20":function(t,e,n){"use strict";n.d(e,"b",(function(){return i})),n.d(e,"c",(function(){return o})),n.d(e,"a",(function(){return a}));var a={uLoadmore:n("84c5").default,uPopup:n("24e3").default},i=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("v-uni-view",{staticClass:"page"},[t._l(t.storeList,(function(e){return n("v-uni-view",{key:e.id,staticClass:"store_content"},[n("v-uni-view",{staticClass:"store_img"},[n("v-uni-image",{staticStyle:{width:"160rpx",height:"160rpx"},attrs:{src:e.img,alt:""}})],1),n("v-uni-view",{staticClass:"store_name"},[n("v-uni-view",{staticStyle:{"margin-top":"10rpx","font-size":"28rpx"}},[t._v(t._s(e.name))]),n("v-uni-view",{staticStyle:{display:"flex","justify-content":"space-between","align-items":"center",color:"#61687C","font-size":"24rpx","margin-top":"10rpx"}},[n("v-uni-view",{staticStyle:{display:"flex"}},[n("v-uni-image",{staticStyle:{width:"28rpx"},attrs:{mode:"widthFix",src:"/static/img/shop/shop-clock.png",alt:""}}),n("v-uni-text",{staticStyle:{"margin-left":"10rpx"}},[t._v(t._s(e.openTime))])],1),n("v-uni-view",{staticStyle:{display:"flex"}},[n("v-uni-view",{staticClass:"store_round",on:{click:function(n){arguments[0]=n=t.$handleEvent(n),t.onCall(e.phone)}}},[n("v-uni-image",{staticStyle:{width:"28rpx"},attrs:{mode:"widthFix",src:"/static/img/shop/shop-calling.png",alt:""}})],1),n("v-uni-view",{staticClass:"store_round",on:{click:function(n){arguments[0]=n=t.$handleEvent(n),t.onStoreInfo(e)}}},[n("v-uni-image",{staticStyle:{width:"28rpx"},attrs:{mode:"widthFix",src:"/static/img/shop/shop-user.png",alt:""}})],1)],1)],1),n("v-uni-view",{staticStyle:{display:"flex",color:"#61687C","font-size":"24rpx","margin-top":"10rpx"}},[n("v-uni-image",{staticStyle:{width:"28rpx"},attrs:{mode:"widthFix",src:"/static/img/shop/shop-location.png",alt:""}}),n("v-uni-text",{staticStyle:{"margin-left":"10rpx"}},[t._v(t._s(e.address))])],1)],1)],1)})),n("u-loadmore",{attrs:{status:t.loadStatus,"load-text":t.loadText},on:{loadmore:function(e){arguments[0]=e=t.$handleEvent(e),t.addRandomData.apply(void 0,arguments)}}}),n("v-uni-view",{staticClass:"page_bottom"}),n("u-popup",{attrs:{mode:"center","border-radius":14,length:"90%","close-icon-color":"#FFFFFF",closeable:!0},model:{value:t.show,callback:function(e){t.show=e},expression:"show"}},[n("v-uni-view",[n("v-uni-view",{staticClass:"gold_new"},[n("v-uni-view",{staticClass:"gold_new_title"},[t._v("门店信息")])],1),n("v-uni-view",{staticStyle:{padding:"0 32rpx"}},[n("v-uni-view",{staticClass:"gold_price_show"},[n("v-uni-view",{staticStyle:{"font-size":"28rpx"}},[t._v("联系人：")]),n("v-uni-view",[n("v-uni-text",{staticStyle:{"font-size":"32rpx",color:"#BBA186"}},[t._v(t._s(t.storeInfo.userName))])],1)],1),n("v-uni-view",{staticClass:"gold_price_show"},[n("v-uni-view",{staticStyle:{"font-size":"28rpx"}},[t._v("联系方式：")]),n("v-uni-view",[n("v-uni-text",{staticStyle:{"font-size":"32rpx",color:"#BBA186"}},[t._v(t._s(t.storeInfo.phone))])],1)],1)],1),n("v-uni-view",{staticClass:"goods_detail_footer"},[n("v-uni-view",{on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.show=!1}}},[t._v("我知道了")])],1)],1)],1)],2)},o=[]},8331:function(t,e,n){var a=n("458c");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[t.i,a,""]]),a.locals&&(t.exports=a.locals);var i=n("4f06").default;i("3041c4f6",a,!0,{sourceMap:!1,shadowMode:!1})},a32f:function(t,e,n){var a=n("24fb");e=a(!1),e.push([t.i,"uni-page-body[data-v-26009a07]{background-color:#f8f8f8}body.?%PAGE?%[data-v-26009a07]{background-color:#f8f8f8}",""]),t.exports=e},a5c8:function(t,e,n){"use strict";n.r(e);var a=n("7d20"),i=n("ff91");for(var o in i)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(o);n("ba06"),n("620e");var s=n("f0c5"),r=Object(s["a"])(i["default"],a["b"],a["c"],!1,null,"26009a07",null,!1,a["a"],void 0);e["default"]=r.exports},ba06:function(t,e,n){"use strict";var a=n("4847"),i=n.n(a);i.a},d17e:function(t,e,n){"use strict";n("7a82");var a=n("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("99af");var i=a(n("c7eb")),o=a(n("1da1")),s=n("30ea"),r=n("9e56"),l={data:function(){return{storeList:[],page:1,limit:10,show:!1,storeInfo:{},loadStatus:"loading",loadText:{loadmore:"加载更多",loading:"努力加载中",nomore:"已经到底了"},isLoadAll:!1}},onLoad:function(t){console.log(t)},onShow:function(){var t=this;return(0,o.default)((0,i.default)().mark((function e(){return(0,i.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t.storeList=[],t.page=1,e.next=4,t.getStoreList();case 4:case"end":return e.stop()}}),e)})))()},onReachBottom:function(){this.isLoadAll||(this.page++,this.getStoreList())},methods:{getStoreList:function(){var t=this;return(0,o.default)((0,i.default)().mark((function e(){var n,a;return(0,i.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return n=t,n.loadStatus="loading",e.next=4,r.request(s.storeListUrl,{limit:n.limit,page:n.page,name:""},"POST");case 4:a=e.sent,0!==a.code?uni.showToast({title:a.msg,icon:"none"}):(n.storeList=n.storeList.concat(a.data.records),n.isLoadAll=n.page>=a.data.pages,n.loadStatus="nomore");case 6:case"end":return e.stop()}}),e)})))()},onCall:function(t){uni.makePhoneCall({phoneNumber:t})},onStoreInfo:function(t){var e=this;e.storeInfo={userName:t.userName,phone:t.phone},setTimeout((function(){e.show=!0}),100)}}};e.default=l},ff91:function(t,e,n){"use strict";n.r(e);var a=n("d17e"),i=n.n(a);for(var o in a)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(o);e["default"]=i.a}}]);