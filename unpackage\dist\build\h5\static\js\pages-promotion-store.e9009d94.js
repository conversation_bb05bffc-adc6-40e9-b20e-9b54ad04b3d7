(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-promotion-store"],{"03fb":function(t,e,n){"use strict";var i=n("4c58"),o=n.n(i);o.a},"0a33":function(t,e,n){"use strict";n.r(e);var i=n("6faa"),o=n.n(i);for(var a in i)["default"].indexOf(a)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(a);e["default"]=o.a},"2fc6":function(t,e,n){"use strict";n.r(e);var i=n("8251"),o=n("3d5e");for(var a in o)["default"].indexOf(a)<0&&function(t){n.d(e,t,(function(){return o[t]}))}(a);n("03fb"),n("e2f9");var r=n("f0c5"),s=Object(r["a"])(o["default"],i["b"],i["c"],!1,null,"2e8e08b9",null,!1,i["a"],void 0);e["default"]=s.exports},"3d5e":function(t,e,n){"use strict";n.r(e);var i=n("d708"),o=n.n(i);for(var a in i)["default"].indexOf(a)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(a);e["default"]=o.a},4439:function(t,e,n){var i=n("24fb");e=i(!1),e.push([t.i,'@charset "UTF-8";\r\n/* uni.scss */\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 字体变量 */\r\n/* 颜色变量 */\r\n/* 行为相关颜色 */\r\n/* 文字基本颜色 */\r\n/* 背景颜色 */\r\n/* 边框颜色 */\r\n/* 尺寸变量 */\r\n/* 文字尺寸 */\r\n/* 图片尺寸 */\r\n/* Border Radius */\r\n/* 水平间距 */\r\n/* 垂直间距 */\r\n/* 透明度 */\r\n/* 文章场景相关 */.page .store_content[data-v-2e8e08b9]{display:flex;margin:%?24?%;padding:%?24?%;border-radius:%?10?%;background-color:#fff}.page .store_content .store_img[data-v-2e8e08b9]{display:flex;border-radius:%?10?%;overflow:hidden}.page .store_content .store_name[data-v-2e8e08b9]{flex:1;margin-left:%?24?%}.gold_new[data-v-2e8e08b9]{padding:%?40?% 0;background:linear-gradient(94deg,#8f8174 -2.53%,#c4b39f 131.45%)}.gold_new uni-view[data-v-2e8e08b9]{color:#fff;text-align:center;font-size:%?24?%}.gold_new .gold_new_title[data-v-2e8e08b9]{font-size:%?36?%;padding-bottom:%?20?%}.gold_price_show[data-v-2e8e08b9]{display:flex;justify-content:space-between;padding:%?24?% 0;border-bottom:%?1?% solid #f1f2f5}.goods_detail_footer[data-v-2e8e08b9]{margin-top:%?32?%;width:100%;height:50px;display:flex;justify-content:center}.goods_detail_footer > uni-view[data-v-2e8e08b9]{height:%?84?%;width:80%;border-radius:9999px;background-color:#bba186;color:#fff;font-size:%?28?%;text-align:center;line-height:%?84?%}.store_round[data-v-2e8e08b9]{width:%?52?%;height:%?52?%;border-radius:50%;overflow:hidden;background-color:#f5f5f5;display:flex;align-items:center;margin-left:%?24?%;justify-content:center}',""]),t.exports=e},"4c58":function(t,e,n){var i=n("e36e");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var o=n("4f06").default;o("05d7cac4",i,!0,{sourceMap:!1,shadowMode:!1})},6846:function(t,e,n){"use strict";n.r(e);var i=n("894f"),o=n("0a33");for(var a in o)["default"].indexOf(a)<0&&function(t){n.d(e,t,(function(){return o[t]}))}(a);n("a35b");var r=n("f0c5"),s=Object(r["a"])(o["default"],i["b"],i["c"],!1,null,"bcc6c970",null,!1,i["a"],void 0);e["default"]=s.exports},"6faa":function(t,e,n){"use strict";n("7a82"),Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("a9e3");var i={name:"u-search",props:{shape:{type:String,default:"round"},bgColor:{type:String,default:"#f2f2f2"},placeholder:{type:String,default:"请输入关键字"},clearabled:{type:Boolean,default:!0},focus:{type:Boolean,default:!1},showAction:{type:Boolean,default:!0},actionStyle:{type:Object,default:function(){return{}}},actionText:{type:String,default:"搜索"},inputAlign:{type:String,default:"left"},disabled:{type:Boolean,default:!1},animation:{type:Boolean,default:!1},borderColor:{type:String,default:"none"},value:{type:String,default:""},height:{type:[Number,String],default:64},inputStyle:{type:Object,default:function(){return{}}},maxlength:{type:[Number,String],default:"-1"},searchIconColor:{type:String,default:""},color:{type:String,default:"#606266"},placeholderColor:{type:String,default:"#909399"},margin:{type:String,default:"0"},searchIcon:{type:String,default:"search"}},data:function(){return{keyword:"",showClear:!1,show:!1,focused:this.focus}},watch:{keyword:function(t){this.$emit("input",t),this.$emit("change",t)},value:{immediate:!0,handler:function(t){this.keyword=t}}},computed:{showActionBtn:function(){return!(this.animation||!this.showAction)},borderStyle:function(){return this.borderColor?"1px solid ".concat(this.borderColor):"none"}},methods:{inputChange:function(t){this.keyword=t.detail.value},clear:function(){var t=this;this.keyword="",this.$nextTick((function(){t.$emit("clear")}))},search:function(t){this.$emit("search",t.detail.value);try{uni.hideKeyboard()}catch(t){}},custom:function(){this.$emit("custom",this.keyword);try{uni.hideKeyboard()}catch(t){}},getFocus:function(){this.focused=!0,this.animation&&this.showAction&&(this.show=!0),this.$emit("focus",this.keyword)},blur:function(){var t=this;setTimeout((function(){t.focused=!1}),100),this.show=!1,this.$emit("blur",this.keyword)},clickHandler:function(){this.disabled&&this.$emit("click")}}};e.default=i},8251:function(t,e,n){"use strict";n.d(e,"b",(function(){return o})),n.d(e,"c",(function(){return a})),n.d(e,"a",(function(){return i}));var i={uSearch:n("6846").default,uLoadmore:n("84c5").default,uPopup:n("24e3").default},o=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("v-uni-view",{staticClass:"page"},[n("v-uni-view",{staticStyle:{"background-color":"#FFFFFF",padding:"10rpx 24rpx 24rpx 24rpx"}},[n("u-search",{attrs:{placeholder:"请输入提货点",clearabled:!0,"show-action":!0,"action-text":"搜索"},on:{custom:function(e){arguments[0]=e=t.$handleEvent(e),t.onSearch.apply(void 0,arguments)},search:function(e){arguments[0]=e=t.$handleEvent(e),t.onSearch.apply(void 0,arguments)}},model:{value:t.keyword,callback:function(e){t.keyword=e},expression:"keyword"}})],1),t._l(t.storeList,(function(e){return n("v-uni-view",{key:e.id,staticClass:"store_content"},[n("v-uni-view",{staticClass:"store_img"},[n("v-uni-image",{staticStyle:{width:"160rpx",height:"160rpx"},attrs:{src:e.img,alt:""}})],1),n("v-uni-view",{staticClass:"store_name"},[n("v-uni-view",{staticStyle:{"margin-top":"10rpx","font-size":"28rpx"}},[t._v(t._s(e.name))]),n("v-uni-view",{staticStyle:{display:"flex","justify-content":"space-between","align-items":"center",color:"#61687C","font-size":"24rpx","margin-top":"10rpx"}},[n("v-uni-view",{staticStyle:{display:"flex"}},[n("v-uni-image",{staticStyle:{width:"28rpx"},attrs:{mode:"widthFix",src:"/static/img/shop/shop-clock.png",alt:""}}),n("v-uni-text",{staticStyle:{"margin-left":"10rpx"}},[t._v(t._s(e.openTime))])],1),1==t.type?n("v-uni-view",{staticStyle:{display:"flex"}},[n("v-uni-view",{staticClass:"store_round",on:{click:function(n){arguments[0]=n=t.$handleEvent(n),t.onCall(e.phone)}}},[n("v-uni-image",{staticStyle:{width:"28rpx"},attrs:{mode:"widthFix",src:"/static/img/shop/shop-calling.png",alt:""}})],1),n("v-uni-view",{staticClass:"store_round",on:{click:function(n){arguments[0]=n=t.$handleEvent(n),t.onStoreInfo(e)}}},[n("v-uni-image",{staticStyle:{width:"28rpx"},attrs:{mode:"widthFix",src:"/static/img/shop/shop-user.png",alt:""}})],1)],1):n("v-uni-view",{staticStyle:{display:"flex"},on:{click:function(n){arguments[0]=n=t.$handleEvent(n),t.onChoose(e)}}},[n("v-uni-view",{staticStyle:{display:"flex"}},[n("v-uni-image",{staticStyle:{width:"48rpx",height:"48rpx"},attrs:{src:"/static/img/shop/shop_round"+(t.storeInfo.id==e.id?"_a":"")+".png",alt:""}})],1)],1)],1),n("v-uni-view",{staticStyle:{display:"flex",color:"#61687C","font-size":"24rpx","margin-top":"10rpx"}},[n("v-uni-image",{staticStyle:{width:"28rpx"},attrs:{mode:"widthFix",src:"/static/img/shop/shop-location.png",alt:""}}),n("v-uni-text",{staticStyle:{"margin-left":"10rpx"}},[t._v(t._s(e.address))])],1)],1)],1)})),n("u-loadmore",{attrs:{status:t.loadStatus,"load-text":t.loadText},on:{loadmore:function(e){arguments[0]=e=t.$handleEvent(e),t.addRandomData.apply(void 0,arguments)}}}),n("u-popup",{attrs:{mode:"center","border-radius":14,length:"90%","close-icon-color":"#FFFFFF",closeable:!0},model:{value:t.show,callback:function(e){t.show=e},expression:"show"}},[n("v-uni-view",[n("v-uni-view",{staticClass:"gold_new"},[n("v-uni-view",{staticClass:"gold_new_title"},[t._v("门店信息")])],1),n("v-uni-view",{staticStyle:{padding:"0 32rpx"}},[n("v-uni-view",{staticClass:"gold_price_show"},[n("v-uni-view",{staticStyle:{"font-size":"28rpx"}},[t._v("联系人：")]),n("v-uni-view",[n("v-uni-text",{staticStyle:{"font-size":"32rpx",color:"#BBA186"}},[t._v(t._s(t.storeInfo.userName))])],1)],1),n("v-uni-view",{staticClass:"gold_price_show"},[n("v-uni-view",{staticStyle:{"font-size":"28rpx"}},[t._v("联系方式：")]),n("v-uni-view",[n("v-uni-text",{staticStyle:{"font-size":"32rpx",color:"#BBA186"}},[t._v(t._s(t.storeInfo.phone))])],1)],1)],1),n("v-uni-view",{staticClass:"goods_detail_footer"},[n("v-uni-view",{on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.show=!1}}},[t._v("我知道了")])],1)],1)],1)],2)},a=[]},"894f":function(t,e,n){"use strict";n.d(e,"b",(function(){return o})),n.d(e,"c",(function(){return a})),n.d(e,"a",(function(){return i}));var i={uIcon:n("c0fb").default},o=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("v-uni-view",{staticClass:"u-search",style:{margin:t.margin},on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.clickHandler.apply(void 0,arguments)}}},[n("v-uni-view",{staticClass:"u-content",style:{backgroundColor:t.bgColor,borderRadius:"round"==t.shape?"100rpx":"10rpx",border:t.borderStyle,height:t.height+"rpx"}},[n("v-uni-view",{staticClass:"u-icon-wrap"},[n("u-icon",{staticClass:"u-clear-icon",attrs:{size:30,name:t.searchIcon,color:t.searchIconColor?t.searchIconColor:t.color}})],1),n("v-uni-input",{staticClass:"u-input",style:[{textAlign:t.inputAlign,color:t.color,backgroundColor:t.bgColor},t.inputStyle],attrs:{"confirm-type":"search",value:t.value,disabled:t.disabled,focus:t.focus,maxlength:t.maxlength,"placeholder-class":"u-placeholder-class",placeholder:t.placeholder,"placeholder-style":"color: "+t.placeholderColor,type:"text"},on:{blur:function(e){arguments[0]=e=t.$handleEvent(e),t.blur.apply(void 0,arguments)},confirm:function(e){arguments[0]=e=t.$handleEvent(e),t.search.apply(void 0,arguments)},input:function(e){arguments[0]=e=t.$handleEvent(e),t.inputChange.apply(void 0,arguments)},focus:function(e){arguments[0]=e=t.$handleEvent(e),t.getFocus.apply(void 0,arguments)}}}),t.keyword&&t.clearabled&&t.focused?n("v-uni-view",{staticClass:"u-close-wrap",on:{click:function(e){arguments[0]=e=t.$handleEvent(e),t.clear.apply(void 0,arguments)}}},[n("u-icon",{staticClass:"u-clear-icon",attrs:{name:"close-circle-fill",size:"34",color:"#c0c4cc"}})],1):t._e()],1),n("v-uni-view",{staticClass:"u-action",class:[t.showActionBtn||t.show?"u-action-active":""],style:[t.actionStyle],on:{click:function(e){e.stopPropagation(),e.preventDefault(),arguments[0]=e=t.$handleEvent(e),t.custom.apply(void 0,arguments)}}},[t._v(t._s(t.actionText))])],1)},a=[]},"9fdf":function(t,e,n){var i=n("4439");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var o=n("4f06").default;o("656237b1",i,!0,{sourceMap:!1,shadowMode:!1})},a35b:function(t,e,n){"use strict";var i=n("e91a"),o=n.n(i);o.a},d708:function(t,e,n){"use strict";n("7a82");var i=n("4ea4").default;Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0,n("99af");var o=i(n("5530")),a=i(n("c7eb")),r=i(n("1da1")),s=n("30ea"),c=n("9e56"),l={data:function(){return{storeList:[],page:1,limit:10,show:!1,storeContact:{},storeInfo:{},type:"",loadStatus:"loading",loadText:{loadmore:"加载更多",loading:"努力加载中",nomore:"已经到底了"},isLoadAll:!1,keyword:""}},onLoad:function(t){console.log(t),this.type=t.type||""},onShow:function(){var t=this;return(0,r.default)((0,a.default)().mark((function e(){var n;return(0,a.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return n=t,t.storeList=[],t.page=1,e.next=5,t.getStoreList();case 5:uni.getStorage({key:"store_info",success:function(t){n.storeInfo=t.data}});case 6:case"end":return e.stop()}}),e)})))()},onReachBottom:function(){this.isLoadAll||(this.page++,this.getStoreList())},methods:{onSearch:function(){var t=this;return(0,r.default)((0,a.default)().mark((function e(){return(0,a.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t.storeList=[],t.page=1,e.next=4,t.getStoreList();case 4:case"end":return e.stop()}}),e)})))()},getStoreList:function(){var t=this;return(0,r.default)((0,a.default)().mark((function e(){var n,i;return(0,a.default)().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return n=t,n.loadStatus="loading",e.next=4,c.request(s.storeListUrl,{limit:n.limit,page:n.page,name:n.keyword},"POST");case 4:i=e.sent,0!==i.code?uni.showToast({title:i.msg,icon:"none"}):(n.$nextTick((function(){n.storeList=n.storeList.concat(i.data.records)})),n.isLoadAll=n.page>=i.data.pages,n.loadStatus="nomore");case 6:case"end":return e.stop()}}),e)})))()},onChoose:function(t){this.storeInfo=(0,o.default)({},t),uni.setStorage({key:"store_info",data:t,success:function(){setTimeout((function(){uni.navigateBack()}),600)}})},onCall:function(t){uni.makePhoneCall({phoneNumber:t})},onStoreInfo:function(t){var e=this;e.storeInfo={userName:t.userName,phone:t.phone},setTimeout((function(){e.show=!0}),100)}}};e.default=l},d9a5:function(t,e,n){var i=n("24fb");e=i(!1),e.push([t.i,'@charset "UTF-8";\r\n/* uni.scss */\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 字体变量 */\r\n/* 颜色变量 */\r\n/* 行为相关颜色 */\r\n/* 文字基本颜色 */\r\n/* 背景颜色 */\r\n/* 边框颜色 */\r\n/* 尺寸变量 */\r\n/* 文字尺寸 */\r\n/* 图片尺寸 */\r\n/* Border Radius */\r\n/* 水平间距 */\r\n/* 垂直间距 */\r\n/* 透明度 */\r\n/* 文章场景相关 */.u-search[data-v-bcc6c970]{display:flex;flex-direction:row;align-items:center;flex:1}.u-content[data-v-bcc6c970]{display:flex;flex-direction:row;align-items:center;padding:0 %?18?%;flex:1}.u-clear-icon[data-v-bcc6c970]{display:flex;flex-direction:row;align-items:center}.u-input[data-v-bcc6c970]{flex:1;font-size:%?28?%;line-height:1;margin:0 %?10?%;color:#909399}.u-close-wrap[data-v-bcc6c970]{width:%?40?%;height:100%;display:flex;flex-direction:row;align-items:center;justify-content:center;border-radius:50%}.u-placeholder-class[data-v-bcc6c970]{color:#909399}.u-action[data-v-bcc6c970]{font-size:%?28?%;color:#303133;width:0;overflow:hidden;transition:all .3s;white-space:nowrap;text-align:center}.u-action-active[data-v-bcc6c970]{width:%?80?%;margin-left:%?10?%}',""]),t.exports=e},e2f9:function(t,e,n){"use strict";var i=n("9fdf"),o=n.n(i);o.a},e36e:function(t,e,n){var i=n("24fb");e=i(!1),e.push([t.i,"uni-page-body[data-v-2e8e08b9]{background-color:#f8f8f8}body.?%PAGE?%[data-v-2e8e08b9]{background-color:#f8f8f8}",""]),t.exports=e},e91a:function(t,e,n){var i=n("d9a5");i.__esModule&&(i=i.default),"string"===typeof i&&(i=[[t.i,i,""]]),i.locals&&(t.exports=i.locals);var o=n("4f06").default;o("216b3cd0",i,!0,{sourceMap:!1,shadowMode:!1})}}]);