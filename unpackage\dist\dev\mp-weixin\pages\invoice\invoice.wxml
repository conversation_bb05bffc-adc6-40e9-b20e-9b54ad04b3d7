<view class="page"><view class="form-container"><view class="order-info"><view class="order-title">订单信息</view><view class="order-item"><text class="label">订单编号：</text><text class="value">{{orderInfo.orderNo}}</text></view><view class="order-item"><text class="label">商品名称：</text><text class="value">{{orderInfo.goodsName}}</text></view><view class="order-item"><text class="label">订单金额：</text><text class="value">{{"¥"+orderInfo.totalAmt}}</text></view></view><view class="invoice-form"><view class="form-title">发票信息</view><view class="form-section"><view class="section-title">必填信息</view><view class="form-item"><view class="item-label required">购方名称</view><u-input bind:input="__e" vue-id="23bf5be4-1" placeholder="请填写购方名称" clearable="{{true}}" value="{{invoiceForm.gfnsrmc}}" data-event-opts="{{[['^input',[['__set_model',['$0','gfnsrmc','$event',[]],['invoiceForm']]]]]}}" bind:__l="__l"></u-input></view><view class="form-item"><view class="item-label required">购方税号</view><u-input bind:input="__e" vue-id="23bf5be4-2" placeholder="请填写购方税号" clearable="{{true}}" value="{{invoiceForm.gfnsrsbh}}" data-event-opts="{{[['^input',[['__set_model',['$0','gfnsrsbh','$event',[]],['invoiceForm']]]]]}}" bind:__l="__l"></u-input></view><view class="form-item"><view class="item-label required">收票电话</view><u-input bind:input="__e" vue-id="23bf5be4-3" placeholder="请填写收票电话" clearable="{{true}}" value="{{invoiceForm.phone}}" data-event-opts="{{[['^input',[['__set_model',['$0','phone','$event',[]],['invoiceForm']]]]]}}" bind:__l="__l"></u-input></view><view class="form-item"><view class="item-label required">收票邮箱</view><u-input bind:input="__e" vue-id="23bf5be4-4" placeholder="请填写收票邮箱" clearable="{{true}}" value="{{invoiceForm.email}}" data-event-opts="{{[['^input',[['__set_model',['$0','email','$event',[]],['invoiceForm']]]]]}}" bind:__l="__l"></u-input></view><view class="form-item"><view class="item-label">是否自然人</view><u-switch bind:input="__e" vue-id="23bf5be4-5" active-color="#BBA186" inactive-color="#dcdfe6" value="{{invoiceForm.isNaturalPerson}}" data-event-opts="{{[['^input',[['__set_model',['$0','isNaturalPerson','$event',[]],['invoiceForm']]]]]}}" bind:__l="__l"></u-switch></view></view><view class="form-section"><view class="section-title">选填信息</view><view class="form-item"><view class="item-label">购方地址</view><u-input bind:input="__e" vue-id="23bf5be4-6" placeholder="请填写购方地址" clearable="{{true}}" value="{{invoiceForm.address}}" data-event-opts="{{[['^input',[['__set_model',['$0','address','$event',[]],['invoiceForm']]]]]}}" bind:__l="__l"></u-input></view><view class="form-item"><view class="item-label">购方电话</view><u-input bind:input="__e" vue-id="23bf5be4-7" placeholder="请填写购方电话" clearable="{{true}}" value="{{invoiceForm.tel}}" data-event-opts="{{[['^input',[['__set_model',['$0','tel','$event',[]],['invoiceForm']]]]]}}" bind:__l="__l"></u-input></view><view class="form-item"><view class="item-label">银行名称</view><u-input bind:input="__e" vue-id="23bf5be4-8" placeholder="请填写开户行" clearable="{{true}}" value="{{invoiceForm.bankName}}" data-event-opts="{{[['^input',[['__set_model',['$0','bankName','$event',[]],['invoiceForm']]]]]}}" bind:__l="__l"></u-input></view><view class="form-item"><view class="item-label">银行账号</view><u-input bind:input="__e" vue-id="23bf5be4-9" placeholder="请填写银行账号" clearable="{{true}}" value="{{invoiceForm.bankNumber}}" data-event-opts="{{[['^input',[['__set_model',['$0','bankNumber','$event',[]],['invoiceForm']]]]]}}" bind:__l="__l"></u-input></view></view></view></view><view class="footer-buttons"><button data-event-opts="{{[['tap',[['resetForm',['$event']]]]]}}" class="btn reset-btn" bindtap="__e">重置</button><button class="btn submit-btn" disabled="{{submitting}}" data-event-opts="{{[['tap',[['submitInvoice',['$event']]]]]}}" bindtap="__e">{{''+(submitting?'提交中...':'提交申请')+''}}</button></view></view>