{"version": 3, "sources": ["uni-app:///main.js", "webpack:///E:/pos/黄金/gold_client/pages/register/register.vue?4f50", "webpack:///E:/pos/黄金/gold_client/pages/register/register.vue?7f69", "webpack:///E:/pos/黄金/gold_client/pages/register/register.vue?4979", "webpack:///E:/pos/黄金/gold_client/pages/register/register.vue?146d", "uni-app:///pages/register/register.vue", "webpack:///E:/pos/黄金/gold_client/pages/register/register.vue?c20e", "webpack:///E:/pos/黄金/gold_client/pages/register/register.vue?6b7e", "webpack:///E:/pos/黄金/gold_client/pages/register/register.vue?cdae", "webpack:///E:/pos/黄金/gold_client/pages/register/register.vue?31bc"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "checked", "srcs", "countdown", "disabled", "imgCode", "DeviceID", "tenantId", "passwd", "phone", "code", "defaultPhoneHeight", "nowPhoneHeight", "footer", "mounted", "watch", "onLoad", "onShow", "methods", "nextSep", "appid", "window", "toPrivacy", "uni", "url", "toUserUsage", "title", "icon", "util", "api", "res", "setTimeout", "length", "result", "timer", "clearInterval"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,iBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAiI;AACjI;AAC4D;AACL;AACa;AACyB;;;AAG7F;AACgL;AAChL,gBAAgB,yLAAU;AAC1B,EAAE,8EAAM;AACR,EAAE,+FAAM;AACR,EAAE,wGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,mGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACxBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,2OAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjCA;AAAA;AAAA;AAAA;AAAypB,CAAgB,8qBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACqC7qB;AACA;AAAA,eACA;EACAC;IACA;MACAC;MACAC;MACA;MACAC;MACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;IAEA;EACA;EACAC,6BAQA;EACAC,QAWA;EACA;AACA;AACA;EACAC;IACA;EAAA,CACA;EACAC;IAAA;MAAA;QAAA;UAAA;YAAA;YAAA;cAAA;UAAA;QAAA;MAAA;IAAA;EASA;EAEAC;IACAC;MACA;MACA;MACA;MACA;MACA;MACA,kFACAC;MACAC;IACA;IACA;IACAC;MACAC;QACAC;MACA;IACA;IAEA;IACAC;MACA;MACA;MACA;IAAA;EACA,6EACA,iFAEA;IACA;EAEA,kFAEA;IAAA;IAAA;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA,IACA;gBAAA;gBAAA;cAAA;cACAF;gBACAG;gBACAC;cACA;cAAA;YAAA;cAAA,IAGA;gBAAA;gBAAA;cAAA;cACAJ;gBACAG;gBACAC;cACA;cAAA;YAAA;cAAA,IAGA;gBAAA;gBAAA;cAAA;cACAJ;gBACAG;gBACAC;cACA;cAAA;YAAA;cAAA;cAAA,OAIAC,aACAC;gBACArB;gBACAC;gBACAF;gBACAG;cACA,GACA,OACA;YAAA;cARAoB;cAAA,MASAA;gBAAA;gBAAA;cAAA;cACAP;cACAQ;gBACAR;kBACAG;kBACAC;gBACA;cACA;cAAA;YAAA;cAIAJ;cACAQ;gBACAR;kBACAG;kBACAC;gBACA;gBAEAJ;kBACAC;gBACA;cACA;YAAA;YAAA;cAAA;UAAA;QAAA;MAAA;IAAA;EAEA,kGAUAQ;IACA;IACA;IACA;MACA;MACAC;IACA;IACA;EACA,kFACA;IAAA;IAAA;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA,IACA;gBAAA;gBAAA;cAAA;cACAV;gBACAG;gBACAC;cACA;cAAA;YAAA;cAAA;cAAA,OAGAC,aACAC;gBACA;gBACA;gBACA;cACA,GACA,OACA;YAAA;cAPAC;cAQA;cACA;gBACAP;kBACAG;kBACAC;gBACA;cACA;gBACAJ;kBACAG;kBACAC;gBACA;gBAEAxB;gBAEA;gBAEA+B;kBACA/B;kBAGA;kBACA;kBACA;oBACAgC;oBACA;oBACA,uBACA;kBACA;gBACA;cACA;YAAA;YAAA;cAAA;UAAA;QAAA;MAAA;IAAA;EACA;AAEA;AAAA,2B;;;;;;;;;;;;;AC3PA;AAAA;AAAA;AAAA;AAAi8B,CAAgB,27BAAG,EAAC,C;;;;;;;;;;;ACAr9B;AACA,OAAO,KAAU,EAAE,kBAKd;;;;;;;;;;;;;ACNL;AAAA;AAAA;AAAA;AAAwwC,CAAgB,quCAAG,EAAC,C;;;;;;;;;;;ACA5xC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/register/register.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/register/register.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./register.vue?vue&type=template&id=891c2434&scoped=true&\"\nvar renderjs\nimport script from \"./register.vue?vue&type=script&lang=js&\"\nexport * from \"./register.vue?vue&type=script&lang=js&\"\nimport style0 from \"./register.vue?vue&type=style&index=0&lang=css&\"\nimport style1 from \"./register.vue?vue&type=style&index=1&id=891c2434&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"891c2434\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/register/register.vue\"\nexport default component.exports", "export * from \"-!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./register.vue?vue&type=template&id=891c2434&scoped=true&\"", "var components\ntry {\n  components = {\n    uNavbar: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-navbar/u-navbar\" */ \"uview-ui/components/u-navbar/u-navbar.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./register.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./register.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"page\">\r\n\t\t\t<!-- #ifdef MP-WEIXIN -->\r\n\t\t\t<u-navbar background=\"transparent\" :border-bottom=\"false\"></u-navbar>\r\n\t\t\t<!-- #endif -->\r\n\t\t\t<view style=\"font-size: 48rpx;font-weight: 600;padding-top: 84rpx;\">验证码登录</view>\r\n\t\t\t<view style=\"font-size: 24rpx;color: #61687C;padding-top: 16rpx;\">若该手机号未注册，我们将为您自动注册</view>\r\n\t\t\t<view style=\"padding-top: 96rpx;\">\r\n\t\t\t\t<view class=\"item\">\r\n\t\t\t\t\t<view class=\"account\" style=\"position: relative\">\r\n\t\t\t\t\t\t<input type=\"number\" placeholder-class=\"inp-palcehoder\" v-model=\"phone\"\r\n\t\t\t\t\t\t\tplaceholder=\"输入11位手机号\" />\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"item\">\r\n\t\t\t\t\t<view class=\"account\" style=\"position: relative\">\r\n\t\t\t\t\t\t<input type=\"number\" placeholder-class=\"inp-palcehoder\" v-model=\"passwd\"\r\n\t\t\t\t\t\t\tplaceholder=\"请输入手机验证码\" />\r\n\t\t\t\t\t\t<view v-if=\"!disabled\" size=\"mini\" class=\"phonecode\" @click=\"getPhonCode\">获取验证码</view>\r\n\t\t\t\t\t\t<view v-if=\"disabled\" class=\"phonecode\">{{ countdown }}秒后重新获取</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"confimr\" @click=\"handleLogin\">确定并登录</view>\r\n\t\t\t<view class=\"manual\" v-if=\"footer\">\r\n\t\t\t\t<view>\r\n\t\t\t\t\t<radio class=\"radion\" @click=\"checkedTap\" color=\"#BBA186\" value=\"r1\" :checked=\"checked\" />\r\n\t\t\t\t\t<text>我已阅读并同意</text>\r\n\t\t\t\t\t<text class=\"login-agree\" @tap=\"toUserUsage\">《注册协议》</text><text>和</text>\r\n\t\t\t\t\t<text class=\"login-agree\" @tap=\"toPrivacy\">《隐私协议》</text><br /><text style=\"margin-left: 36rpx;margin-top: 10rpx;\">并使用本机号码登录</text>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\tconst api = require('../../config/api');\r\n\tconst util = require('../../utils/util');\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tchecked: false,\r\n\t\t\t\tsrcs: '',\r\n\t\t\t\t//图片验证码src\r\n\t\t\t\tcountdown: 60,\r\n\t\t\t\t// 初始倒计时时间，单位：秒\r\n\t\t\t\tdisabled: false,\r\n\t\t\t\timgCode: '',\r\n\t\t\t\tDeviceID: '',\r\n\t\t\t\ttenantId: '3',\r\n\t\t\t\tpasswd: '',\r\n\t\t\t\tphone: '' ,//17753422520,\r\n\t\t\t\tcode: '',\r\n\t\t\t\tdefaultPhoneHeight: '', //屏幕默认高度\r\n\t\t\t\tnowPhoneHeight: '', //屏幕现在的高度\r\n\t\t\t\tfooter: true\r\n\r\n\t\t\t};\r\n\t\t},\r\n\t\tmounted() {\r\n\t\t\t// #ifdef WEB\r\n\t\t\t//监听软键盘获取当前屏幕高度的事件\r\n\t\t\tthis.defaultPhoneHeight = window.innerHeight\r\n\t\t\twindow.onresize = () => {\r\n\t\t\t\tthis.nowPhoneHeight = window.innerHeight\r\n\t\t\t}\r\n\t\t\t// #endif\r\n\t\t},\r\n\t\twatch:{\r\n\t\t\t// #ifdef WEB\r\n\t\t\t//软键盘弹起事件\r\n\t\t\tnowPhoneHeight(){\r\n\t\t\t\tif(this.defaultPhoneHeight != this.nowPhoneHeight){ //手机键盘被唤起\r\n\t\t\t\t\tthis.footer = false\r\n\t\t\t\t}else{ //手机键盘被关闭\r\n\t\t\t\t\tthis.footer = true\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t// #endif\r\n\t\t},\r\n\t\t/**\r\n\t\t * 生命周期函数--监听页面加载\r\n\t\t */\r\n\t\tonLoad(options) {\r\n\t\t\t// this.refreshsrc();\r\n\t\t},\r\n\t\tasync onShow() {\r\n\t\t\t// #ifdef WEB\r\n\t\t\t// 在回调页面取出code值\r\n\t\t\tconst urlParams = new URLSearchParams(window.location.href.split('?')[1]);\r\n\t\t\tthis.code = urlParams.get('code')\r\n\t\t\tconsole.log('code: ',urlParams.get('code'))\r\n\t\t\tif(urlParams.get('code')) return\r\n\t\t\tawait this.nextSep()\r\n\t\t\t// #endif\r\n\t\t},\r\n\r\n\t\tmethods: {\r\n\t\t\tnextSep() {\r\n\t\t\t\t// 构造微信授权链接\r\n\t\t\t\tconst appid = \"wxa56aa346588ae42f\";\r\n\t\t\t\tconst redirectUri = encodeURIComponent(`https://gold.xinxiangfu.cn/goldxcx/#/pages/register/register`); // 回调页面的URL\r\n\t\t\t\tconst scope = \"snsapi_base\"; // snsapi_base 或 snsapi_userinfo，snsapi_userinfo可以拉起用户授权窗口\r\n\t\t\t\tconst state = \"STATE\";\r\n\t\t\t\tconst authUrl =\r\n\t\t\t\t\t`https://open.weixin.qq.com/connect/oauth2/authorize?appid=${appid}&redirect_uri=${redirectUri}&response_type=code&scope=${scope}&state=${state}#wechat_redirect`;\r\n\t\t\t\twindow.location.href = authUrl;\r\n\t\t\t},\r\n\t\t\t// 去隐私协议\r\n\t\t\ttoPrivacy() {\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: '/pages/Privacy/Privacy'\r\n\t\t\t\t});\r\n\t\t\t},\r\n\r\n\t\t\t// 去用户使用协议\r\n\t\t\ttoUserUsage() {\r\n\t\t\t\t// uni.navigateTo({\r\n\t\t\t\t// \turl: '/pages/UserUsage/UserUsage'\r\n\t\t\t\t// });\r\n\t\t\t},\r\n\t\t\ttoPrivacy() {},\r\n\t\t\t// 单选框\r\n\t\t\tcheckedTap() {\r\n\t\t\t\tthis.checked = !this.checked\r\n\r\n\t\t\t},\r\n\t\t\t// 登录\r\n\t\t\tasync handleLogin() {\r\n\t\t\t\tif (!this.phone.trim()) {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '手机号不能为空',\r\n\t\t\t\t\t\ticon: 'none',\r\n\t\t\t\t\t})\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tif (!this.passwd.trim()) {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '验证码不能空',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t});\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tif (!this.checked) {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '请同意隐私政策和注册协议',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t});\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\t// try {\r\n\t\t\t\t\tconst res = await util.request(\r\n\t\t\t\t\t\tapi.LoginUrl, {\r\n\t\t\t\t\t\t\tpasswd: this.passwd,\r\n\t\t\t\t\t\t\tphone: this.phone,\r\n\t\t\t\t\t\t\ttenantId: this.tenantId,\r\n\t\t\t\t\t\t\tcode: this.code\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t\t'POST'\r\n\t\t\t\t\t);\r\n\t\t\t\t\tif (res.code !== 0) {\r\n\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\ttitle: res.msg,\r\n\t\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t}, 30)\r\n\t\t\t\r\n\t\t\t\t\t\treturn;\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tuni.setStorageSync('token', res.data.token)\r\n\t\t\t\t\t\tsetTimeout(() => { \r\n\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\ttitle: \"登陆成功\",\r\n\t\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t\r\n\t\t\t\t\t\t\tuni.switchTab({\r\n\t\t\t\t\t\t\t    url: '/pages/index/index'\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t}, 30)\r\n\t\t\t\t\t}\r\n\t\t\t},\r\n\r\n\t\t\t// 获取图片验证码\r\n\t\t\t// async refreshsrc() {\r\n\t\t\t// \tconst randomString = this.generateRandomString(6);\r\n\t\t\t// \tthis.DeviceID = randomString\r\n\t\t\t// \tthis.srcs = api.ImgCodeUrl + \"?DeviceID=\" + this.DeviceID;\r\n\t\t\t// },\r\n\r\n\t\t\t// 生成字符串\r\n\t\t\tgenerateRandomString(length) {\r\n\t\t\t\tconst characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';\r\n\t\t\t\tlet result = '';\r\n\t\t\t\tfor (let i = 0; i < length; i++) {\r\n\t\t\t\t\tconst randomIndex = Math.floor(Math.random() * characters.length);\r\n\t\t\t\t\tresult += characters.charAt(randomIndex);\r\n\t\t\t\t}\r\n\t\t\t\treturn result;\r\n\t\t\t},\r\n\t\t\tasync getPhonCode() {\r\n\t\t\t\tif (!this.phone.trim()) {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '手机号不能为空',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t});\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tconst res = await util.request(\r\n\t\t\t\t\tapi.PhoneCodeUrl, {\r\n\t\t\t\t\t\t\"deviceID\": this.DeviceID,\r\n\t\t\t\t\t\t\"phone\": this.phone,\r\n\t\t\t\t\t\t\"tenantId\": 3\r\n\t\t\t\t\t},\r\n\t\t\t\t\t'POST'\r\n\t\t\t\t);\r\n\t\t\t\t// console.log(res);\r\n\t\t\t\tif (res.code !== 0) {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: res.msg,\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t});\r\n\t\t\t\t} else {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '发送成功',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t});\r\n\r\n\t\t\t\t\tlet countdown = this.countdown;\r\n\r\n\t\t\t\t\tthis.disabled = true\r\n\r\n\t\t\t\t\tlet timer = setInterval(() => {\r\n\t\t\t\t\t\tcountdown--;\r\n\r\n\r\n\t\t\t\t\t\tthis.countdown = countdown\r\n\t\t\t\t\t\t// 倒计时结束\r\n\t\t\t\t\t\tif (countdown <= 0) {\r\n\t\t\t\t\t\t\tclearInterval(timer);\r\n\t\t\t\t\t\t\t// 恢复按钮可点击状态\r\n\t\t\t\t\t\t\tthis.countdown = 60,\r\n\t\t\t\t\t\t\t\tthis.disabled = false\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}, 1000);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t};\r\n</script>\r\n<style>\r\n\t.status_bar {\r\n\t\theight: var(--status-bar-height);\r\n\t\twidth: 100%;\r\n\t}\r\n\r\n\t/* 单选框 */\r\n\t.manual {\r\n\t\tposition: fixed;\r\n\t\tbottom: 100rpx;\r\n\t\tleft: 0;\r\n\t\tright: 0;\r\n\t\tfont-size: 22rpx;\r\n\t\tcolor: #9FA3B0;\r\n\t\tpadding: 0 80rpx;\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: center;\r\n\t}\r\n\r\n\t.radion {\r\n\t\talign-self: center;\r\n\t\tmargin-top: 0rpx;\r\n\t}\r\n\r\n\tradio .wx-radio-input {\r\n\t\tborder-radius: 50%;\r\n\t\twidth: 24rpx;\r\n\t\tborder: 2rpx solid #5e5e5f;\r\n\t\theight: 24rpx;\r\n\t}\r\n\r\n\t.login-agree {\r\n\t\tcolor: #BBA186;\r\n\t}\r\n</style>\r\n<style lang=\"scss\" scoped>\r\n\t.page {\r\n\t\tpadding: 1px;\r\n\t\tpadding: 0 80rpx;\r\n\t}\r\n\t.item {\r\n\t\theight: 45px;\r\n\t\tmargin-top: 15px;\r\n\r\n\t}\r\n\t.account {\r\n\t\tdisplay: flex;\r\n\t\tbox-sizing: border-box;\r\n\t\tfont-size: 30rpx;\r\n\t\talign-items: center;\r\n\t\tborder-bottom: 1px solid #F1F2F5;\r\n\t}\r\n\r\n\t.account input {\r\n\t\t// padding-left: 20rpx;\r\n\t\t// width: 80%;\r\n\t\tflex: 1;\r\n\t\theight: 60rpx;\r\n\t}\r\n\r\n\r\n\t/* 短信验证码 */\r\n\t.phonecode {\r\n\t\theight: 35px;\r\n\t\tpadding: 0px 10px;\r\n\t\ttext-align: center;\r\n\t\tline-height: 35px;\r\n\t\tcolor: #BBA186;\r\n\t\tfont-size: 28rpx;\r\n\t}\r\n\r\n\t.confimr {\r\n\t\theight: 88rpx;\r\n\t\tfont-size: 28rpx;\r\n\t\tmargin-top: 80rpx;\r\n\t\tbackground-color: #BBA186;\r\n\t\ttext-align: center;\r\n\t\tcolor: #fff;\r\n\t\tline-height: 88rpx;\r\n\t\tborder-radius: 30px;\r\n\t}\r\n</style>", "import mod from \"-!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./register.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./register.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754188037837\n      var cssReload = require(\"D:/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  ", "import mod from \"-!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./register.vue?vue&type=style&index=1&id=891c2434&lang=scss&scoped=true&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./register.vue?vue&type=style&index=1&id=891c2434&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754188039878\n      var cssReload = require(\"D:/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}