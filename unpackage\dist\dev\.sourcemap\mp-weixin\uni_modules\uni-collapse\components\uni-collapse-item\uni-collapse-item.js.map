{"version": 3, "sources": ["webpack:///E:/Home/ma-Yi/gold/uni_modules/uni-collapse/components/uni-collapse-item/uni-collapse-item.vue?0800", "webpack:///E:/Home/ma-Yi/gold/uni_modules/uni-collapse/components/uni-collapse-item/uni-collapse-item.vue?8ca2", "webpack:///E:/Home/ma-Yi/gold/uni_modules/uni-collapse/components/uni-collapse-item/uni-collapse-item.vue?be2a", "webpack:///E:/Home/ma-Yi/gold/uni_modules/uni-collapse/components/uni-collapse-item/uni-collapse-item.vue?a4ad", "uni-app:///uni_modules/uni-collapse/components/uni-collapse-item/uni-collapse-item.vue", "webpack:///E:/Home/ma-Yi/gold/uni_modules/uni-collapse/components/uni-collapse-item/uni-collapse-item.vue?de66", "webpack:///E:/Home/ma-Yi/gold/uni_modules/uni-collapse/components/uni-collapse-item/uni-collapse-item.vue?8467"], "names": ["name", "props", "title", "type", "default", "disabled", "showAnimation", "open", "thumb", "titleBorder", "border", "showArrow", "data", "isOpen", "isheight", "height", "elId", "nameSync", "watch", "updated", "created", "destroyed", "mounted", "console", "methods", "init", "uninstall", "onClick", "getCollapseHeight", "views", "select", "fields", "size", "index", "exec", "getNvueHwight", "getCollapse", "parent", "parentName"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACqE;AACL;AACc;;;AAG9E;AACqM;AACrM,gBAAgB,8MAAU;AAC1B,EAAE,uFAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,sTAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjCA;AAAA;AAAA;AAAA;AAA2wB,CAAgB,gyBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACkC/xB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAZA,eAaA;EACAA;EACAC;IACA;IACAC;MACAC;MACAC;IACA;IACAJ;MACAG;MACAC;IACA;IACA;IACAC;MACAF;MACAC;IACA;IASA;IACAE;MACAH;MACAC;IACA;IAEA;IACAG;MACAJ;MACAC;IACA;IACA;IACAI;MACAL;MACAC;IACA;IACA;IACAK;MACAN;MACAC;IACA;IACAM;MACAP;MACAC;IACA;IACAO;MACAR;MACAC;IACA;EACA;EACAQ;IACA;IACA;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACAX;MACA;MACA;IACA;EACA;EACAY;IAAA;IACA;MACA;IACA;EACA;EACAC;IACA;IACA;IACA;EACA;EAEA;EACAC;IACA;IACA;EACA;EASAC;IACA;IACA;MACA;IACA;MACA;IACA;IACA;MACA;IACA;MACAC;IACA;IACA;MACA;IACA;IACA;EACA;EACAC;IACAC;MAEA;IAKA;IACAC;MAAA;MACA;QACA;UACA;YACA;UACA;QACA;QACA;UACA;YACA;UACA;QACA;MACA;IACA;IACAC;MACA;MACA;MACA;QACA;MACA;MACA;QACA;MACA;IACA;IACAC;MAAA;MAAA;MACA;MACAC,MACAC,8BACAC;QACAC;MACA;QACA;QACA;QACA;UACAC;UACA;UACA;QACA;QAKA;QAEA;QACA;QACA;MACA,GACAC;IACA;IACAC;MAAA;MACA;QACA;UAKA;UAEA;UACA;UACA;QACA;MACA;IACA;IACA;AACA;AACA;IACAC;MAAA;MACA;MACA;MACA;QACAC;QACA;QACAC;MACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACxPA;AAAA;AAAA;AAAA;AAAs7C,CAAgB,m5CAAG,EAAC,C;;;;;;;;;;;ACA18C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "uni_modules/uni-collapse/components/uni-collapse-item/uni-collapse-item.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./uni-collapse-item.vue?vue&type=template&id=41027c34&\"\nvar renderjs\nimport script from \"./uni-collapse-item.vue?vue&type=script&lang=js&\"\nexport * from \"./uni-collapse-item.vue?vue&type=script&lang=js&\"\nimport style0 from \"./uni-collapse-item.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"uni_modules/uni-collapse/components/uni-collapse-item/uni-collapse-item.vue\"\nexport default component.exports", "export * from \"-!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./uni-collapse-item.vue?vue&type=template&id=41027c34&\"", "var components\ntry {\n  components = {\n    uniIcons: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uni-icons/components/uni-icons/uni-icons\" */ \"@/uni_modules/uni-icons/components/uni-icons/uni-icons.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./uni-collapse-item.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./uni-collapse-item.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"uni-collapse-item\">\r\n\t\t<!-- onClick(!isOpen) -->\r\n\t\t<view @click=\"onClick(!isOpen)\" class=\"uni-collapse-item__title\"\r\n\t\t\t:class=\"{'is-open':isOpen &&titleBorder === 'auto' ,'uni-collapse-item-border':titleBorder !== 'none'}\">\r\n\t\t\t<view class=\"uni-collapse-item__title-wrap\">\r\n\t\t\t\t<slot name=\"title\">\r\n\t\t\t\t\t<view class=\"uni-collapse-item__title-box\" :class=\"{'is-disabled':disabled}\">\r\n\t\t\t\t\t\t<image v-if=\"thumb\" :src=\"thumb\" class=\"uni-collapse-item__title-img\" />\r\n\t\t\t\t\t\t<text class=\"uni-collapse-item__title-text\">{{ title }}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</slot>\r\n\t\t\t</view>\r\n\t\t\t<view v-if=\"showArrow\"\r\n\t\t\t\t:class=\"{ 'uni-collapse-item__title-arrow-active': isOpen, 'uni-collapse-item--animation': showAnimation === true }\"\r\n\t\t\t\tclass=\"uni-collapse-item__title-arrow\">\r\n\t\t\t\t<uni-icons :color=\"disabled?'#ddd':'#bbb'\" size=\"14\" type=\"bottom\" />\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view class=\"uni-collapse-item__wrap\" :class=\"{'is--transition':showAnimation}\"\r\n\t\t\t:style=\"{height: (isOpen?height:0) +'px'}\">\r\n\t\t\t<view :id=\"elId\" ref=\"collapse--hook\" class=\"uni-collapse-item__wrap-content\"\r\n\t\t\t\t:class=\"{open:isheight,'uni-collapse-item--border':border&&isOpen}\">\r\n\t\t\t\t<slot></slot>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\t// #ifdef APP-NVUE\r\n\tconst dom = weex.requireModule('dom')\r\n\t// #endif\r\n\t/**\r\n\t * CollapseItem 折叠面板子组件\r\n\t * @description 折叠面板子组件\r\n\t * @property {String} title 标题文字\r\n\t * @property {String} thumb 标题左侧缩略图\r\n\t * @property {String} name 唯一标志符\r\n\t * @property {Boolean} open = [true|false] 是否展开组件\r\n\t * @property {Boolean} titleBorder = [true|false] 是否显示标题分隔线\r\n\t * @property {String} border = ['auto'|'show'|'none'] 是否显示分隔线\r\n\t * @property {Boolean} disabled = [true|false] 是否展开面板\r\n\t * @property {Boolean} showAnimation = [true|false] 开启动画\r\n\t * @property {Boolean} showArrow = [true|false] 是否显示右侧箭头\r\n\t */\r\n\texport default {\r\n\t\tname: 'uniCollapseItem',\r\n\t\tprops: {\r\n\t\t\t// 列表标题\r\n\t\t\ttitle: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: ''\r\n\t\t\t},\r\n\t\t\tname: {\r\n\t\t\t\ttype: [Number, String],\r\n\t\t\t\tdefault: ''\r\n\t\t\t},\r\n\t\t\t// 是否禁用\r\n\t\t\tdisabled: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: false\r\n\t\t\t},\r\n\t\t\t// #ifdef APP-PLUS\r\n\t\t\t// 是否显示动画,app 端默认不开启动画，卡顿严重\r\n\t\t\tshowAnimation: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: false\r\n\t\t\t},\r\n\t\t\t// #endif\r\n\t\t\t// #ifndef APP-PLUS\r\n\t\t\t// 是否显示动画\r\n\t\t\tshowAnimation: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: true\r\n\t\t\t},\r\n\t\t\t// #endif\r\n\t\t\t// 是否展开\r\n\t\t\topen: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: false\r\n\t\t\t},\r\n\t\t\t// 缩略图\r\n\t\t\tthumb: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: ''\r\n\t\t\t},\r\n\t\t\t// 标题分隔线显示类型\r\n\t\t\ttitleBorder: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: 'auto'\r\n\t\t\t},\r\n\t\t\tborder: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: true\r\n\t\t\t},\r\n\t\t\tshowArrow: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: true\r\n\t\t\t}\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\t// TODO 随机生生元素ID，解决百度小程序获取同一个元素位置信息的bug\r\n\t\t\tconst elId = `Uni_${Math.ceil(Math.random() * 10e5).toString(36)}`\r\n\t\t\treturn {\r\n\t\t\t\tisOpen: false,\r\n\t\t\t\tisheight: null,\r\n\t\t\t\theight: 0,\r\n\t\t\t\telId,\r\n\t\t\t\tnameSync: 0\r\n\t\t\t}\r\n\t\t},\r\n\t\twatch: {\r\n\t\t\topen(val) {\r\n\t\t\t\tthis.isOpen = val\r\n\t\t\t\tthis.onClick(val, 'init')\r\n\t\t\t}\r\n\t\t},\r\n\t\tupdated(e) {\r\n\t\t\tthis.$nextTick(() => {\r\n\t\t\t\tthis.init(true)\r\n\t\t\t})\r\n\t\t},\r\n\t\tcreated() {\r\n\t\t\tthis.collapse = this.getCollapse()\r\n\t\t\tthis.oldHeight = 0\r\n\t\t\tthis.onClick(this.open, 'init')\r\n\t\t},\r\n\t\t// #ifndef VUE3\r\n\t\t// TODO vue2\r\n\t\tdestroyed() {\r\n\t\t\tif (this.__isUnmounted) return\r\n\t\t\tthis.uninstall()\r\n\t\t},\r\n\t\t// #endif\r\n\t\t// #ifdef VUE3\r\n\t\t// TODO vue3\r\n\t\tunmounted() {\r\n\t\t\tthis.__isUnmounted = true\r\n\t\t\tthis.uninstall()\r\n\t\t},\r\n\t\t// #endif\r\n\t\tmounted() {\r\n\t\t\tif (!this.collapse) return\r\n\t\t\tif (this.name !== '') {\r\n\t\t\t\tthis.nameSync = this.name\r\n\t\t\t} else {\r\n\t\t\t\tthis.nameSync = this.collapse.childrens.length + ''\r\n\t\t\t}\r\n\t\t\tif (this.collapse.names.indexOf(this.nameSync) === -1) {\r\n\t\t\t\tthis.collapse.names.push(this.nameSync)\r\n\t\t\t} else {\r\n\t\t\t\tconsole.warn(`name 值 ${this.nameSync} 重复`);\r\n\t\t\t}\r\n\t\t\tif (this.collapse.childrens.indexOf(this) === -1) {\r\n\t\t\t\tthis.collapse.childrens.push(this)\r\n\t\t\t}\r\n\t\t\tthis.init()\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tinit(type) {\r\n\t\t\t\t// #ifndef APP-NVUE\r\n\t\t\t\tthis.getCollapseHeight(type)\r\n\t\t\t\t// #endif\r\n\t\t\t\t// #ifdef APP-NVUE\r\n\t\t\t\tthis.getNvueHwight(type)\r\n\t\t\t\t// #endif\r\n\t\t\t},\r\n\t\t\tuninstall() {\r\n\t\t\t\tif (this.collapse) {\r\n\t\t\t\t\tthis.collapse.childrens.forEach((item, index) => {\r\n\t\t\t\t\t\tif (item === this) {\r\n\t\t\t\t\t\t\tthis.collapse.childrens.splice(index, 1)\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t})\r\n\t\t\t\t\tthis.collapse.names.forEach((item, index) => {\r\n\t\t\t\t\t\tif (item === this.nameSync) {\r\n\t\t\t\t\t\t\tthis.collapse.names.splice(index, 1)\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tonClick(isOpen, type) {\r\n\t\t\t\tif (this.disabled) return\r\n\t\t\t\tthis.isOpen = isOpen\r\n\t\t\t\tif (this.isOpen && this.collapse) {\r\n\t\t\t\t\tthis.collapse.setAccordion(this)\r\n\t\t\t\t}\r\n\t\t\t\tif (type !== 'init') {\r\n\t\t\t\t\tthis.collapse.onChange(isOpen, this)\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tgetCollapseHeight(type, index = 0) {\r\n\t\t\t\tconst views = uni.createSelectorQuery().in(this)\r\n\t\t\t\tviews\r\n\t\t\t\t\t.select(`#${this.elId}`)\r\n\t\t\t\t\t.fields({\r\n\t\t\t\t\t\tsize: true\r\n\t\t\t\t\t}, data => {\r\n\t\t\t\t\t\t// TODO 百度中可能获取不到节点信息 ，需要循环获取\r\n\t\t\t\t\t\tif (index >= 10) return\r\n\t\t\t\t\t\tif (!data) {\r\n\t\t\t\t\t\t\tindex++\r\n\t\t\t\t\t\t\tthis.getCollapseHeight(false, index)\r\n\t\t\t\t\t\t\treturn\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\t// #ifdef APP-NVUE\r\n\t\t\t\t\t\tthis.height = data.height + 1\r\n\t\t\t\t\t\t// #endif\r\n\t\t\t\t\t\t// #ifndef APP-NVUE\r\n\t\t\t\t\t\tthis.height = data.height\r\n\t\t\t\t\t\t// #endif\r\n\t\t\t\t\t\tthis.isheight = true\r\n\t\t\t\t\t\tif (type) return\r\n\t\t\t\t\t\tthis.onClick(this.isOpen, 'init')\r\n\t\t\t\t\t})\r\n\t\t\t\t\t.exec()\r\n\t\t\t},\r\n\t\t\tgetNvueHwight(type) {\r\n\t\t\t\tconst result = dom.getComponentRect(this.$refs['collapse--hook'], option => {\r\n\t\t\t\t\tif (option && option.result && option.size) {\r\n\t\t\t\t\t\t// #ifdef APP-NVUE\r\n\t\t\t\t\t\tthis.height = option.size.height + 1\r\n\t\t\t\t\t\t// #endif\r\n\t\t\t\t\t\t// #ifndef APP-NVUE\r\n\t\t\t\t\t\tthis.height = option.size.height\r\n\t\t\t\t\t\t// #endif\r\n\t\t\t\t\t\tthis.isheight = true\r\n\t\t\t\t\t\tif (type) return\r\n\t\t\t\t\t\tthis.onClick(this.open, 'init')\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t/**\r\n\t\t\t * 获取父元素实例\r\n\t\t\t */\r\n\t\t\tgetCollapse(name = 'uniCollapse') {\r\n\t\t\t\tlet parent = this.$parent;\r\n\t\t\t\tlet parentName = parent.$options.name;\r\n\t\t\t\twhile (parentName !== name) {\r\n\t\t\t\t\tparent = parent.$parent;\r\n\t\t\t\t\tif (!parent) return false;\r\n\t\t\t\t\tparentName = parent.$options.name;\r\n\t\t\t\t}\r\n\t\t\t\treturn parent;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n\t.uni-collapse-item {\r\n\t\t/* #ifndef APP-NVUE */\r\n\t\tbox-sizing: border-box;\r\n\r\n\t\t/* #endif */\r\n\t\t&__title {\r\n\t\t\t/* #ifndef APP-NVUE */\r\n\t\t\tdisplay: flex;\r\n\t\t\twidth: 100%;\r\n\t\t\tbox-sizing: border-box;\r\n\t\t\t/* #endif */\r\n\t\t\tflex-direction: row;\r\n\t\t\talign-items: center;\r\n\t\t\ttransition: border-bottom-color .3s;\r\n\r\n\t\t\t// transition-property: border-bottom-color;\r\n\t\t\t// transition-duration: 5s;\r\n\t\t\t&-wrap {\r\n\t\t\t\twidth: 100%;\r\n\t\t\t\tflex: 1;\r\n\r\n\t\t\t}\r\n\r\n\t\t\t&-box {\r\n\t\t\t\tpadding: 0 15px;\r\n\t\t\t\t/* #ifndef APP-NVUE */\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\twidth: 100%;\r\n\t\t\t\tbox-sizing: border-box;\r\n\t\t\t\t/* #endif */\r\n\t\t\t\tflex-direction: row;\r\n\t\t\t\tjustify-content: space-between;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\theight: 48px;\r\n\t\t\t\tline-height: 48px;\r\n\t\t\t\tbackground-color: #fff;\r\n\t\t\t\tcolor: #303133;\r\n\t\t\t\tfont-size: 13px;\r\n\t\t\t\tfont-weight: 500;\r\n\t\t\t\t/* #ifdef H5 */\r\n\t\t\t\tcursor: pointer;\r\n\t\t\t\toutline: none;\r\n\r\n\t\t\t\t/* #endif */\r\n\t\t\t\t&.is-disabled {\r\n\t\t\t\t\t.uni-collapse-item__title-text {\r\n\t\t\t\t\t\tcolor: #999;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\r\n\t\t\t}\r\n\r\n\t\t\t&.uni-collapse-item-border {\r\n\t\t\t\tborder-bottom: 1px solid #ebeef5;\r\n\t\t\t}\r\n\r\n\t\t\t&.is-open {\r\n\t\t\t\tborder-bottom-color: transparent;\r\n\t\t\t}\r\n\r\n\t\t\t&-img {\r\n\t\t\t\theight: 22px;\r\n\t\t\t\twidth: 22px;\r\n\t\t\t\tmargin-right: 10px;\r\n\t\t\t}\r\n\r\n\t\t\t&-text {\r\n\t\t\t\tflex: 1;\r\n\t\t\t\tfont-size: 14px;\r\n\t\t\t\t/* #ifndef APP-NVUE */\r\n\t\t\t\twhite-space: nowrap;\r\n\t\t\t\tcolor: inherit;\r\n\t\t\t\t/* #endif */\r\n\t\t\t\t/* #ifdef APP-NVUE */\r\n\t\t\t\tlines: 1;\r\n\t\t\t\t/* #endif */\r\n\t\t\t\toverflow: hidden;\r\n\t\t\t\ttext-overflow: ellipsis;\r\n\t\t\t}\r\n\r\n\t\t\t&-arrow {\r\n\t\t\t\t/* #ifndef APP-NVUE */\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\tbox-sizing: border-box;\r\n\t\t\t\t/* #endif */\r\n\t\t\t\talign-items: center;\r\n\t\t\t\tjustify-content: center;\r\n\t\t\t\twidth: 20px;\r\n\t\t\t\theight: 20px;\r\n\t\t\t\tmargin-right: 10px;\r\n\t\t\t\ttransform: rotate(0deg);\r\n\r\n\t\t\t\t&-active {\r\n\t\t\t\t\ttransform: rotate(-180deg);\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\r\n\t\t}\r\n\r\n\t\t&__wrap {\r\n\t\t\t/* #ifndef APP-NVUE */\r\n\t\t\twill-change: height;\r\n\t\t\tbox-sizing: border-box;\r\n\t\t\t/* #endif */\r\n\t\t\tbackground-color: #fff;\r\n\t\t\toverflow: hidden;\r\n\t\t\tposition: relative;\r\n\t\t\theight: 0;\r\n\r\n\t\t\t&.is--transition {\r\n\t\t\t\t// transition: all 0.3s;\r\n\t\t\t\ttransition-property: height, border-bottom-width;\r\n\t\t\t\ttransition-duration: 0.3s;\r\n\t\t\t\t/* #ifndef APP-NVUE */\r\n\t\t\t\twill-change: height;\r\n\t\t\t\t/* #endif */\r\n\t\t\t}\r\n\r\n\r\n\r\n\t\t\t&-content {\r\n\t\t\t\tposition: absolute;\r\n\t\t\t\tfont-size: 13px;\r\n\t\t\t\tcolor: #303133;\r\n\t\t\t\t// transition: height 0.3s;\r\n\t\t\t\tborder-bottom-color: transparent;\r\n\t\t\t\tborder-bottom-style: solid;\r\n\t\t\t\tborder-bottom-width: 0;\r\n\r\n\t\t\t\t&.uni-collapse-item--border {\r\n\t\t\t\t\tborder-bottom-width: 1px;\r\n\t\t\t\t\tborder-bottom-color: red;\r\n\t\t\t\t\tborder-bottom-color: #ebeef5;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t&.open {\r\n\t\t\t\t\tposition: relative;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t&--animation {\r\n\t\t\ttransition-property: transform;\r\n\t\t\ttransition-duration: 0.3s;\r\n\t\t\ttransition-timing-function: ease;\r\n\t\t}\r\n\r\n\t}\r\n</style>\r\n", "import mod from \"-!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./uni-collapse-item.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./uni-collapse-item.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1752590364731\n      var cssReload = require(\"D:/HBuilderX.3.3.13.20220314.full/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"../../\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}