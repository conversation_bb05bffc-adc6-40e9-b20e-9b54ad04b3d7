(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["pages-register-register"],{2218:function(e,t,n){var a=n("24fb");t=a(!1),t.push([e.i,".status_bar[data-v-7f006d85]{height:0;width:100%}\n\n/* 单选框 */.manual[data-v-7f006d85]{position:fixed;bottom:%?100?%;left:0;right:0;font-size:%?22?%;color:#9fa3b0;padding:0 %?80?%;display:flex;justify-content:center}.radion[data-v-7f006d85]{align-self:center;margin-top:%?0?%}uni-radio .wx-radio-input[data-v-7f006d85]{border-radius:50%;width:%?24?%;border:%?2?% solid #5e5e5f;height:%?24?%}.login-agree[data-v-7f006d85]{color:#bba186}",""]),e.exports=t},"2aa9":function(e,t,n){"use strict";n.d(t,"b",(function(){return a})),n.d(t,"c",(function(){return i})),n.d(t,"a",(function(){}));var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("v-uni-view",{staticClass:"page"},[n("v-uni-view",{staticStyle:{"font-size":"48rpx","font-weight":"600","padding-top":"84rpx"}},[e._v("验证码登录")]),n("v-uni-view",{staticStyle:{"font-size":"24rpx",color:"#61687C","padding-top":"16rpx"}},[e._v("若该手机号未注册，我们将为您自动注册")]),n("v-uni-view",{staticStyle:{"padding-top":"96rpx"}},[n("v-uni-view",{staticClass:"item"},[n("v-uni-view",{staticClass:"account",staticStyle:{position:"relative"}},[n("v-uni-input",{attrs:{type:"number","placeholder-class":"inp-palcehoder",placeholder:"输入11位手机号"},model:{value:e.phone,callback:function(t){e.phone=t},expression:"phone"}})],1)],1),n("v-uni-view",{staticClass:"item"},[n("v-uni-view",{staticClass:"account",staticStyle:{position:"relative"}},[n("v-uni-input",{attrs:{type:"number","placeholder-class":"inp-palcehoder",placeholder:"请输入手机验证码"},model:{value:e.passwd,callback:function(t){e.passwd=t},expression:"passwd"}}),e.disabled?e._e():n("v-uni-view",{staticClass:"phonecode",attrs:{size:"mini"},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.getPhonCode.apply(void 0,arguments)}}},[e._v("获取验证码")]),e.disabled?n("v-uni-view",{staticClass:"phonecode"},[e._v(e._s(e.countdown)+"秒后重新获取")]):e._e()],1)],1)],1),n("v-uni-view",{staticClass:"confimr",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.handleLogin.apply(void 0,arguments)}}},[e._v("确定并登录")]),e.footer?n("v-uni-view",{staticClass:"manual"},[n("v-uni-view",[n("v-uni-radio",{staticClass:"radion",attrs:{color:"#BBA186",value:"r1",checked:e.checked},on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.checkedTap.apply(void 0,arguments)}}}),n("v-uni-text",[e._v("我已阅读并同意")]),n("v-uni-text",{staticClass:"login-agree",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.toUserUsage.apply(void 0,arguments)}}},[e._v("《注册协议》")]),n("v-uni-text",[e._v("和")]),n("v-uni-text",{staticClass:"login-agree",on:{click:function(t){arguments[0]=t=e.$handleEvent(t),e.toPrivacy.apply(void 0,arguments)}}},[e._v("《隐私协议》")]),n("br"),n("v-uni-text",{staticStyle:{"margin-left":"36rpx","margin-top":"10rpx"}},[e._v("并使用本机号码登录")])],1)],1):e._e()],1)},i=[]},3005:function(e,t,n){"use strict";var a=n("d124"),i=n.n(a);i.a},"77e9":function(e,t,n){"use strict";n.r(t);var a=n("2aa9"),i=n("ce53");for(var o in i)["default"].indexOf(o)<0&&function(e){n.d(t,e,(function(){return i[e]}))}(o);n("3005"),n("c456");var r=n("f0c5"),c=Object(r["a"])(i["default"],a["b"],a["c"],!1,null,"7f006d85",null,!1,a["a"],void 0);t["default"]=c.exports},"806f":function(e,t,n){var a=n("24fb");t=a(!1),t.push([e.i,'@charset "UTF-8";\r\n/* uni.scss */\r\n/**\r\n * 这里是uni-app内置的常用样式变量\r\n *\r\n * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量\r\n * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App\r\n *\r\n */\r\n/**\r\n * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能\r\n *\r\n * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件\r\n */\r\n/* 字体变量 */\r\n/* 颜色变量 */\r\n/* 行为相关颜色 */\r\n/* 文字基本颜色 */\r\n/* 背景颜色 */\r\n/* 边框颜色 */\r\n/* 尺寸变量 */\r\n/* 文字尺寸 */\r\n/* 图片尺寸 */\r\n/* Border Radius */\r\n/* 水平间距 */\r\n/* 垂直间距 */\r\n/* 透明度 */\r\n/* 文章场景相关 */.page[data-v-7f006d85]{padding:1px;padding:0 %?80?%}.item[data-v-7f006d85]{height:45px;margin-top:15px}.account[data-v-7f006d85]{display:flex;box-sizing:border-box;font-size:%?30?%;align-items:center;border-bottom:1px solid #f1f2f5}.account uni-input[data-v-7f006d85]{flex:1;height:%?60?%}\r\n/* 短信验证码 */.phonecode[data-v-7f006d85]{height:35px;padding:0 10px;text-align:center;line-height:35px;color:#bba186;font-size:%?28?%}.confimr[data-v-7f006d85]{height:%?88?%;font-size:%?28?%;margin-top:%?80?%;background-color:#bba186;text-align:center;color:#fff;line-height:%?88?%;border-radius:30px}',""]),e.exports=t},a623:function(e,t,n){"use strict";n("7a82");var a=n("4ea4").default;Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0,n("d3b7"),n("3ca3"),n("ddb0"),n("9861"),n("99af"),n("498a");var i,o=a(n("ade3")),r=a(n("c7eb")),c=a(n("1da1")),s=n("30ea"),d=n("9e56"),u={data:function(){return{checked:!1,srcs:"",countdown:60,disabled:!1,imgCode:"",DeviceID:"",tenantId:"3",passwd:"",phone:"",code:"",defaultPhoneHeight:"",nowPhoneHeight:"",footer:!0}},mounted:function(){var e=this;this.defaultPhoneHeight=window.innerHeight,window.onresize=function(){e.nowPhoneHeight=window.innerHeight}},watch:{nowPhoneHeight:function(){this.defaultPhoneHeight!=this.nowPhoneHeight?this.footer=!1:this.footer=!0}},onLoad:function(e){},onShow:function(){var e=this;return(0,c.default)((0,r.default)().mark((function t(){var n;return(0,r.default)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(n=new URLSearchParams(window.location.href.split("?")[1]),e.code=n.get("code"),console.log("code: ",n.get("code")),!n.get("code")){t.next=5;break}return t.abrupt("return");case 5:return t.next=7,e.nextSep();case 7:case"end":return t.stop()}}),t)})))()},methods:(i={nextSep:function(){var e=encodeURIComponent("https://gold.xinxiangfu.cn/goldxcx/#/pages/register/register"),t="https://open.weixin.qq.com/connect/oauth2/authorize?appid=".concat("wxa56aa346588ae42f","&redirect_uri=").concat(e,"&response_type=code&scope=").concat("snsapi_base","&state=").concat("STATE","#wechat_redirect");window.location.href=t},toPrivacy:function(){uni.navigateTo({url:"/pages/Privacy/Privacy"})},toUserUsage:function(){}},(0,o.default)(i,"toPrivacy",(function(){})),(0,o.default)(i,"checkedTap",(function(){this.checked=!this.checked})),(0,o.default)(i,"handleLogin",(function(){var e=this;return(0,c.default)((0,r.default)().mark((function t(){var n;return(0,r.default)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(e.phone.trim()){t.next=3;break}return uni.showToast({title:"手机号不能为空",icon:"none"}),t.abrupt("return");case 3:if(e.passwd.trim()){t.next=6;break}return uni.showToast({title:"验证码不能空",icon:"none"}),t.abrupt("return");case 6:if(e.checked){t.next=9;break}return uni.showToast({title:"请同意隐私政策和注册协议",icon:"none"}),t.abrupt("return");case 9:return t.next=11,d.request(s.LoginUrl,{passwd:e.passwd,phone:e.phone,tenantId:e.tenantId,code:e.code},"POST");case 11:if(n=t.sent,0===n.code){t.next=18;break}return uni.hideLoading(),setTimeout((function(){uni.showToast({title:n.msg,icon:"none"})}),30),t.abrupt("return");case 18:uni.setStorageSync("token",n.data.token),setTimeout((function(){uni.showToast({title:"登陆成功",icon:"none"}),uni.switchTab({url:"/pages/index/index"})}),30);case 20:case"end":return t.stop()}}),t)})))()})),(0,o.default)(i,"generateRandomString",(function(e){for(var t="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789",n="",a=0;a<e;a++){var i=Math.floor(Math.random()*t.length);n+=t.charAt(i)}return n})),(0,o.default)(i,"getPhonCode",(function(){var e=this;return(0,c.default)((0,r.default)().mark((function t(){var n,a,i;return(0,r.default)().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(e.phone.trim()){t.next=3;break}return uni.showToast({title:"手机号不能为空",icon:"none"}),t.abrupt("return");case 3:return t.next=5,d.request(s.PhoneCodeUrl,{deviceID:e.DeviceID,phone:e.phone,tenantId:3},"POST");case 5:n=t.sent,0!==n.code?uni.showToast({title:n.msg,icon:"none"}):(uni.showToast({title:"发送成功",icon:"none"}),a=e.countdown,e.disabled=!0,i=setInterval((function(){a--,e.countdown=a,a<=0&&(clearInterval(i),e.countdown=60,e.disabled=!1)}),1e3));case 7:case"end":return t.stop()}}),t)})))()})),i)};t.default=u},c456:function(e,t,n){"use strict";var a=n("ff1f"),i=n.n(a);i.a},ce53:function(e,t,n){"use strict";n.r(t);var a=n("a623"),i=n.n(a);for(var o in a)["default"].indexOf(o)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(o);t["default"]=i.a},d124:function(e,t,n){var a=n("2218");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[e.i,a,""]]),a.locals&&(e.exports=a.locals);var i=n("4f06").default;i("42aa12a7",a,!0,{sourceMap:!1,shadowMode:!1})},ff1f:function(e,t,n){var a=n("806f");a.__esModule&&(a=a.default),"string"===typeof a&&(a=[[e.i,a,""]]),a.locals&&(e.exports=a.locals);var i=n("4f06").default;i("1819875c",a,!0,{sourceMap:!1,shadowMode:!1})}}]);