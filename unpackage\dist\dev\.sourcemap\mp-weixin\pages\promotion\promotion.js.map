{"version": 3, "sources": ["uni-app:///main.js", "webpack:///E:/pos/黄金/gold_client/pages/promotion/promotion.vue?bd77", "webpack:///E:/pos/黄金/gold_client/pages/promotion/promotion.vue?a018", "webpack:///E:/pos/黄金/gold_client/pages/promotion/promotion.vue?5f03", "webpack:///E:/pos/黄金/gold_client/pages/promotion/promotion.vue?8abe", "uni-app:///pages/promotion/promotion.vue", "webpack:///E:/pos/黄金/gold_client/pages/promotion/promotion.vue?3730", "webpack:///E:/pos/黄金/gold_client/pages/promotion/promotion.vue?0273", "webpack:///E:/pos/黄金/gold_client/pages/promotion/promotion.vue?b6ec", "webpack:///E:/pos/黄金/gold_client/pages/promotion/promotion.vue?da94"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "storeList", "page", "limit", "show", "storeInfo", "loadStatus", "loadText", "loadmore", "loading", "nomore", "isLoadAll", "onLoad", "console", "onShow", "onReachBottom", "methods", "getStoreList", "that", "util", "api", "name", "res", "uni", "title", "icon", "onCall", "phoneNumber", "onStoreInfo", "userName", "phone", "setTimeout"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,kBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAsH;AACtH;AAC6D;AACL;AACa;AACC;;;AAGtE;AACgL;AAChL,gBAAgB,yLAAU;AAC1B,EAAE,+EAAM;AACR,EAAE,oFAAM;AACR,EAAE,6FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,wFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACxBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,uPAEN;AACP,KAAK;AACL;AACA,aAAa,qOAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACpDA;AAAA;AAAA;AAAA;AAA0pB,CAAgB,+qBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACmD9qB;AACA;AAAA,eACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;QACAC;QACAC;QACAC;MACA;MACAC;IACA;EACA;EACAC;IACAC;IACA;EACA;EACAC;IAAA;IAAA;MAAA;QAAA;UAAA;YAAA;cACA;cACA;cAAA;cAAA,OACA;YAAA;YAAA;cAAA;UAAA;QAAA;MAAA;IAAA;EACA;EACAC;IACA;MACA;MACA;IACA;EACA;EACAC;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAC;gBACAA;gBAAA;gBAAA,OACAC,aACAC;kBACAjB;kBACAD;kBACAmB;gBACA,GACA,OACA;cAAA;gBAPAC;gBAQA;kBACAC;oBACAC;oBACAC;kBACA;gBAEA;kBACAP;kBACAA;kBACAA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAQ;MACAH;QACAI;MACA;IACA;IACAC;MACA;MACAV;QACAW;QACAC;MACA;MACAC;QACAb;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC7HA;AAAA;AAAA;AAAA;AAAk8B,CAAgB,47BAAG,EAAC,C;;;;;;;;;;;ACAt9B;AACA,OAAO,KAAU,EAAE,kBAKd;;;;;;;;;;;;;ACNL;AAAA;AAAA;AAAA;AAAivC,CAAgB,8sCAAG,EAAC,C;;;;;;;;;;;ACArwC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/promotion/promotion.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/promotion/promotion.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./promotion.vue?vue&type=template&id=6ca4abce&\"\nvar renderjs\nimport script from \"./promotion.vue?vue&type=script&lang=js&\"\nexport * from \"./promotion.vue?vue&type=script&lang=js&\"\nimport style0 from \"./promotion.vue?vue&type=style&index=0&lang=css&\"\nimport style1 from \"./promotion.vue?vue&type=style&index=1&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/promotion/promotion.vue\"\nexport default component.exports", "export * from \"-!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./promotion.vue?vue&type=template&id=6ca4abce&\"", "var components\ntry {\n  components = {\n    uLoadmore: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-loadmore/u-loadmore\" */ \"uview-ui/components/u-loadmore/u-loadmore.vue\"\n      )\n    },\n    uPopup: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-popup/u-popup\" */ \"uview-ui/components/u-popup/u-popup.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.storeList.length\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event) {\n      _vm.show = false\n    }\n  }\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./promotion.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./promotion.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"page\">\r\n\t\t<view class=\"store_content\" v-for=\"item in storeList\" :key=\"item.id\">\r\n\t\t\t<view class=\"store_img\">\r\n\t\t\t\t<image :src=\"item.img\" style=\"width: 160rpx;height: 160rpx;\" alt=\"\" />\r\n\t\t\t</view>\r\n\t\t\t<view class=\"store_name\">\r\n\t\t\t\t<view style=\"margin-top: 10rpx;font-size: 28rpx;\">{{item.name}}</view>\r\n\t\t\t\t<view style=\"display: flex;justify-content: space-between;align-items: center;color: #61687C;font-size: 24rpx;margin-top: 10rpx;\">\r\n\t\t\t\t\t<view style=\"display: flex;\">\r\n\t\t\t\t\t\t<image style=\"width: 28rpx;\" mode=\"widthFix\" src=\"/static/img/shop/shop-clock.png\" alt=\"\" /><text style=\"margin-left: 10rpx;\">{{item.openTime}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view style=\"display: flex;\">\r\n\t\t\t\t\t\t<view class=\"store_round\" @click=\"onCall(item.phone)\"><image style=\"width: 28rpx;\" mode=\"widthFix\" src=\"/static/img/shop/shop-calling.png\" alt=\"\" /></view>\r\n\t\t\t\t\t\t<view class=\"store_round\" @click=\"onStoreInfo(item)\"><image style=\"width: 28rpx;\" mode=\"widthFix\" src=\"/static/img/shop/shop-user.png\" alt=\"\" /></view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view style=\"display: flex;color: #61687C;font-size: 24rpx;margin-top: 10rpx;\"><image style=\"width: 28rpx;\" mode=\"widthFix\" src=\"/static/img/shop/shop-location.png\" alt=\"\" /><text style=\"margin-left: 10rpx;\">{{item.address}}</text></view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<!-- #ifdef MP-WEIXIN -->\r\n\t\t<u-loadmore :margin-top=\"storeList.length?'':50\" :status=\"loadStatus\" :load-text=\"loadText\"></u-loadmore>\r\n\t\t<!-- #endif -->\r\n\t\t<!-- #ifdef WEB -->\r\n\t\t<u-loadmore :status=\"loadStatus\" :load-text=\"loadText\" @loadmore=\"addRandomData\"></u-loadmore>\r\n\t\t<view class=\"page_bottom\"></view>\r\n\t\t<!-- #endif -->\r\n\t\t<u-popup v-model=\"show\" mode=\"center\" :border-radius=\"14\" length=\"90%\" :close-icon-color=\"'#FFFFFF'\" :closeable=\"true\">\r\n\t\t\t<view>\r\n\t\t\t\t<view class=\"gold_new\">\r\n\t\t\t\t\t<view class=\"gold_new_title\">门店信息</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view style=\"padding: 0 32rpx;\">\r\n\t\t\t\t\t<view class=\"gold_price_show\">\r\n\t\t\t\t\t\t<view style=\"font-size: 28rpx;\">联系人：</view>\r\n\t\t\t\t\t\t<view><text style=\"font-size: 32rpx;color: #BBA186;\">{{storeInfo.userName}}</text></view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"gold_price_show\">\r\n\t\t\t\t\t\t<view style=\"font-size: 28rpx;\">联系方式：</view>\r\n\t\t\t\t\t\t<view><text style=\"font-size: 32rpx;color: #BBA186;\">{{storeInfo.phone}}</text></view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"goods_detail_footer\">\r\n\t\t\t\t\t<view @click=\"show=false\">我知道了</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</u-popup>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\tconst api = require('../../config/api');\r\n\tconst util = require('../../utils/util');\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tstoreList: [],\r\n\t\t\t\tpage: 1,\r\n\t\t\t\tlimit: 10,\r\n\t\t\t\tshow: false,\r\n\t\t\t\tstoreInfo: {},\r\n\t\t\t\tloadStatus: 'loading',\r\n\t\t\t\tloadText: {\r\n\t\t\t\t\tloadmore: '加载更多',\r\n\t\t\t\t\tloading: '努力加载中',\r\n\t\t\t\t\tnomore: '已经到底了'\r\n\t\t\t\t},\r\n\t\t\t\tisLoadAll: false\r\n\t\t\t}\r\n\t\t},\r\n\t\tonLoad(options) {\r\n\t\t\tconsole.log(options)\r\n\t\t\t// this.getUserInfo()\r\n\t\t},\r\n\t\tasync onShow() {\r\n\t\t\tthis.storeList=[]\r\n\t\t\tthis.page = 1\r\n\t\t\tawait this.getStoreList()\r\n\t\t},\r\n\t\tonReachBottom () {\r\n\t\t\tif (!this.isLoadAll) {\r\n\t\t\t\tthis.page++\r\n\t\t\t\tthis.getStoreList()\r\n\t\t\t}\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tasync getStoreList() {\r\n\t\t\t\tlet that = this;\r\n\t\t\t\tthat.loadStatus = 'loading'\r\n\t\t\t\tconst res = await util.request(\r\n\t\t\t\t\tapi.storeListUrl, {\r\n\t\t\t\t\t\tlimit: that.limit,\r\n\t\t\t\t\t\tpage: that.page,\r\n\t\t\t\t\t\tname: ''\r\n\t\t\t\t\t},\r\n\t\t\t\t\t'POST'\r\n\t\t\t\t);\r\n\t\t\t\tif (res.code !== 0) {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: res.msg,\r\n\t\t\t\t\t\ticon: \"none\"\r\n\t\t\t\t\t})\r\n\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthat.storeList = that.storeList.concat(res.data.records)\r\n\t\t\t\t\tthat.isLoadAll = that.page >= res.data.pages //4\r\n\t\t\t\t\tthat.loadStatus = 'nomore'\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tonCall(phone) {\r\n\t\t\t\tuni.makePhoneCall({\r\n\t\t\t\t\tphoneNumber: phone //仅为示例\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tonStoreInfo(item) {\r\n\t\t\t\tlet that = this\r\n\t\t\t\tthat.storeInfo = {\r\n\t\t\t\t\tuserName: item.userName,\r\n\t\t\t\t\tphone: item.phone\r\n\t\t\t\t}\r\n\t\t\t\tsetTimeout(()=>{\r\n\t\t\t\t\tthat.show = true\r\n\t\t\t\t},100)\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n<style>\r\n\tpage {\r\n\t\tbackground-color: #F8F8F8;\r\n\t}\r\n\r\n</style>\r\n<style lang=\"scss\">\r\n\t.page {\r\n\t\tpadding: 24rpx 24rpx 0 24rpx;\r\n\t\t.store_content{\r\n\t\t\tdisplay: flex;\r\n\t\t\tmargin-bottom: 24rpx;\r\n\t\t\tpadding: 24rpx;\r\n\t\t\tborder-radius: 10rpx;\r\n\t\t\tbackground-color: #FFFFFF;\r\n\t\t\t.store_img{\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\tborder-radius: 10rpx;\r\n\t\t\t\toverflow: hidden;\r\n\t\t\t}\r\n\t\t\t.store_name{\r\n\t\t\t\tflex: 1;\r\n\t\t\t\tmargin-left: 24rpx;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\t.store_round{\r\n\t\twidth: 52rpx;\r\n\t\theight: 52rpx;\r\n\t\tborder-radius: 50%;\r\n\t\toverflow: hidden;\r\n\t\tbackground-color: #F5F5F5;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\tmargin-left: 24rpx;\r\n\t\tjustify-content: center;\r\n\t}\r\n\t.gold_new{\r\n\t\t\tpadding: 40rpx 0;\r\n\t\t\tbackground: linear-gradient(94deg, #8F8174 -2.53%, #C4B39F 131.45%);\r\n\t\t\tview{\r\n\t\t\t\tcolor: #FFFFFF;\r\n\t\t\t\ttext-align: center;\r\n\t\t\t\tfont-size: 24rpx;\r\n\t\t\t}\r\n\t\t.gold_new_title{\r\n\t\t\tfont-size: 36rpx;\r\n\t\t\t// padding-top: 40rpx;\r\n\t\t\tpadding-bottom: 20rpx;\r\n\t\t}\r\n\t}\r\n\t\t.gold_price_show{\r\n\t\t\tdisplay: flex;\r\n\t\t\tjustify-content: space-between;\r\n\t\t\tpadding: 24rpx 0;\r\n\t\t\tborder-bottom: 1rpx solid #F1F2F5;\r\n\t\t}\r\n\t\t.goods_detail_footer {\r\n\t\t\tmargin-top: 32rpx;\r\n\t\t\twidth: 100%;\r\n\t\t\theight: 50px;\r\n\t\t\tdisplay: flex;\r\n\t\t\tjustify-content: center;\r\n\t\t}\r\n\t\r\n\t\t.goods_detail_footer>view {\r\n\t\t\theight: 84rpx;\r\n\t\t\twidth: 80%;\r\n\t\t\tborder-radius: 9999px;\r\n\t\t\tbackground-color: #BBA186;\r\n\t\t\tcolor: #FFFFFF;\r\n\t\t\tfont-size: 28rpx;\r\n\t\t\ttext-align: center;\r\n\t\t\tline-height: 84rpx;\r\n\t\t}\r\n</style>", "import mod from \"-!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./promotion.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./promotion.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754273097000\n      var cssReload = require(\"D:/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  ", "import mod from \"-!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./promotion.vue?vue&type=style&index=1&lang=scss&\"; export default mod; export * from \"-!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./promotion.vue?vue&type=style&index=1&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754273099705\n      var cssReload = require(\"D:/HBuilderX/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}