<view class="page"><view style="position:relative;padding-bottom:30rpx;"><u-navbar vue-id="aedde45e-1" background="transparent" back-text="{{$root.f0}}" back-icon-size="{{30}}" back-text-style="{{({fontSize:'40rpx',marginLeft:'20rpx'})}}" border-bottom="{{false}}" bind:__l="__l"></u-navbar><block wx:if="{{orderObj.orderStatus==0}}"><view style="font-size:24rpx;color:#61687C;position:absolute;left:60rpx;bottom:30rpx;">请在<u-count-down vue-id="aedde45e-2" timestamp="{{orderObj.timestamp}}" font-size="24" bg-color="none" color="#FF0046" separator="zh" separator-size="24" separator-color="#FF0046" bind:__l="__l"></u-count-down>前完成付款</view></block><block wx:if="{{orderObj.orderStatus==4}}"><view style="font-size:24rpx;color:#61687C;position:absolute;left:60rpx;bottom:30rpx;">订单已取消，期待您再次光临</view></block><block wx:if="{{orderObj.orderStatus==1}}"><view style="font-size:24rpx;color:#61687C;position:absolute;left:60rpx;bottom:30rpx;">请到店后使用</view></block><block wx:if="{{orderObj.orderStatus==3}}"><view style="font-size:24rpx;color:#61687C;position:absolute;left:60rpx;bottom:30rpx;">订单已完成，期待您再次光临</view></block><block wx:if="{{orderObj.orderStatus==7}}"><view style="font-size:24rpx;color:#61687C;position:absolute;left:60rpx;bottom:30rpx;">预计1-5个个工作日到账，请注意查收</view></block></view><view class="address"><block wx:if="{{storeInfo.id}}"><view><view style="display:flex;justify-content:space-between;align-items:center;"><view class="address_top"><view style="display:flex;"><image style="width:40rpx;height:40rpx;" src="/static/img/shop/shop_store.png" mode="widthFix"></image></view><view style="margin-left:10rpx;ffont-size:32rpx;font-weight:600;">{{storeInfo.name}}</view></view><view style="display:flex;align-items:center;font-size:24rpx;">切换门店<view class="right-icon"></view></view></view><view style="margin-top:20rpx;color:#61687C;font-size:24rpx;">{{storeInfo.address}}</view></view></block><block wx:else><view class="address_right" style="width:100%;display:flex;align-items:center;justify-content:space-between;"><view style="display:flex;align-items:center;"><image style="width:40rpx;height:40rpx;" src="/static/img/shop/shop_store.png" mode="widthFix"></image><text style="margin-left:10rpx;">请选择门店</text></view><view class="right-icon"></view></view></block></view><view class="product_content"><view class="product_info"><view class="prodct_left"><image style="width:164rpx;height:164rpx;" src="{{orderObj.goodsImg}}"></image></view><view class="product_center"><view class="text-ellipsis_2" style="font-size:28rpx;line-height:44rpx;">{{orderObj.goodsName+''}}</view><view style="display:flex;justify-content:space-between;margin-top:20rpx;"><view style="color:#61687C;font-size:24rpx;background-color:#F2F4F7;text-align:center;padding:6rpx 20rpx;border-radius:6rpx;">{{''+orderObj.specification+"g"}}</view></view></view><view class="product_right"><view style="display:flex;justify-content:flex-end;"><text class="font_small">{{"¥"+orderObj.price}}</text></view><view style="font-size:24rpx;color:#9FA3B0;margin-top:8rpx;text-align:right;">x1</view><view style="margin-top:26rpx;"><text style="color:#61687C;font-size:24rpx;">实付</text><text style="color:#171B25;font-size:28rpx;margin-left:6rpx;">{{"¥"+orderObj.totalAmt}}</text></view></view></view><block wx:if="{{orderObj.orderStatus==1}}"><view class="product_yzm"><view style="display:flex;align-items:center;"><text>验证码</text><u-icon vue-id="aedde45e-3" name="{{eyeOff?'eye':'eye-off'}}" custom-style="{{({marginLeft:'10rpx'})}}" size="36" data-event-opts="{{[['^click',[['onEye']]]]}}" bind:click="__e" bind:__l="__l"></u-icon></view><view>{{eyeOff?orderObj.code:orderObj.code?$root.g0:''}}</view></view></block></view><view class="inviter"><view>邀请人</view><view style="display:flex;align-items:center;color:#61687C;">{{orderObj.yqrName.name||'-'}}</view></view><view class="pay_type"><view>订单信息</view><view style="margin-top:24rpx;"><text style="color:#61687C;">订单编号：</text><text>{{orderObj.orderNo}}</text></view><view style="margin-top:16rpx;"><text style="color:#61687C;">下单时间：</text><text>{{orderObj.createDate}}</text></view><block wx:if="{{$root.g1}}"><view style="margin-top:16rpx;"><text style="color:#61687C;">付款时间：</text><text>{{orderObj.payTime}}</text></view></block><view style="margin-top:16rpx;"><text style="color:#61687C;">微信支付：</text><text>{{"¥"+orderObj.totalAmt}}</text></view><block wx:if="{{$root.g2}}"><view class="invoice_btn_container"><button data-event-opts="{{[['tap',[['goToInvoice',['$event']]]]]}}" class="invoice_btn" bindtap="__e">开具发票</button></view></block></view><block wx:if="{{orderObj.orderStatus==0}}"><view class="goods_detail_footer"><button data-event-opts="{{[['tap',[['settleOrder',['$event']]]]]}}" class="btn" bindtap="__e">确认支付</button></view></block></view>