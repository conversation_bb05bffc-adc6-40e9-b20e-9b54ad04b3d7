{"version": 3, "sources": ["webpack:///E:/Home/ma-Yi/gold/node_modules/uview-ui/components/u-read-more/u-read-more.vue?4180", "webpack:///E:/Home/ma-Yi/gold/node_modules/uview-ui/components/u-read-more/u-read-more.vue?1e75", "webpack:///E:/Home/ma-Yi/gold/node_modules/uview-ui/components/u-read-more/u-read-more.vue?7204", "webpack:///E:/Home/ma-Yi/gold/node_modules/uview-ui/components/u-read-more/u-read-more.vue?bab2", "uni-app:///node_modules/uview-ui/components/u-read-more/u-read-more.vue", "webpack:///E:/Home/ma-Yi/gold/node_modules/uview-ui/components/u-read-more/u-read-more.vue?f4e3", "webpack:///E:/Home/ma-Yi/gold/node_modules/uview-ui/components/u-read-more/u-read-more.vue?2c28"], "names": ["name", "props", "showHeight", "type", "default", "toggle", "closeText", "openText", "color", "fontSize", "shadowStyle", "backgroundImage", "paddingTop", "marginTop", "textIndent", "index", "watch", "paramsChange", "computed", "innerShadowStyle", "data", "is<PERSON>ong<PERSON><PERSON>nt", "showMore", "elId", "mounted", "methods", "init", "toggleReadMore"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAoI;AACpI;AAC+D;AACL;AACsC;;;AAGhG;AACqM;AACrM,gBAAgB,8MAAU;AAC1B,EAAE,iFAAM;AACR,EAAE,kGAAM;AACR,EAAE,2GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,sGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,+NAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC1CA;AAAA;AAAA;AAAA;AAAqwB,CAAgB,0xBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACyBzxB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAZA,gBAaA;EACAA;EACAC;IACA;IACAC;MACAC;MACAC;IACA;IACA;IACAC;MACAF;MACAC;IACA;IACA;IACAE;MACAH;MACAC;IACA;IACA;IACAG;MACAJ;MACAC;IACA;IACA;IACAI;MACAL;MACAC;IACA;IACA;IACAK;MACAN;MACAC;IACA;IACA;IACAM;MACAP;MACAC;QACA;UACAO;UACAC;UACAC;QACA;MACA;IACA;IACA;IACAC;MACAX;MACAC;IACA;IACA;IACAW;MACAZ;MACAC;IACA;EACA;EACAY;IACAC;MACA;IACA;EACA;EACAC;IACAD;MACA;IACA;IACA;IACAE;MACA,kCACA;IACA;EACA;EACAC;IACA;MACAC;MAAA;MACAC;MAAA;MACAC;IACA;EACA;EACAC;IAAA;IACA;MACA;IACA;EACA;EACAC;IACAC;MAAA;MACA;QACA;QACA;UACA;UACA;QACA;MACA;IACA;IACA;IACAC;MACA;MACA;MACA;MACA;MACA;IACA;EACA;AACA;AAAA,4B;;;;;;;;;;;;;AC3IA;AAAA;AAAA;AAAA;AAAw8C,CAAgB,q6CAAG,EAAC,C;;;;;;;;;;;ACA59C;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "node-modules/uview-ui/components/u-read-more/u-read-more.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./u-read-more.vue?vue&type=template&id=35272c51&scoped=true&\"\nvar renderjs\nimport script from \"./u-read-more.vue?vue&type=script&lang=js&\"\nexport * from \"./u-read-more.vue?vue&type=script&lang=js&\"\nimport style0 from \"./u-read-more.vue?vue&type=style&index=0&id=35272c51&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"35272c51\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"node_modules/uview-ui/components/u-read-more/u-read-more.vue\"\nexport default component.exports", "export * from \"-!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-read-more.vue?vue&type=template&id=35272c51&scoped=true&\"", "var components\ntry {\n  components = {\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-icon/u-icon\" */ \"uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var s0 = _vm.isLongContent ? _vm.__get_style([_vm.innerShadowStyle]) : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        s0: s0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-read-more.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-read-more.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"\">\r\n\t\t<view class=\"u-content\" :class=\"[elId]\" :style=\"{ \r\n\t\t\theight: isLongContent && !showMore ? showHeight + 'rpx' : 'auto',\r\n\t\t\ttextIndent: textIndent\r\n\t\t}\">\r\n\t\t\t<slot></slot>\r\n\t\t</view>\r\n\t\t<view @tap=\"toggleReadMore\" v-if=\"isLongContent\" class=\"u-content__showmore-wrap\"\r\n\t\t    :class=\"{ 'u-content__show-more': showMore }\"\r\n\t\t    :style=\"[innerShadowStyle]\">\r\n\t\t\t<text class=\"u-content__showmore-wrap__readmore-btn\" :style=\"{\r\n\t\t\t\tfontSize: fontSize + 'rpx',\r\n\t\t\t\tcolor: color\r\n\t\t\t}\">\r\n\t\t\t\t{{ showMore ? openText : closeText }}\r\n\t\t\t</text>\r\n\t\t\t<view class=\"u-content__showmore-wrap__readmore-btn__icon u-flex\">\r\n\t\t\t\t<u-icon :color=\"color\" :size=\"fontSize\" :name=\"showMore ? 'arrow-up' : 'arrow-down'\"></u-icon>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\t/**\r\n\t * readMore 阅读更多\r\n\t * @description 该组件一般用于内容较长，预先收起一部分，点击展开全部内容的场景。\r\n\t * @tutorial https://www.uviewui.com/components/readMore.html\r\n\t * @property {String Number} show-height 内容超出此高度才会显示展开全文按钮，单位rpx（默认400）\r\n\t * @property {Boolean} toggle 展开后是否显示收起按钮（默认false）\r\n\t * @property {String} close-text 关闭时的提示文字（默认“展开阅读全文”）\r\n\t * @property {String Number} font-size 提示文字的大小，单位rpx（默认28）\r\n\t * @property {String} text-indent 段落首行缩进的字符个数（默认2em）\r\n\t * @property {String} open-text 展开时的提示文字（默认“收起”）\r\n\t * @property {String} color 提示文字的颜色（默认#2979ff）\r\n\t * @example <u-read-more><rich-text :nodes=\"content\"></rich-text></u-read-more>\r\n\t */\r\n\texport default {\r\n\t\tname: \"u-read-more\",\r\n\t\tprops: {\r\n\t\t\t// 默认的显示占位高度，单位为rpx\r\n\t\t\tshowHeight: {\r\n\t\t\t\ttype: [Number, String],\r\n\t\t\t\tdefault: 400\r\n\t\t\t},\r\n\t\t\t// 展开后是否显示\"收起\"按钮\r\n\t\t\ttoggle: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: false\r\n\t\t\t},\r\n\t\t\t// 关闭时的提示文字\r\n\t\t\tcloseText: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: '展开阅读全文'\r\n\t\t\t},\r\n\t\t\t// 展开时的提示文字\r\n\t\t\topenText: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: '收起'\r\n\t\t\t},\r\n\t\t\t// 提示的文字颜色\r\n\t\t\tcolor: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: '#2979ff'\r\n\t\t\t},\r\n\t\t\t// 提示文字的大小\r\n\t\t\tfontSize: {\r\n\t\t\t\ttype: [String, Number],\r\n\t\t\t\tdefault: 28\r\n\t\t\t},\r\n\t\t\t// 是否显示阴影\r\n\t\t\tshadowStyle: {\r\n\t\t\t\ttype: Object,\r\n\t\t\t\tdefault () {\r\n\t\t\t\t\treturn {\r\n\t\t\t\t\t\tbackgroundImage: \"linear-gradient(-180deg, rgba(255, 255, 255, 0) 0%, #fff 80%)\",\r\n\t\t\t\t\t\tpaddingTop: \"300rpx\",\r\n\t\t\t\t\t\tmarginTop: \"-300rpx\"\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t// 段落首行缩进的字符个数\r\n\t\t\ttextIndent: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: '2em'\r\n\t\t\t},\r\n\t\t\t// open和close事件时，将此参数返回在回调参数中\r\n\t\t\tindex: {\r\n\t\t\t\ttype: [Number, String],\r\n\t\t\t\tdefault: ''\r\n\t\t\t}\r\n\t\t},\r\n\t\twatch: {\r\n\t\t\tparamsChange(val) {\r\n\t\t\t\tthis.init();\r\n\t\t\t}\r\n\t\t},\r\n\t\tcomputed: {\r\n\t\t\tparamsChange() {\r\n\t\t\t\treturn `${this.toggle}-${this.showHeight}`;\r\n\t\t\t},\r\n\t\t\t// 展开后无需阴影，收起时才需要阴影样式\r\n\t\t\tinnerShadowStyle() {\r\n\t\t\t\tif (this.showMore) return {};\r\n\t\t\t\telse return this.shadowStyle\r\n\t\t\t}\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tisLongContent: false, // 是否需要隐藏一部分内容\r\n\t\t\t\tshowMore: false, // 当前隐藏与显示的状态，true-显示，false-收起\r\n\t\t\t\telId: this.$u.guid(), // 生成唯一class\r\n\t\t\t};\r\n\t\t},\r\n\t\tmounted() {\r\n\t\t\tthis.$nextTick(() => {\r\n\t\t\t\tthis.init();\r\n\t\t\t})\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tinit() {\r\n\t\t\t\tthis.$uGetRect('.' + this.elId).then(res => {\r\n\t\t\t\t\t// 判断高度，如果真实内容高度大于占位高度，则显示收起与展开的控制按钮\r\n\t\t\t\t\tif (res.height > uni.upx2px(this.showHeight)) {\r\n\t\t\t\t\t\tthis.isLongContent = true;\r\n\t\t\t\t\t\tthis.showMore = false;\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t// 展开或者收起\r\n\t\t\ttoggleReadMore() {\r\n\t\t\t\tthis.showMore = !this.showMore;\r\n\t\t\t\t// 如果toggle为false，隐藏\"收起\"部分的内容\r\n\t\t\t\tif (this.toggle == false) this.isLongContent = false;\r\n\t\t\t\t// 发出打开或者收齐的事件\r\n\t\t\t\tthis.$emit(this.showMore ? 'open' : 'close', this.index);\r\n\t\t\t}\r\n\t\t}\r\n\t};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n\t@import \"../../libs/css/style.components.scss\";\r\n\r\n\t.u-content {\r\n\t\tfont-size: 30rpx;\r\n\t\tcolor: $u-content-color;\r\n\t\tline-height: 1.8;\r\n\t\ttext-align: left;\r\n\t\toverflow: hidden;\r\n\r\n\t\t&__show-more {\r\n\t\t\tpadding-top: 0;\r\n\t\t\tbackground: none;\r\n\t\t\tmargin-top: 20rpx;\r\n\t\t}\r\n\r\n\t\t&__showmore-wrap {\r\n\t\t\tposition: relative;\r\n\t\t\twidth: 100%;\r\n\t\t\tpadding-bottom: 26rpx;\r\n\t\t\t@include vue-flex;\r\n\t\t\talign-items: center;\r\n\t\t\tjustify-content: center;\r\n\r\n\t\t\t&__readmore-btn {\r\n\t\t\t\t@include vue-flex;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\tjustify-content: center;\r\n\t\t\t\tline-height: 1;\r\n\r\n\t\t\t\t&__icon {\r\n\t\t\t\t\tmargin-left: 14rpx;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</style>\r\n", "import mod from \"-!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-read-more.vue?vue&type=style&index=0&id=35272c51&lang=scss&scoped=true&\"; export default mod; export * from \"-!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-read-more.vue?vue&type=style&index=0&id=35272c51&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1752389152648\n      var cssReload = require(\"D:/HBuilderX.3.3.13.20220314.full/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"../../\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}