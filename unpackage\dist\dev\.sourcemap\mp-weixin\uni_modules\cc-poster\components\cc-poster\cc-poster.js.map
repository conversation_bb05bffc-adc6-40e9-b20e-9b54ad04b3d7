{"version": 3, "sources": ["webpack:///E:/Home/ma-Yi/gold/uni_modules/cc-poster/components/cc-poster/cc-poster.vue?64f7", "webpack:///E:/Home/ma-Yi/gold/uni_modules/cc-poster/components/cc-poster/cc-poster.vue?d008", "webpack:///E:/Home/ma-Yi/gold/uni_modules/cc-poster/components/cc-poster/cc-poster.vue?e1f0", "webpack:///E:/Home/ma-Yi/gold/uni_modules/cc-poster/components/cc-poster/cc-poster.vue?846e", "uni-app:///uni_modules/cc-poster/components/cc-poster/cc-poster.vue"], "names": ["name", "props", "CanvasID", "Type", "default", "imgSrc", "QrSrc", "Title", "TitleColor", "LineType", "showLongPressSaveTip", "OriginalTxt", "OriginalColor", "<PERSON><PERSON><PERSON>", "CanvasBg", "<PERSON><PERSON><PERSON>", "ViewDetails", "data", "loading", "tempFile<PERSON>ath", "canvasW", "canvasH", "canvasImgSrc", "ctx", "methods", "toSave", "console", "uni", "src", "success", "filePath", "title", "icon", "duration", "chong<PERSON>e", "setTimeout", "OnCanvas", "_this", "C_W", "C_P", "C_Q", "_strlineW", "_imgInfo", "_QrCode", "r", "q", "imgW", "_strLastIndex", "_strHeight", "_num", "i", "getImageInfo", "resolve", "fail", "errs", "mask", "getNewImage", "canvasId", "quality", "complete", "mounted"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAsH;AACtH;AAC6D;AACL;;;AAGxD;AACqM;AACrM,gBAAgB,8MAAU;AAC1B,EAAE,+EAAM;AACR,EAAE,oFAAM;AACR,EAAE,6FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,wFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACtBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAmwB,CAAgB,wxBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACSvxB;AAAA,eACA;EACAA;EACAC;IACAC;MACA;MACAC;MACAC;IACA;IACAC;MACA;MACAF;MACAC;IACA;IACAE;MACA;MACAH;MACAC;IACA;IACAG;MACA;MACAJ;MACAC;IACA;IACAI;MACA;MACAL;MACAC;IACA;IACAK;MACA;MACAN;MACAC;IACA;IACAM;MACA;MACAP;MACAC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACAO;MACA;MACAR;MACAC;IACA;IACAQ;MACA;MACAT;MACAC;IACA;IACAS;MACA;MACAV;MACAC;IACA;IACAU;MACA;MACAX;MACAC;IACA;IACAW;MACA;MACAZ;MACAC;IACA;IACAY;MACA;MACAb;MACAC;IACA;EACA;EACAa;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACAC;MACAC;MAEAC;QACAC;QACAC;UACAH;UACAC;YACAG;YACAD;cACAH;cACAC;gBACAI;gBACAC;gBACAC;cACA;YACA;UACA;QACA;MACA;IAEA;IACAC;MAAA;MACA;MACA;MACA;MACAC;QACA;MACA;IACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACA;;gBAEA;gBACAC;gBACAC,+BACAC,sBACAC;gBACAC;gBAAA;gBAAA,OACAJ;kBACAhC;gBACA;cAAA;gBAFAqC;gBAAA;gBAAA,OAKAL;kBACAhC;gBACA;cAAA;gBAFAsC;gBAEA;gBAEA;gBAEAC;gBACAC;gBACAC;gBACA;kBACAF;kBACAA;gBACA;gBACA;kBACAC;kBACAA;gBACA;gBACAR;gBACA;gBACAA;gBACAA;gBACAA;;gBAEA;gBACAA;gBACA;;gBAEA;gBACAA;gBACAA;gBACAU;gBACA;gBACAC;gBACAC;gBACAC;cAAA;gBAAA;kBAAA;kBAAA;gBAAA;gBACAT;gBAAA,MACAA;kBAAA;kBAAA;gBAAA;gBAAA,MACAQ;kBAAA;kBAAA;gBAAA;gBACA;gBACAZ;gBACAI;gBACAM;gBACAE;gBAAA;cAAA;gBAGAZ;gBACAI;gBACAO;gBACAD;gBACAE;cAAA;gBAAA;gBAAA;cAAA;gBAEA;kBACAZ;kBACAI;gBACA;cAAA;gBApBAS;gBAAA;gBAAA;cAAA;gBAsBA;gBACA;gBACAT;gBACAO;gBACA;kBACAA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;;gBAEA;kBACA;kBACAX;kBACAA;kBACAA;gBACA;;gBAEAA;gBACAA;gBACAA;gBACAA;gBACA;;gBAEA;gBACAW;gBACAX;gBACA;;gBAEA;gBACAA;gBACAA;gBACAA;gBACAA;gBACAA;gBACAA;gBACA;gBACA;gBACAF;kBACAE;oBACAV;oBACAU;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAc,0CAEA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBADA9C;gBAAA,kCAEA;kBACAsB;oBACAC;oBACAC;sBACAuB;oBACA;oBACAC;sBACAC;sBACA;sBACA3B;wBACAI;wBACAwB;wBACAtB;wBACAD;sBACA;sBACAL;oBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACA6B;MACA7B;QACA8B;QACAC;QACAC;UACAtB;UACAA;UACAA;UACA;UACAV;YACAI;YACAwB;YACAtB;YACAD;UACA;UACAL;QACA;MACA,GACA,KACA;IACA;EACA;EACAiC;IACAvB;IACA;EACA;AACA;AAAA,2B", "file": "uni_modules/cc-poster/components/cc-poster/cc-poster.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./cc-poster.vue?vue&type=template&id=fc6b3574&\"\nvar renderjs\nimport script from \"./cc-poster.vue?vue&type=script&lang=js&\"\nexport * from \"./cc-poster.vue?vue&type=script&lang=js&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"uni_modules/cc-poster/components/cc-poster/cc-poster.vue\"\nexport default component.exports", "export * from \"-!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./cc-poster.vue?vue&type=template&id=fc6b3574&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./cc-poster.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./cc-poster.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view style=\"background: #FFFFFF;\">\r\n\t\t<!-- <view v-if=\"loading\"></view> -->\r\n\t\t<canvas v-if=\"!tempFilePath\" :canvas-id=\"CanvasID\" :style=\"{ width: canvasW + 'px', height: canvasH + 'px' }\"></canvas>\r\n\t\t<image v-else lazy-load :src=\"tempFilePath\" mode=\"widthFix\" class=\"is-response\" @longpress=\"toSave(tempFilePath)\"></image>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\tvar _this;\r\n\texport default {\r\n\t\tname: 'cc-poster',\r\n\t\tprops: {\r\n\t\t\tCanvasID: {\r\n\t\t\t\t//CanvasID 等同于 canvas-id\r\n\t\t\t\tType: String,\r\n\t\t\t\tdefault: 'PosterCanvas'\r\n\t\t\t},\r\n\t\t\timgSrc: {\r\n\t\t\t\t//展示图\r\n\t\t\t\tType: String,\r\n\t\t\t\tdefault: ''\r\n\t\t\t},\r\n\t\t\tQrSrc: {\r\n\t\t\t\t//二维码\r\n\t\t\t\tType: String,\r\n\t\t\t\tdefault: ''\r\n\t\t\t},\r\n\t\t\tTitle: {\r\n\t\t\t\t//文本内容\r\n\t\t\t\tType: String,\r\n\t\t\t\tdefault: '省钱兄'\r\n\t\t\t},\r\n\t\t\tTitleColor: {\r\n\t\t\t\t//标题颜色\r\n\t\t\t\tType: String,\r\n\t\t\t\tdefault: '#333333'\r\n\t\t\t},\r\n\t\t\tLineType: {\r\n\t\t\t\t//标题显示行数 大于两行是否省略\t（注超出2行显示会导致画布布局絮乱）\r\n\t\t\t\tType: [String, Boolean],\r\n\t\t\t\tdefault: true\r\n\t\t\t},\r\n\t\t\tshowLongPressSaveTip: {\r\n\t\t\t\t//长按图片保存海报/海报生成失败\r\n\t\t\t\tType: Boolean,\r\n\t\t\t\tdefault: true\r\n\t\t\t},\r\n\t\t\t// PriceTxt: {\r\n\t\t\t// \t//价格值\r\n\t\t\t// \tType: String,\r\n\t\t\t// \tdefault: ''\r\n\t\t\t// },\r\n\t\t\t// PriceColor: {\r\n\t\t\t// \t//价格颜色\r\n\t\t\t// \tType: String,\r\n\t\t\t// \tdefault: '#e31d1a'\r\n\t\t\t// },\r\n\t\t\tOriginalTxt: {\r\n\t\t\t\t//原价值\r\n\t\t\t\tType: String,\r\n\t\t\t\tdefault: ''\r\n\t\t\t},\r\n\t\t\tOriginalColor: {\r\n\t\t\t\t//默认颜色（如原价与扫描二维码颜色）\r\n\t\t\t\tType: String,\r\n\t\t\t\tdefault: '#b8b8b8'\r\n\t\t\t},\r\n\t\t\tWidth: {\r\n\t\t\t\t//画布宽度  (高度根据图片比例计算 单位upx)\r\n\t\t\t\tType: String,\r\n\t\t\t\tdefault: 750\r\n\t\t\t},\r\n\t\t\tCanvasBg: {\r\n\t\t\t\t//canvas画布背景色\r\n\t\t\t\tType: String,\r\n\t\t\t\tdefault: '#ffffff'\r\n\t\t\t},\r\n\t\t\tReferrer: {\r\n\t\t\t\t//推荐人信息\r\n\t\t\t\tType: String,\r\n\t\t\t\tdefault: '精选好物'\r\n\t\t\t},\r\n\t\t\tViewDetails: {\r\n\t\t\t\t//描述提示文字\r\n\t\t\t\tType: String,\r\n\t\t\t\tdefault: '长按或扫描识别二维码领券'\r\n\t\t\t}\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tloading: false,\r\n\t\t\t\ttempFilePath: '',\r\n\t\t\t\tcanvasW: 0,\r\n\t\t\t\tcanvasH: 0,\r\n\t\t\t\tcanvasImgSrc: '',\r\n\t\t\t\tctx: null\r\n\t\t\t};\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\ttoSave(url) {\r\n\t\t\t\tconsole.log(\"长按开始\");\r\n\t\t\t\t//#ifndef H5\r\n\t\t\t\tuni.getImageInfo({\r\n\t\t\t\t\tsrc: url,\r\n\t\t\t\t\tsuccess: function(image) {\r\n\t\t\t\t\t\tconsole.log('图片信息：', JSON.stringify(image));\r\n\t\t\t\t\t\tuni.saveImageToPhotosAlbum({\r\n\t\t\t\t\t\t\tfilePath: image.path,\r\n\t\t\t\t\t\t\tsuccess: function() {\r\n\t\t\t\t\t\t\t\tconsole.log('save success');\r\n\t\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\t\ttitle: '海报已保存相册',\r\n\t\t\t\t\t\t\t\t\ticon: 'success',\r\n\t\t\t\t\t\t\t\t\tduration: 2000\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t\t//#endif\r\n\t\t\t},\r\n\t\t\tchongxie(){\r\n\t\t\t\tthis.tempFilePath = ''\r\n\t\t\t\tthis.ctx =  null\r\n\t\t\t\t// this.OnCanvas2();\r\n\t\t\t\tsetTimeout(()=>{\r\n\t\t\t\t\tthis.OnCanvas();\r\n\t\t\t\t},500)\r\n\t\t\t},\r\n\t\t\tasync OnCanvas() {\r\n\t\t\t\tthis.loading = true;\r\n\t\t\t\t\r\n\t\t\t\t// this.$queue.showLoading('海报生成中...');\r\n\t\t\t\t_this.ctx = uni.createCanvasContext(_this.CanvasID, this);\r\n\t\t\t\tconst C_W = uni.upx2px(_this.Width), //canvas宽度\r\n\t\t\t\t\tC_P = uni.upx2px(30), //canvas Paddng 间距\r\n\t\t\t\t\tC_Q = uni.upx2px(150); //二维码或太阳码宽高\r\n\t\t\t\tlet _strlineW = 0; //文本宽度\r\n\t\t\t\tlet _imgInfo = await _this.getImageInfo({\r\n\t\t\t\t\timgSrc: _this.imgSrc\r\n\t\t\t\t}); //广告图\r\n\t\t\t\t// console.log('图片信息0：', JSON.stringify(\"\"));\r\n\t\t\t\t\r\n\t\t\t\tlet _QrCode = await _this.getImageInfo({\r\n\t\t\t\t\timgSrc: _this.QrSrc\r\n\t\t\t\t}); //二维码或太阳码\r\n\t\t\t\t\r\n\t\t\t\t// console.log('图片信息1：', JSON.stringify(\"\"));\r\n\t\t\t\t\r\n\t\t\t\tlet r = [_imgInfo.width, _imgInfo.height];\r\n\t\t\t\tlet q = [_QrCode.width, _QrCode.height];\r\n\t\t\t\tlet imgW = C_W - C_P * 2;\r\n\t\t\t\tif (r[0] != imgW) {\r\n\t\t\t\t\tr[1] = Math.floor((imgW / r[0]) * r[1]);\r\n\t\t\t\t\tr[0] = imgW;\r\n\t\t\t\t}\r\n\t\t\t\tif (q[0] != C_Q) {\r\n\t\t\t\t\tq[1] = Math.floor((C_Q / q[0]) * q[1]);\r\n\t\t\t\t\tq[0] = C_Q;\r\n\t\t\t\t}\r\n\t\t\t\t_this.canvasW = C_W;\r\n\t\t\t\t// _this.canvasH = r[1] + q[1] + 128;\r\n\t\t\t\t_this.canvasH = r[1] + q[1] + 50;\r\n\t\t\t\t_this.ctx.setFillStyle(_this.CanvasBg); //canvas背景颜色\r\n\t\t\t\t_this.ctx.fillRect(0, 0, C_W, _this.canvasH); //canvas画布大小\r\n\r\n\t\t\t\t//添加图片展示\r\n\t\t\t\t_this.ctx.drawImage(_imgInfo.path, C_P, C_P, r[0], r[1]);\r\n\t\t\t\t//添加图片展示 end\r\n\r\n\t\t\t\t//设置文本\r\n\t\t\t\t_this.ctx.setFontSize(uni.upx2px(32)); //设置标题字体大小\r\n\t\t\t\t_this.ctx.setFillStyle(_this.TitleColor); //设置标题文本颜色\r\n\t\t\t\tlet _strLastIndex = 0; //每次开始截取的字符串的索引\r\n\t\t\t\t// let _strHeight = r[1] + C_P * 2 + 10; //绘制字体距离canvas顶部的初始高度\r\n\t\t\t\t\t\tlet _strHeight = r[1] - C_P * 2 + 10; //绘制字体距离canvas顶部的初始高度\r\n\t\t\t\tlet _num = 1;\r\n\t\t\t\tfor (let i = 0; i < _this.Title.length; i++) {\r\n\t\t\t\t\t_strlineW += _this.ctx.measureText(_this.Title[i]).width;\r\n\t\t\t\t\tif (_strlineW > r[0]) {\r\n\t\t\t\t\t\tif (_num == 2 && _this.LineType) {\r\n\t\t\t\t\t\t\t//文字换行数量大于二进行省略号处理\r\n\t\t\t\t\t\t\t_this.ctx.fillText(_this.Title.substring(_strLastIndex, i - 8) + '...', C_P, _strHeight);\r\n\t\t\t\t\t\t\t_strlineW = 0;\r\n\t\t\t\t\t\t\t_strLastIndex = i;\r\n\t\t\t\t\t\t\t_num++;\r\n\t\t\t\t\t\t\tbreak;\r\n\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t_this.ctx.fillText(_this.Title.substring(_strLastIndex, i), C_P, _strHeight);\r\n\t\t\t\t\t\t\t_strlineW = 0;\r\n\t\t\t\t\t\t\t_strHeight += 20;\r\n\t\t\t\t\t\t\t_strLastIndex = i;\r\n\t\t\t\t\t\t\t_num++;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t} else if (i == _this.Title.length - 1) {\r\n\t\t\t\t\t\t_this.ctx.fillText(_this.Title.substring(_strLastIndex, i + 1), C_P, _strHeight);\r\n\t\t\t\t\t\t_strlineW = 0;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\t//设置文本 end\r\n\t\t\t\t//设置价格\r\n\t\t\t\t_strlineW = C_P;\r\n\t\t\t\t_strHeight += uni.upx2px(60);\r\n\t\t\t\tif (_num == 1) {\r\n\t\t\t\t\t_strHeight += 20; //单行标题时占位符\r\n\t\t\t\t}\r\n\t\t\t\t// if (_this.PriceTxt != '') {\r\n\t\t\t\t// \t//判断是否有销售价格\r\n\t\t\t\t// \t_this.ctx.setFillStyle(_this.PriceColor);\r\n\t\t\t\t// \t_this.ctx.setFontSize(uni.upx2px(38));\r\n\t\t\t\t// \t_this.ctx.fillText('券后价 ￥' + _this.PriceTxt, _strlineW, _strHeight); //商品价格\r\n\t\t\t\t// \t_strlineW += _this.ctx.measureText('券后价 ￥' + _this.PriceTxt).width + uni.upx2px(10);\r\n\t\t\t\t// }\r\n\t\t\t\r\n\t\t\t\tif (_this.PriceTxt != '' && _this.OriginalTxt != '') {\r\n\t\t\t\t\t//判断是否有销售价格且原价\r\n\t\t\t\t\t_this.ctx.setFillStyle(_this.OriginalColor);\r\n\t\t\t\t\t_this.ctx.setFontSize(uni.upx2px(24));\r\n\t\t\t\t\t_this.ctx.fillText(_this.OriginalTxt, _strlineW, _strHeight); //商品原价\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t_this.ctx.strokeStyle = _this.OriginalColor;\r\n\t\t\t\t_this.ctx.moveTo(_strlineW, _strHeight - uni.upx2px(10)); //起点\r\n\t\t\t\t_this.ctx.lineTo(_strlineW + _this.ctx.measureText(_this.OriginalTxt).width, _strHeight - uni.upx2px(10)); //终点\r\n\t\t\t\t_this.ctx.stroke();\r\n\t\t\t\t//设置价格 end\r\n\r\n\t\t\t\t//添加二维码\r\n\t\t\t\t_strHeight += uni.upx2px(20);\r\n\t\t\t\t_this.ctx.drawImage(_QrCode.path, r[0] - q[0] + C_P, _strHeight, q[0], q[1]);\r\n\t\t\t\t//添加二维码 end\r\n\r\n\t\t\t\t//添加推荐人与描述\r\n\t\t\t\t_this.ctx.setFillStyle(_this.TitleColor);\r\n\t\t\t\t_this.ctx.setFontSize(uni.upx2px(30));\r\n\t\t\t\t_this.ctx.fillText(_this.Referrer, C_P, _strHeight + q[1] / 2);\r\n\t\t\t\t_this.ctx.setFillStyle(_this.OriginalColor);\r\n\t\t\t\t_this.ctx.setFontSize(uni.upx2px(24));\r\n\t\t\t\t_this.ctx.fillText(_this.ViewDetails, C_P, _strHeight + q[1] / 2 + 20);\r\n\t\t\t\t//添加推荐人与描述 end\r\n\t\t\t\t//延迟后渲染至canvas上\r\n\t\t\t\tsetTimeout(function() {\r\n\t\t\t\t\t_this.ctx.draw(true, ret => {\r\n\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\t_this.getNewImage();\r\n\t\t\t\t\t});\r\n\t\t\t\t}, 600);\r\n\t\t\t},\r\n\t\t\tasync getImageInfo({\r\n\t\t\t\timgSrc\r\n\t\t\t}) {\r\n\t\t\t\treturn new Promise((resolve, errs) => {\r\n\t\t\t\t\tuni.getImageInfo({\r\n\t\t\t\t\t\tsrc: imgSrc,\r\n\t\t\t\t\t\tsuccess: function(image) {\r\n\t\t\t\t\t\t\tresolve(image);\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t\tfail(err) {\r\n\t\t\t\t\t\t\terrs(err);\r\n\t\t\t\t\t\t\tif (!_this.showLongPressSaveTip) return\r\n\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\ttitle: '海报生成失败',\r\n\t\t\t\t\t\t\t\tmask: false,\r\n\t\t\t\t\t\t\t\tduration: 2000,\r\n\t\t\t\t\t\t\t\ticon: \"none\"\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\tuni.hideLoading()\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t});\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tgetNewImage() {\r\n\t\t\t\tuni.canvasToTempFilePath({\r\n\t\t\t\t\t\tcanvasId: _this.CanvasID,\r\n\t\t\t\t\t\tquality: 1,\r\n\t\t\t\t\t\tcomplete: res => {\r\n\t\t\t\t\t\t\t_this.tempFilePath = res.tempFilePath;\r\n\t\t\t\t\t\t\t_this.$emit('success', res);\r\n\t\t\t\t\t\t\t_this.loading = false;\r\n\t\t\t\t\t\t\tif (!_this.showLongPressSaveTip) return\r\n\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\ttitle: '长按图片保存海报',\r\n\t\t\t\t\t\t\t\tmask: false,\r\n\t\t\t\t\t\t\t\tduration: 2000,\r\n\t\t\t\t\t\t\t\ticon: \"none\"\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t},\r\n\t\t\t\t\tthis\r\n\t\t\t\t);\r\n\t\t\t}\r\n\t\t},\r\n\t\tmounted() {\r\n\t\t\t_this = this;\r\n\t\t\tthis.OnCanvas();\r\n\t\t}\r\n\t};\r\n</script>\r\n\r\n<style></style>\r\n"], "sourceRoot": ""}