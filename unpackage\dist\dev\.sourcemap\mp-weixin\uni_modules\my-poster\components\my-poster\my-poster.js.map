{"version": 3, "sources": ["webpack:///E:/Home/ma-Yi/gold/uni_modules/my-poster/components/my-poster/my-poster.vue?805f", "webpack:///E:/Home/ma-Yi/gold/uni_modules/my-poster/components/my-poster/my-poster.vue?e5a8", "webpack:///E:/Home/ma-Yi/gold/uni_modules/my-poster/components/my-poster/my-poster.vue?5467", "webpack:///E:/Home/ma-Yi/gold/uni_modules/my-poster/components/my-poster/my-poster.vue?5405", "uni-app:///uni_modules/my-poster/components/my-poster/my-poster.vue"], "names": ["name", "props", "CanvasID", "Type", "default", "imgSrc", "QrSrc", "LogoSrc", "Title", "TitleColor", "LineType", "showLongPressSaveTip", "OriginalTxt", "OriginalColor", "<PERSON><PERSON><PERSON>", "CanvasBg", "<PERSON><PERSON><PERSON>", "ViewDetails", "data", "loading", "tempFile<PERSON>ath", "canvasW", "canvasH", "canvasImgSrc", "ctx", "methods", "toSave", "console", "uni", "src", "success", "filePath", "title", "icon", "duration", "chong<PERSON>e", "setTimeout", "OnCanvas", "_this", "C_W", "C_P", "systemInfo", "screenWidth", "screenHeight", "screenRatio", "_imgInfo", "_QrCode", "_logoInfo", "r", "q", "imgW", "imgH", "logoMaxWidth", "logoMaxHeight", "logoWidth", "logoHeight", "scale", "logoX", "logoY", "boxMargin", "boxWidth", "qrSize", "C_Q", "finalQr<PERSON>idth", "finalQrHeight", "qrScaleFactor", "boxHeight", "boxY", "boxRadius", "viewDetailsWidth", "viewDetailsX", "qrX", "qrY", "scanText", "scanTextWidth", "scanTextX", "getImageInfo", "resolve", "fail", "errs", "mask", "getNewImage", "canvasId", "quality", "fileType", "destWidth", "destHeight", "complete", "mounted"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAsH;AACtH;AAC6D;AACL;;;AAGxD;AACqM;AACrM,gBAAgB,8MAAU;AAC1B,EAAE,+EAAM;AACR,EAAE,oFAAM;AACR,EAAE,6FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,wFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACtBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAmwB,CAAgB,wxBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACSvxB;AAAA,eACA;EACAA;EACAC;IACAC;MACA;MACAC;MACAC;IACA;IACAC;MACA;MACAF;MACAC;IACA;IACAE;MACA;MACAH;MACAC;IACA;IACAG;MACA;MACAJ;MACAC;IACA;IACAI;MACA;MACAL;MACAC;IACA;IACAK;MACA;MACAN;MACAC;IACA;IACAM;MACA;MACAP;MACAC;IACA;IACAO;MACA;MACAR;MACAC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACAQ;MACA;MACAT;MACAC;IACA;IACAS;MACA;MACAV;MACAC;IACA;IACAU;MACA;MACAX;MACAC;IACA;IACAW;MACA;MACAZ;MACAC;IACA;IACAY;MACA;MACAb;MACAC;IACA;IACAa;MACA;MACAd;MACAC;IACA;EACA;EACAc;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACAC;MACAC;MAEAC;QACAC;QACAC;UACAH;UACAC;YACAG;YACAD;cACAH;cACAC;gBACAI;gBACAC;gBACAC;cACA;YACA;UACA;QACA;MACA;IAEA;IACAC;MAAA;MACA;MACA;MACA;MACAC;QACA;MACA;IACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACA;gBAEAC;gBACAC;gBACAC;gBAEA;gBACAC;gBACAC;gBACAC;gBACAC,0CAEA;gBAAA;gBAAA,OACAN;kBACAjC;gBACA;cAAA;gBAFAwC;gBAAA;gBAAA,OAIAP;kBACAjC;gBACA;cAAA;gBAFAyC;gBAEA;gBAEA;gBACAC;gBAAA,KACAT;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA;gBAAA,OAEAA;kBACAjC;gBACA;cAAA;gBAFA0C;gBAEA;gBACApB;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAA;cAAA;gBAIA;gBACAqB;gBACAC,qCAEA;gBACAC,YACA;gBACAC;gBAEA;kBACAH;kBACAA;gBACA;;gBAEA;gBACAV;gBACAA;;gBAEA;gBACAA;;gBAEA;gBACAA;gBACAA;;gBAEA;gBACAA;;gBAEA;gBACA;kBACA;kBACAc;kBACAC;kBAEAC;kBACAC,+BAEA;kBACA;oBACAC;oBACAF;oBACAC;kBACA;kBAEA;oBACAC;oBACAD;oBACAD;kBACA;;kBAEA;kBACAG;kBACAC;kBAEApB;gBACA;;gBAEA;gBACAqB;gBACAC,gCAEA;gBACAC;gBACAC;gBAEA;gBACA;gBACAC;gBACAC;gBACAC,mBAEA;gBACA;kBACAA;kBACAF;kBACAC;gBACA;gBACA;gBAAA,KACA;kBACAD;kBACAC;gBACA;gBACA;gBAAA,KACA;kBACAD;kBACAC;kBACArC;gBACA;;gBAEA;gBACAuC;gBACAC;gBACAC;gBAEA;gBACA9B;gBACAA;;gBAEA;gBACAA;gBACAA;gBACAA;gBACAA;gBACAA;gBACAA;gBACAA;gBACAA;gBAEAA;;gBAEA;gBACAA;gBACAA;gBACAA;gBACAA;gBACA+B;gBACAC;gBACAhC;;gBAEA;gBACAiC;gBACAC;gBAEA;gBACAlC;gBACAA;gBACAA;;gBAEA;gBACAA;gBACAA;gBACAA;gBACAmC;gBACAC;gBACAC;gBACArC;;gBAEA;gBACAF;kBACAE;oBACAV;oBACAU;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAsC,0CAEA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBADAvE;gBAAA,kCAEA;kBACAuB;oBACAC;oBACAC;sBACA+C;oBACA;oBACAC;sBACAC;sBACA;sBACAnD;wBACAI;wBACAgD;wBACA9C;wBACAD;sBACA;sBACAL;oBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAqD;MACArD;QACAsD;QACAC;QAAA;QACAC;QAAA;QACAC;QAAA;QACAC;QAAA;QACAC;UACAjD;UACAA;UACAA;UACA;UACAV;YACAI;YACAgD;YACA9C;YACAD;UACA;UACAL;QACA;MACA,GACA,KACA;IACA;EACA;EACA4D;IACAlD;IACA;EACA;AACA;AAAA,2B", "file": "uni_modules/my-poster/components/my-poster/my-poster.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./my-poster.vue?vue&type=template&id=427684fa&\"\nvar renderjs\nimport script from \"./my-poster.vue?vue&type=script&lang=js&\"\nexport * from \"./my-poster.vue?vue&type=script&lang=js&\"\n\n\n/* normalize component */\nimport normalizer from \"!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"uni_modules/my-poster/components/my-poster/my-poster.vue\"\nexport default component.exports", "export * from \"-!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./my-poster.vue?vue&type=template&id=427684fa&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./my-poster.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!D:\\\\HBuilderX.3.3.13.20220314.full\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./my-poster.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view style=\"background: #FFFFFF; width: 100%; padding: 0; margin: 0;\">\r\n\t\t<!-- <view v-if=\"loading\"></view> -->\r\n\t\t<canvas v-if=\"!tempFilePath\" :canvas-id=\"CanvasID\" :style=\"{ width: canvasW + 'px', height: canvasH + 'px', display: 'block' }\"></canvas>\r\n\t\t<image v-else lazy-load :src=\"tempFilePath\" mode=\"widthFix\" style=\"width: 100%; display: block;\" class=\"is-response\" @longpress=\"toSave(tempFilePath)\"></image>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\tvar _this;\r\n\texport default {\r\n\t\tname: 'cc-poster',\r\n\t\tprops: {\r\n\t\t\tCanvasID: {\r\n\t\t\t\t//CanvasID 等同于 canvas-id\r\n\t\t\t\tType: String,\r\n\t\t\t\tdefault: 'PosterCanvas'\r\n\t\t\t},\r\n\t\t\timgSrc: {\r\n\t\t\t\t//展示图\r\n\t\t\t\tType: String,\r\n\t\t\t\tdefault: ''\r\n\t\t\t},\r\n\t\t\tQrSrc: {\r\n\t\t\t\t//二维码\r\n\t\t\t\tType: String,\r\n\t\t\t\tdefault: ''\r\n\t\t\t},\r\n\t\t\tLogoSrc: {\r\n\t\t\t\t//左上角logo\r\n\t\t\t\tType: String,\r\n\t\t\t\tdefault: ''\r\n\t\t\t},\r\n\t\t\tTitle: {\r\n\t\t\t\t//文本内容\r\n\t\t\t\tType: String,\r\n\t\t\t\tdefault: '省钱兄'\r\n\t\t\t},\r\n\t\t\tTitleColor: {\r\n\t\t\t\t//标题颜色\r\n\t\t\t\tType: String,\r\n\t\t\t\tdefault: '#333333'\r\n\t\t\t},\r\n\t\t\tLineType: {\r\n\t\t\t\t//标题显示行数 大于两行是否省略\t（注超出2行显示会导致画布布局絮乱）\r\n\t\t\t\tType: [String, Boolean],\r\n\t\t\t\tdefault: true\r\n\t\t\t},\r\n\t\t\tshowLongPressSaveTip: {\r\n\t\t\t\t//长按图片保存海报/海报生成失败\r\n\t\t\t\tType: Boolean,\r\n\t\t\t\tdefault: false\r\n\t\t\t},\r\n\t\t\t// PriceTxt: {\r\n\t\t\t// \t//价格值\r\n\t\t\t// \tType: String,\r\n\t\t\t// \tdefault: ''\r\n\t\t\t// },\r\n\t\t\t// PriceColor: {\r\n\t\t\t// \t//价格颜色\r\n\t\t\t// \tType: String,\r\n\t\t\t// \tdefault: '#e31d1a'\r\n\t\t\t// },\r\n\t\t\tOriginalTxt: {\r\n\t\t\t\t//原价值\r\n\t\t\t\tType: String,\r\n\t\t\t\tdefault: ''\r\n\t\t\t},\r\n\t\t\tOriginalColor: {\r\n\t\t\t\t//默认颜色（如原价与扫描二维码颜色）\r\n\t\t\t\tType: String,\r\n\t\t\t\tdefault: '#b8b8b8'\r\n\t\t\t},\r\n\t\t\tWidth: {\r\n\t\t\t\t//画布宽度  (高度根据图片比例计算 单位upx)\r\n\t\t\t\tType: String,\r\n\t\t\t\tdefault: 750\r\n\t\t\t},\r\n\t\t\tCanvasBg: {\r\n\t\t\t\t//canvas画布背景色\r\n\t\t\t\tType: String,\r\n\t\t\t\tdefault: '#ffffff'\r\n\t\t\t},\r\n\t\t\tReferrer: {\r\n\t\t\t\t//推荐人信息\r\n\t\t\t\tType: String,\r\n\t\t\t\tdefault: '精选好物'\r\n\t\t\t},\r\n\t\t\tViewDetails: {\r\n\t\t\t\t//描述提示文字\r\n\t\t\t\tType: String,\r\n\t\t\t\tdefault: '长按或扫描识别二维码领券'\r\n\t\t\t}\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tloading: false,\r\n\t\t\t\ttempFilePath: '',\r\n\t\t\t\tcanvasW: 0,\r\n\t\t\t\tcanvasH: 0,\r\n\t\t\t\tcanvasImgSrc: '',\r\n\t\t\t\tctx: null\r\n\t\t\t};\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\ttoSave(url) {\r\n\t\t\t\tconsole.log(\"长按开始\");\r\n\t\t\t\t//#ifndef H5\r\n\t\t\t\tuni.getImageInfo({\r\n\t\t\t\t\tsrc: url,\r\n\t\t\t\t\tsuccess: function(image) {\r\n\t\t\t\t\t\tconsole.log('图片信息：', JSON.stringify(image));\r\n\t\t\t\t\t\tuni.saveImageToPhotosAlbum({\r\n\t\t\t\t\t\t\tfilePath: image.path,\r\n\t\t\t\t\t\t\tsuccess: function() {\r\n\t\t\t\t\t\t\t\tconsole.log('save success');\r\n\t\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\t\ttitle: '海报已保存相册',\r\n\t\t\t\t\t\t\t\t\ticon: 'success',\r\n\t\t\t\t\t\t\t\t\tduration: 2000\r\n\t\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t});\r\n\t\t\t\t//#endif\r\n\t\t\t},\r\n\t\t\tchongxie(){\r\n\t\t\t\tthis.tempFilePath = ''\r\n\t\t\t\tthis.ctx =  null\r\n\t\t\t\t// this.OnCanvas2();\r\n\t\t\t\tsetTimeout(()=>{\r\n\t\t\t\t\tthis.OnCanvas();\r\n\t\t\t\t},500)\r\n\t\t\t},\r\n\t\t\tasync OnCanvas() {\r\n\t\t\t\tthis.loading = true;\r\n\t\t\t\t\r\n\t\t\t\t_this.ctx = uni.createCanvasContext(_this.CanvasID, this);\r\n\t\t\t\tconst C_W = uni.upx2px(_this.Width); // canvas宽度\r\n\t\t\t\tconst C_P = uni.upx2px(30); // canvas Paddng 间距\r\n\t\t\t\t\r\n\t\t\t\t// 获取设备信息，计算合适的高度比例\r\n\t\t\t\tconst systemInfo = uni.getSystemInfoSync();\r\n\t\t\t\tconst screenWidth = systemInfo.windowWidth;\r\n\t\t\t\tconst screenHeight = systemInfo.windowHeight;\r\n\t\t\t\tconst screenRatio = screenHeight / screenWidth;\r\n\t\t\t\t\r\n\t\t\t\t// 获取图片信息\r\n\t\t\t\tlet _imgInfo = await _this.getImageInfo({\r\n\t\t\t\t\timgSrc: _this.imgSrc\r\n\t\t\t\t}); // 背景图\r\n\t\t\t\t\r\n\t\t\t\tlet _QrCode = await _this.getImageInfo({\r\n\t\t\t\t\timgSrc: _this.QrSrc\r\n\t\t\t\t}); // 二维码\r\n\t\t\t\t\r\n\t\t\t\t// 获取Logo图片信息（如果提供了Logo）\r\n\t\t\t\tlet _logoInfo = null;\r\n\t\t\t\tif (_this.LogoSrc) {\r\n\t\t\t\t\ttry {\r\n\t\t\t\t\t\t_logoInfo = await _this.getImageInfo({\r\n\t\t\t\t\t\t\timgSrc: _this.LogoSrc\r\n\t\t\t\t\t\t}); // Logo图片\r\n\t\t\t\t\t\tconsole.log('Logo图片加载成功:', _logoInfo.width, 'x', _logoInfo.height);\r\n\t\t\t\t\t} catch (error) {\r\n\t\t\t\t\t\tconsole.error('Logo图片加载失败:', error);\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t// 计算图片尺寸\r\n\t\t\t\tlet r = [_imgInfo.width, _imgInfo.height];\r\n\t\t\t\tlet q = [_QrCode.width, _QrCode.height];\r\n\t\t\t\t\r\n\t\t\t\t// 计算背景图尺寸，覆盖整个canvas\r\n\t\t\t\tlet imgW = C_W;\r\n\t\t\t\t// 使用屏幕比例计算高度，确保与设备屏幕比例接近\r\n\t\t\t\tlet imgH = C_W * 1.5; \r\n\t\t\t\t\r\n\t\t\t\tif (r[0] != imgW) {\r\n\t\t\t\t\tr[1] = Math.floor((imgW / r[0]) * r[1]);\r\n\t\t\t\t\tr[0] = imgW;\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t// 设置canvas尺寸和显示倍率，提高清晰度\r\n\t\t\t\t_this.canvasW = C_W;\r\n\t\t\t\t_this.canvasH = imgH;\r\n\t\t\t\t\r\n\t\t\t\t// 设置绘制品质\r\n\t\t\t\t_this.ctx.setQuality && _this.ctx.setQuality('high');\r\n\t\t\t\t\r\n\t\t\t\t// 设置背景色\r\n\t\t\t\t_this.ctx.setFillStyle('#1678FF'); // 蓝色背景\r\n\t\t\t\t_this.ctx.fillRect(0, 0, C_W, _this.canvasH);\r\n\t\t\t\t\r\n\t\t\t\t// 绘制背景图\r\n\t\t\t\t_this.ctx.drawImage(_imgInfo.path, 0, 0, C_W, imgH);\r\n\t\t\t\t\r\n\t\t\t\t// 绘制Logo（如果有）\r\n\t\t\t\tif (_logoInfo) {\r\n\t\t\t\t\t// 计算Logo尺寸，控制在合理范围内\r\n\t\t\t\t\tconst logoMaxWidth = C_W * 0.45; // Logo最大宽度为Canvas宽度的25%\r\n\t\t\t\t\tconst logoMaxHeight = imgH * 0.22; // Logo最大高度为Canvas高度的12%\r\n\t\t\t\t\t\r\n\t\t\t\t\tlet logoWidth = _logoInfo.width;\r\n\t\t\t\t\tlet logoHeight = _logoInfo.height;\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 如果Logo太大，按比例缩小\r\n\t\t\t\t\tif (logoWidth > logoMaxWidth) {\r\n\t\t\t\t\t\tconst scale = logoMaxWidth / logoWidth;\r\n\t\t\t\t\t\tlogoWidth = logoMaxWidth;\r\n\t\t\t\t\t\tlogoHeight = logoHeight * scale;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\tif (logoHeight > logoMaxHeight) {\r\n\t\t\t\t\t\tconst scale = logoMaxHeight / logoHeight;\r\n\t\t\t\t\t\tlogoHeight = logoMaxHeight;\r\n\t\t\t\t\t\tlogoWidth = logoWidth * scale;\r\n\t\t\t\t\t}\r\n\t\t\t\t\t\r\n\t\t\t\t\t// 在左上角绘制Logo，留出一定的边距\r\n\t\t\t\t\tconst logoX = C_P*2.5;  // 左边距\r\n\t\t\t\t\tconst logoY = C_P*5;  // 顶部边距\r\n\t\t\t\t\t\r\n\t\t\t\t\t_this.ctx.drawImage(_logoInfo.path, logoX, logoY, logoWidth, logoHeight);\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t// 计算白色背景框的位置和大小\r\n\t\t\t\tconst boxMargin = C_P * 3; // 增大边距，使卡片宽度更窄\r\n\t\t\t\tconst boxWidth = C_W - boxMargin * 2;\r\n\t\t\t\t\r\n\t\t\t\t// 计算二维码尺寸，将比例从60%改为70%，使二维码更大\r\n\t\t\t\tconst qrSize = Math.floor(boxWidth * 0.85);\r\n\t\t\t\tconst C_Q = qrSize; // 更新二维码尺寸\r\n\t\t\t\t\r\n\t\t\t\t// 计算二维码的合适尺寸，避免过度缩放导致模糊\r\n\t\t\t\t// 如果二维码原始尺寸足够大，就不要放大它\r\n\t\t\t\tlet finalQrWidth = q[0];\r\n\t\t\t\tlet finalQrHeight = q[1];\r\n\t\t\t\tlet qrScaleFactor = 1;\r\n\t\t\t\t\r\n\t\t\t\t// 如果原始二维码足够大，只需要适当缩小到目标尺寸\r\n\t\t\t\tif (q[0] > C_Q) {\r\n\t\t\t\t\tqrScaleFactor = C_Q / q[0];\r\n\t\t\t\t\tfinalQrWidth = C_Q;\r\n\t\t\t\t\tfinalQrHeight = Math.floor(q[1] * qrScaleFactor);\r\n\t\t\t\t} \r\n\t\t\t\t// 如果原始二维码较小但在可接受范围内(至少是目标尺寸的75%)，则使用原始尺寸\r\n\t\t\t\telse if (q[0] >= C_Q * 0.75) {\r\n\t\t\t\t\tfinalQrWidth = q[0];\r\n\t\t\t\t\tfinalQrHeight = q[1];\r\n\t\t\t\t} \r\n\t\t\t\t// 只有当原始二维码过小时，才考虑放大\r\n\t\t\t\telse {\r\n\t\t\t\t\tfinalQrWidth = C_Q;\r\n\t\t\t\t\tfinalQrHeight = Math.floor(q[1] * (C_Q / q[0]));\r\n\t\t\t\t\tconsole.log('二维码尺寸过小，可能导致模糊，建议提高原始二维码分辨率');\r\n\t\t\t\t}\r\n\t\t\t\t\r\n\t\t\t\t// 调整白色背景框的高度，增加上下留白空间\r\n\t\t\t\tconst boxHeight = finalQrHeight + C_P * 6.5; // 增加卡片高度，为文字留出更多空间\r\n\t\t\t\tconst boxY = imgH * 0.35; // 将白色背景向上移动到图片33%的位置\r\n\t\t\t\tconst boxRadius = 16; // 圆角\r\n\t\t\t\t\r\n\t\t\t\t// 绘制白色背景框（带圆角）\r\n\t\t\t\t_this.ctx.setFillStyle('#ffffff');\r\n\t\t\t\t_this.ctx.setShadow(0, 4, 12, 'rgba(0, 0, 0, 0.18)'); // 增强阴影效果\r\n\t\t\t\t\r\n\t\t\t\t// 绘制圆角矩形\r\n\t\t\t\t_this.ctx.beginPath();\r\n\t\t\t\t_this.ctx.moveTo(boxMargin + boxRadius, boxY);\r\n\t\t\t\t_this.ctx.arcTo(boxMargin + boxWidth, boxY, boxMargin + boxWidth, boxY + boxRadius, boxRadius);\r\n\t\t\t\t_this.ctx.arcTo(boxMargin + boxWidth, boxY + boxHeight, boxMargin + boxWidth - boxRadius, boxY + boxHeight, boxRadius);\r\n\t\t\t\t_this.ctx.arcTo(boxMargin, boxY + boxHeight, boxMargin, boxY + boxHeight - boxRadius, boxRadius);\r\n\t\t\t\t_this.ctx.arcTo(boxMargin, boxY, boxMargin + boxRadius, boxY, boxRadius);\r\n\t\t\t\t_this.ctx.closePath();\r\n\t\t\t\t_this.ctx.fill();\r\n\t\t\t\t\r\n\t\t\t\t_this.ctx.setShadow(0, 0, 0, '#ffffff'); // 重置阴影\r\n\t\t\t\t\r\n\t\t\t\t// 添加邀请码信息（在白色框内向下移动），减小文字大小\r\n\t\t\t\t_this.ctx.setFillStyle('#000000'); // 黑色文字\r\n\t\t\t\t_this.ctx.setFontSize(uni.upx2px(28)); // 减小字号\r\n\t\t\t\t_this.ctx.setTextBaseline('middle');\r\n\t\t\t\t_this.ctx.font = 'bold ' + uni.upx2px(28) + 'px sans-serif'; // 设置为粗体但更小\r\n\t\t\t\tconst viewDetailsWidth = _this.ctx.measureText(_this.ViewDetails).width;\r\n\t\t\t\tconst viewDetailsX = (C_W - viewDetailsWidth) / 2;\r\n\t\t\t\t_this.ctx.fillText(_this.ViewDetails, viewDetailsX, boxY + C_P * 2.5); // 调整展业码文字位置\r\n\t\t\t\t\r\n\t\t\t\t// 在白色背景框内居中绘制二维码\r\n\t\t\t\tconst qrX = (C_W - finalQrWidth) / 2;\r\n\t\t\t\tconst qrY = boxY + C_P * 3; // 增加上边距，为上方文字留出更多空间\r\n\t\t\t\t\r\n\t\t\t\t// 使用更高质量的绘制方式\r\n\t\t\t\t_this.ctx.imageSmoothingEnabled = true;\r\n\t\t\t\t_this.ctx.imageSmoothingQuality = 'high';\r\n\t\t\t\t_this.ctx.drawImage(_QrCode.path, qrX, qrY, finalQrWidth, finalQrHeight);\r\n\t\t\t\t\r\n\t\t\t\t// 添加\"扫码加入\"文本（在二维码下方），增大字体\r\n\t\t\t\t_this.ctx.setFillStyle('#000000');\r\n\t\t\t\t_this.ctx.setFontSize(uni.upx2px(35)); // 字号保持不变\r\n\t\t\t\t_this.ctx.font = 'bold ' + uni.upx2px(32) + 'px sans-serif'; // 设置粗体\r\n\t\t\t\tconst scanText = '扫码加入卡拉灵动';\r\n\t\t\t\tconst scanTextWidth = _this.ctx.measureText(scanText).width;\r\n\t\t\t\tconst scanTextX = (C_W - scanTextWidth) / 2;\r\n\t\t\t\t_this.ctx.fillText(scanText, scanTextX, qrY + finalQrHeight + C_P * 0.8); // 增加二维码与文字间距\r\n\t\t\t\t\r\n\t\t\t\t// 延迟后渲染至canvas上\r\n\t\t\t\tsetTimeout(function() {\r\n\t\t\t\t\t_this.ctx.draw(true, ret => {\r\n\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\t_this.getNewImage();\r\n\t\t\t\t\t});\r\n\t\t\t\t}, 600);\r\n\t\t\t},\r\n\t\t\tasync getImageInfo({\r\n\t\t\t\timgSrc\r\n\t\t\t}) {\r\n\t\t\t\treturn new Promise((resolve, errs) => {\r\n\t\t\t\t\tuni.getImageInfo({\r\n\t\t\t\t\t\tsrc: imgSrc,\r\n\t\t\t\t\t\tsuccess: function(image) {\r\n\t\t\t\t\t\t\tresolve(image);\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t\tfail(err) {\r\n\t\t\t\t\t\t\terrs(err);\r\n\t\t\t\t\t\t\tif (!_this.showLongPressSaveTip) return\r\n\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\ttitle: '海报生成失败',\r\n\t\t\t\t\t\t\t\tmask: false,\r\n\t\t\t\t\t\t\t\tduration: 2000,\r\n\t\t\t\t\t\t\t\ticon: \"none\"\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\tuni.hideLoading()\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t});\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tgetNewImage() {\r\n\t\t\t\tuni.canvasToTempFilePath({\r\n\t\t\t\t\t\tcanvasId: _this.CanvasID,\r\n\t\t\t\t\t\tquality: 1, // 最高质量\r\n\t\t\t\t\t\tfileType: 'png', // 使用PNG格式，更适合二维码\r\n\t\t\t\t\t\tdestWidth: _this.canvasW * 2, // 输出图片宽度为canvas宽度的2倍\r\n\t\t\t\t\t\tdestHeight: _this.canvasH * 2, // 输出图片高度为canvas高度的2倍\r\n\t\t\t\t\t\tcomplete: res => {\r\n\t\t\t\t\t\t\t_this.tempFilePath = res.tempFilePath;\r\n\t\t\t\t\t\t\t_this.$emit('success', res);\r\n\t\t\t\t\t\t\t_this.loading = false;\r\n\t\t\t\t\t\t\tif (!_this.showLongPressSaveTip) return\r\n\t\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\t\ttitle: '长按图片保存海报',\r\n\t\t\t\t\t\t\t\tmask: false,\r\n\t\t\t\t\t\t\t\tduration: 2000,\r\n\t\t\t\t\t\t\t\ticon: \"none\"\r\n\t\t\t\t\t\t\t});\r\n\t\t\t\t\t\t\tuni.hideLoading();\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t},\r\n\t\t\t\t\tthis\r\n\t\t\t\t);\r\n\t\t\t}\r\n\t\t},\r\n\t\tmounted() {\r\n\t\t\t_this = this;\r\n\t\t\tthis.OnCanvas();\r\n\t\t}\r\n\t};\r\n</script>\r\n\r\n<style></style>\r\n"], "sourceRoot": ""}